{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^7.4|^8.0", "alexusmai/laravel-file-manager": "^2.5", "barryvdh/laravel-debugbar": "^3.3", "barryvdh/laravel-elfinder": "^0.4.7", "barryvdh/laravel-snappy": "^0.4.7", "beyondcode/laravel-websockets": "^1.6", "cviebrock/eloquent-sluggable": "^8.0", "doctrine/dbal": "^3.3", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "h4cc/wkhtmltopdf-amd64": "^0.12.4", "idma/robokassa": "dev-master", "itsgoingd/clockwork": "^5.1", "jenssegers/date": "^4.0", "jumbojett/openid-connect-php": "^0.9.5", "kalnoy/nestedset": "^6.0", "kwn/number-to-words": "^1.10", "laravel/framework": "8.75", "laravel/tinker": "^2.0", "laravel/ui": "^3.4", "laravelrus/sleepingowl": "dev-development", "league/flysystem-sftp": "^1.0", "livewire/livewire": "^2.3", "mnvx/lowrapper": "^1.0", "mpdf/mpdf": "^8.0", "owen-it/laravel-auditing": "^13.0", "phpoffice/phpspreadsheet": "^1.17", "phpoffice/phpword": "^0.17.0", "rtconner/laravel-tagging": "^4.0", "santigarcor/laratrust": "^7.1", "spatie/laravel-medialibrary": "^8.0.0", "spatie/laravel-rate-limited-job-middleware": "^1.5", "vinkla/hashids": "^9.1.0"}, "require-dev": {"facade/ignition": "^2.3.6", "fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform": {"php": "7.4"}}, "autoload": {"psr-4": {"App\\": "app/", "Admin\\": "admin/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": ["*"]}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"]}}