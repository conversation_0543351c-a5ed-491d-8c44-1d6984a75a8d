{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --disable-host-check --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"@fortawesome/fontawesome-free": "^5.11.2", "axios": "^0.19", "bootstrap": "^4.0.0", "bootstrap-vue": "^2.15.0", "cross-env": "^7.0", "jquery": "^3.5.1", "laravel-mix": "^5.0.1", "lodash": "^4.17.13", "popper.js": "^1.12", "resolve-url-loader": "^2.3.1", "sass": "^1.63.6", "sass-loader": "^10.1.1", "socket.io-client": "^2.3.0", "uiv": "^0.31.5", "vue": "^2.6.14", "vue-router": "^3.1.3", "vue-sidebar-menu": "^4.2.3", "vue-socket.io-extended": "^4.2.0", "vue-template-compiler": "^2.6.10", "vuedraggable": "^2.24.3"}, "dependencies": {"laravel-echo": "^1.8.1", "moment": "^2.29.1", "pusher-js": "^7.0.0"}}