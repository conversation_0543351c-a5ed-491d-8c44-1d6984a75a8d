@if ( ! empty($title))
	<div class="row">
		<div class="col-lg-12 pt-3">
			{!! $title !!}
		</div>
	</div>
	<br />
@endif

@yield('before.card')

<div class="card card-default mb-0 {!! $card_class !!}">
	@push('navbar.buttons')
		@if ($creatable)
			<a href="{{ url($createUrl) }}" class="btn btn btn-primary mr-2">
				<i class="fas fa-plus"></i> {{ $newEntryButtonText }}
			</a>
		@endif
	@endpush

	@push('panel.filters')
		@yield('card.buttons')

		@yield('card.heading.actions')
	@endpush

	@foreach($extensions as $ext)
		{!! $ext->render() !!}
	@endforeach

	@yield('card.footer')
</div>

@yield('after.card')
