@yield('before.card')

@push('navbar.buttons')
    @if ($creatable)
        <a href="{{ url($createUrl) }}" class="btn btn btn-primary mr-2">
            <i class="fas fa-plus"></i> {{ $newEntryButtonText }}
        </a>
    @endif
@endpush

@push('panel.filters')
    @yield('card.buttons')

    @yield('card.heading.actions')
@endpush

@foreach($items as $entry)
    <div class="card card-widget meeting-item">
        <div class="card-header d-flex align-items-center">
            <div class="user-block">
                <img class="img-circle" src="{{ $entry->user->avatar_url_or_blank }}" alt="">
                <span class="username">{{ $entry->user->fio }}</span>
                <span class="description">{{ $entry->date->format('d.m.Y') }}</span>
            </div>
            <h3 class="card-title ml-3">{{ $entry->title ?? '' }}</h3>
            <div class="ml-auto card-tools">
                @foreach ($controls as $control)
                    @php
                        if($control instanceof \SleepingOwl\Admin\Contracts\Display\ColumnInterface) {
                            $control->setModel($entry);
                            $control->initialize();
                        }
                    @endphp
                    {!! $control->render() !!}
                @endforeach
            </div>
        </div>
        <div class="card-body">
            @if (is_callable($value))
                {!! $value($entry) !!}
            @else
                {{ $entry->{$value} }}
            @endif
        </div>

        @if($comments)
            <div class="card-footer">
                {!! AdminFormElement::view("admin::comment", ['id' => $entry->id, 'model_type' => get_class($entry), 'type' => 0]) !!}
            </div>
        @endif
    </div>
@endforeach

@if(!is_null($pagination))
    <div class="panel-footer">
        {!! $pagination !!}
    </div>
@endif

@yield('after.card')

