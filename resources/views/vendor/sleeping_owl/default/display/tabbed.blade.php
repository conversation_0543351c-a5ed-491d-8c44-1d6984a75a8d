<div class="card card-secondary card-outline card-outline-tabs">
    <div class="card-header p-0">
        <div class="nav nav-tabs" id="nav-tab" role="tablist">
            @foreach ($tabs as $tab)
                {!! $tab->render() !!}
            @endforeach
        </div>
    </div>
    <div class="card-body">
        <div class="tab-content {!! $classAttributes !!}" id="nav-tabContent">
            @foreach ($tabs as $tab)
                <div class="tab-pane fade {!! ($tab->isActive()) ? 'show active' : '' !!}" id="nav-{{ $tab->getName() }}" role="tabpanel" aria-labelledby="nav-{{ $tab->getName() }}">
                    {!! str_replace('id="sleeping_owl_tab_id"','',$tab->addTabElement()->getContent()->render()) !!}
                </div>
            @endforeach
        </div>
    </div>
</div>
