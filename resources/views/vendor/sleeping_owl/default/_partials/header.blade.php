<a class="nav-link mx-0 mx-lg-4" data-widget="pushmenu" href="#" role="button">
	<svg class="feather">
		<use xlink:href="/fonts/feather-sprite.svg#menu"/>
	</svg>
</a>

<a class="nav-link mr-auto d-lg-none" href="#" role="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-expanded="false">
	<i class="fas fa-th-large"></i>
</a>

<ul class="navbar-nav order-lg-last flex-row">
	<li class="nav-item">
		<a class="nav-link" data-widget="control-sidebar" data-controlsidebar-slide="false" data-scrollbar-auto-hide="true" data-slide="true" href="#" role="button">
			<svg class="feather feather-solid">
				<use xlink:href="/fonts/feather-sprite.svg#filter"/>
			</svg>
		</a>
	</li>

	@stack('navbar.right')
</ul>

<div class="collapse navbar-collapse" id="navbarSupportedContent">
	<ul class="nav navbar-nav align-items-center">
		@stack('navbar.left')

		@stack('navbar')
	</ul>

	<div class="nav navbar-nav align-items-center">
		@stack('navbar.buttons')

		@if(in_array(Route::currentRouteName(),['school','rubric','page']) && (auth()->user()->isAdmin() || auth()->id() == 171))
			<a href="/school_rubrics/create" class="btn btn btn-primary mr-2"><i class="fas fa-plus"></i> Добавить рубрику</a>
			<a href="/school_pages/create" class="btn btn btn-primary mr-2"><i class="fas fa-plus"></i> Добавить страницу</a>
		@endif
		@if(in_array(Route::currentRouteName(),['rd.school','rd.rubric','rd.page']) && (auth()->user()->isAdmin() || auth()->id() == 171))
			<a href="/school_rubricrds/create" class="btn btn btn-primaryrd mr-2"><i class="fas fa-plus"></i> Добавить рубрику</a>
			<a href="/school_pagerds/create" class="btn btn btn-primaryrd mr-2"><i class="fas fa-plus"></i> Добавить страницу</a>
		@endif

		@if(Route::currentRouteName() === 'admin.dictionary' && (auth()->user()->isAdmin() || auth()->id() == 171))
			<a href="/dictionaries/create" class="btn btn btn-primary mr-2"><i class="fas fa-plus"></i> Новая запись</a>
		@else
			<a role="button" href="/dictionary" class="btn btn-outline-secondary mr-2">Словарик</a>
		@endif

		@if(in_array(Route::currentRouteName(),['school','rubric','page']) && (auth()->user()->isAdmin() || auth()->id() == 171))
		@else
			<a role="button" href="/school" class="btn btn-outline-primary mr-2">Школа AM24</a>
		@endif

		@if(in_array(Route::currentRouteName(),['rd.school','rd.rubric','rd.page']) && (auth()->user()->isAdmin() || auth()->id() == 171))
		@else
			<a role="button" href="/schoolrd" class="btn btn-outline-primaryrd mr-2">Школа RD</a>
		@endif

		@if(Route::currentRouteName() != 'admin.dashboard')
			<a role="button" href="/" class="btn btn-outline-primary mr-2">Новости</a>
		@endif
		@if(Route::currentRouteName() != 'employees-all')
			<a role="button" href="/employees_all" class="btn btn-outline-primary mr-2">Сотрудники</a>
		@endif
		@if(Route::currentRouteName() != 'admin.work_days_index')
			<a role="button" href="/work_days/index" class="btn btn-outline-primary mr-2">Календарь</a>
		@endif

		<a role="button" target="_blank" href="https://vk.com/alfamart24" class="mr-2"><i class="fab fa-vk fa-2x text-primary" aria-hidden="true"></i></a>
		<a role="button" target="_blank" href="https://www.youtube.com/channel/UCVfDGAFrlb-VR44dQKl9kJg/videos" class="mr-2"><i class="fab fa-youtube fa-2x text-danger"></i></a>
	</div>

	@stack('navbar.buttons.after')
</div>
