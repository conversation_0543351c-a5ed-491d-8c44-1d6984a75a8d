@if($value)
    <div {!! $attributes !!} class="position-relative">
        @if ($visibled)
            @if(!$isReadonly)
                <a href="#"
                   style="width: 100%; height: 100%"
                   class="inline-editable ec-{{ $id }}"
                   data-name="{{ $name }}"
                   data-value="{{ $value }}"
                   data-url="{{ $url }}"
                   data-type="text"
                   data-pk="{{ $id }}"
                   data-mode="{{ $mode }}"
                   data-emptytext="{{ trans('sleeping_owl::lang.select.empty') }}"
                    {{ $isReadonly ? 'data-disabled' : '' }}
                >
                    <i class="fa fa-lg fa-comment-dots" style="color: #0b0b0b; z-index: 100"></i>
                    {{--                🗨--}}
                    ️</a>
                <script>
                    var content = '<i class="fa fa-lg fa-comment-dots" style="color: #0b0b0b; z-index: 100"></i>';
                    const element_{{$id}} = document.querySelector('.ec-{{$id}}');

                    if (element_{{$id}}) { // Проверяем, существует ли элемент в DOM
                        {{--console.log(element_{{$id}});--}}
                        const observer_{{$id}} = new MutationObserver(() => {
                            // Отключаем наблюдателя перед внесением изменений
                            observer_{{$id}}.disconnect();

                            // Восстанавливаем содержимое элемента
                            element_{{$id}}.innerHTML = content;

                            // Включаем наблюдателя обратно
                            observer_{{$id}}.observe(element_{{$id}}, { childList: true, subtree: true });
                        });

                        // Инициализируем наблюдателя
                        observer_{{$id}}.observe(element_{{$id}}, { childList: true, subtree: true });

                        // Устанавливаем оригинальное содержимое
                        element_{{$id}}.innerHTML = content;
                    } else {
                        console.warn('Element .ec-{{$id}} not found');
                    }
                </script>
            @else
                <i class="fa fa-lg fa-comment-dots"></i>
            @endif
            @if($small)
                <small class="clearfix"><i class="fa fa-sm fa-comment-dots"></i></small>
            @endif
        @endif
    </div>
@endif


