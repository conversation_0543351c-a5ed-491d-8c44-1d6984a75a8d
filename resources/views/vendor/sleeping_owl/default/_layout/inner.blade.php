@extends(AdminTemplate::getViewPath('_layout.base'))

@section('content')
    @if(isset($_GET['clean_view']))
        <div class="wrapper container-fluid" id="vueApp">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <h5 class="py-3">{!! $template->renderBreadcrumbs($breadcrumbKey) !!}</h5>
                </div>
            </div>

            @stack('content.top')

            {!! $content !!}
        </div>
    @else
	<div class="wrapper" id="vueApp">
{{--		<nav class="main-header navbar navbar-expand-lg bg-white navbar-light border-bottom px-0 px-md-4">--}}
		<nav class="main-header navbar navbar-expand-lg navbar-white navbar-light">
			@include(AdminTemplate::getViewPath('_partials.header'))
		</nav>

		<aside class="main-sidebar sidebar-light-primary">
			@include(AdminTemplate::getViewPath('_partials.navigation'))
		</aside>

		<div class="content-wrapper px-0 px-md-4">
			<div class="row align-items-center">
				<div class="col-md-12">
					{!! $template->renderBreadcrumbs($breadcrumbKey) !!}
				</div>
			</div>

			@if(auth()->user()->ext)
				<mango-toast :ext="{!! auth()->user()->ext !!}"></mango-toast>
			@endif

			<div class="content-header pt-0">
				@stack('content.header')

				@stack('content.header.after')
			</div>


			<div class="content body">
				@stack('content.top')

				{!! $content !!}

				@stack('content.bottom')
			</div>
		</div>

		<aside class="control-sidebar control-sidebar-light">
			<div class="p-3 border-bottom bg-light">
				@stack('panel.filters.before')
			</div>

			<div class="p-3">
				@stack('panel.filters')

				@yield('panel.filters')
			</div>
		</aside>
	</div>
    @endif
@stop
