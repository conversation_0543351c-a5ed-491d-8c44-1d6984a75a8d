<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
	{!! $template->renderMeta($title) !!}
	@if(null !== ($favicon = config('sleeping_owl.favicon')))
		<link rel="icon" href="{{ $favicon }}">
	@endif

	<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
	<!--[if lt IE 9]>
		<script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
		<script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
	<![endif]-->

	@if (Auth::user())
		<script>
            window.user = {"name":"{{ Auth::user()->fio }}", "email":"{{ Auth::user()->email }}","id":"{{ Auth::user()->id }}","is_admin": "{{ Auth::user()->isAdmin() }}","is_topsales": "{{ Auth::user()->hasRole('topsales') }}","is_subdostavka": "{{ Auth::user()->hasRole('subdostavka') }}", "ext":"{{ Auth::user()->ext }}", "mango_port": {{ (config('franchise.key') == 'ekb' || in_array(Auth::user()->ext,[28,31,12,35,16,23,11,13,32])) ? 9877 : 9876 }} };
		</script>
	@endif
	@stack('scripts')
</head>
<body class="{{ config('sleeping_owl.body_default_class', 'sidebar-mini sidebar-open') . (@$_COOKIE['sidebar-state'] == 'sidebar-collapse' ? ' sidebar-collapse' : '') }}">
	@yield('content')

    @if(!isset($_GET['clean_view']))

        @include('admin::leads.instruction')

	    @include(AdminTemplate::getViewPath('helper.scrolltotop'))
@php
    $template_scripts = $template->meta()->renderScripts(true);
    $manifest = json_decode(file_get_contents(public_path('packages/sleepingowl/default/mix-manifest.json')), true);
    $template_scripts = str_replace(array_keys($manifest), array_values($manifest), $template_scripts);
@endphp
{{--	    {!! $template->meta()->renderScripts(true) !!}--}}
        {!! $template_scripts !!}

	    @include(AdminTemplate::getViewPath('helper.autoupdate'))
    @else
        <script src="{{ mix('js/admin-app.js','packages/sleepingowl/default') }}"></script>
        <script src="{{ asset('js/common.js?v=2') }}"></script>
{{--        <script src="{{ asset('js/lib/jquery.autocomplete.min.js') }}"></script>--}}
        <script src="{{ mix('js/components_clean.js') }}"></script>
{{--        <script src="{{ asset('js/app.js') }}"></script>--}}
        <script src="{{ asset('js/lib/jquery.cookie.min.js') }}"></script>
{{--        <script src="{{ asset('js/crm.js') }}"></script>--}}
        <script src="{{ mix('js/vue.js','packages/sleepingowl/default') }}"></script>
        <script src="{{ mix('js/modules.js','packages/sleepingowl/default') }}"></script>
    @endif

	@stack('footer-scripts')
</body>
</html>
