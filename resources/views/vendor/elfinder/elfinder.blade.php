<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
    <head>
        <meta charset="utf-8">
        <title>elFinder 2.0</title>

        <!-- jQuery and jQuery UI (REQUIRED) -->
        <link rel="stylesheet" type="text/css" href="//cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
        <script src="//ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
        <script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/jquery-ui.min.js"></script>

        @php
            $dir = '/js/libs/elfinder';
            $id = $type = null;
            if (request()->has('lead_id')) { $id = request()->get('lead_id'); $type = 'leads'; }
            elseif (request()->has('supplier_id')) { $id = request()->get('supplier_id'); $type = 'suppliers'; }
            elseif (request()->has('candidate_id')) { $id = request()->get('candidate_id'); $type = 'candidates'; }
            elseif (request()->has('franchise_id')) { $id = request()->get('franchise_id'); $type = 'franchises'; }
            elseif (request()->has('user_id')) { $id = request()->get('user_id'); $type = 'users'; }
        @endphp

        <!-- elFinder CSS (REQUIRED) -->
        <link rel="stylesheet" type="text/css" href="{{ asset($dir.'/css/elfinder.min.css') }}">
        <link rel="stylesheet" type="text/css" href="{{ asset($dir.'/css/theme.css') }}">

        <!-- elFinder JS (REQUIRED) -->
        <script src="{{ asset($dir.'/js/elfinder.min.js') }}"></script>

        @if($locale)
            <!-- elFinder translation (OPTIONAL) -->
            <script src="{{ asset($dir."/js/i18n/elfinder.$locale.js") }}"></script>
        @endif

        <!-- Extra contents editors (OPTIONAL) -->
        <script src="{{ asset($dir.'/js/extras/editors.default.min.js') }}"></script>
        <script src="{{ asset($dir.'/js/extras/quicklook.googledocs.min.js') }}"></script>

        <!-- elFinder initialization (REQUIRED) -->
        <script type="text/javascript" charset="utf-8">
            // Documentation for client options:
            // https://github.com/Studio-42/elFinder/wiki/Client-configuration-options
            function getUrlParam(paramName) {
                var reParam = new RegExp('(?:[\?&]|&amp;)' + paramName + '=([^&]+)', 'i') ;
                var match = window.location.search.match(reParam) ;

                return (match && match.length > 1) ? match[1] : '' ;
            }

            $().ready(function() {
                let funcNum = getUrlParam('CKEditorFuncNum');

                var elf = $('#elfinder').elfinder({
                    // set your elFinder options here
                    @if($locale)
                        lang: '{{ $locale }}', // locale
                    @endif
                    customData: {
                        _token: '{{ csrf_token() }}',
                        @if(!empty($id))
                            id: '{{ $id }}',
                            type: '{{ $type }}'
                        @endif
                    },
                    @if(!empty($id))
                        startPathHash : 'l1_' + btoa('{{ $id }}').replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '.').replace(/\.+$/, ''),
                    @else
                        startPathHash : 'fls1_Lw',
                    @endif
                    rememberLastDir : false,
                    url : '{{ route("elfinder.connector") }}',  // connector URL
                    soundPath: '{{ asset($dir.'/sounds') }}',
                    // getFileCallback : function(file) {
                    //     window.opener.CKEDITOR.tools.callFunction(funcNum, file.url);
                    //     window.close();
                    // }
                    commandsOptions : {
                        // getfile: {
                        //     // send only URL or URL+path if false
                        //     onlyURL: false,
                        //
                        //     // allow to return multiple files info
                        //     multiple: false,
                        //
                        //     // allow to return folders info
                        //     folders: false,
                        //
                        //     // action after callback (close/destroy)
                        //     oncomplete: ''
                        // },
                        // edit : {
                        //     extraOptions : {
                        //         uploadOpts : {
                        //             dropEvt: {shiftKey: true, ctrlKey: true}
                        //         },
                        //         pixo: {
                        //             apikey: '359qg6hf7t40'
                        //         },
                        //         creativeCloudApiKey : '6e62687b643a413cbb6aedf72ced95e3',
                        //         managerUrl : 'manager.html',
                        //         tinymce : {
                        //             imagetools_cors_hosts: ['hypweb.net']
                        //         }
                        //     }
                        // },
                        quicklook : {
                            // googleMapsApiKey : 'AIzaSyAmQiMcWI1e0QryaAHuGNblqJ9xRE2NXL8',
                            // sharecadMimes : ['image/vnd.dwg', 'image/vnd.dxf', 'model/vnd.dwf', 'application/vnd.hp-hpgl', 'application/plt', 'application/step', 'model/iges', 'application/vnd.ms-pki.stl', 'application/sat', 'image/cgm', 'application/x-msmetafile'],
                            googleDocsMimes : ['application/pdf', 'image/tiff', 'application/vnd.ms-office', 'application/msword', 'application/vnd.ms-word', 'application/vnd.ms-excel', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/postscript', 'application/rtf'],
                            officeOnlineMimes : ['application/msword', 'application/vnd.ms-word', 'application/vnd.ms-excel', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/vnd.oasis.opendocument.text', 'application/vnd.oasis.opendocument.spreadsheet', 'application/vnd.oasis.opendocument.presentation'],
                            width : 640,  // Set default width/height voor quicklook
                            height : 480
                        },
                    },
                    handlers : {
                        dblclick : function(event, elfinderInstance) {
                            event.preventDefault();
                            elfinderInstance.exec('getfile')
                                .done(function() { elfinderInstance.exec('quicklook'); })
                                .fail(function() { elfinderInstance.exec('open'); });
                        }
                    },

                    getFileCallback : function(files, fm) {
                        return false;
                    },


                }).elfinder('instance')

                @if(request()->has('supplier_id'))
                elf.bind('upload', function(event) {
                    //console.log(event.data.added);
                    $.ajax({
                        url: 'api/suppliers/{{ request('supplier_id') }}/add_file',
                        type: 'get',
                        data: {
                            files: event.data.added,
                        },
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('X-CSRF-TOKEN', $('meta[name="csrf-token"]').attr('content'));
                        }
                    }).done(function (data) {
                    });
                });
                @endif
                elf.exec('fullscreen')

            });
        </script>
    </head>
    <body>
        <!-- Element where elFinder will be created (REQUIRED) -->
        <div id="elfinder"></div>

    </body>
</html>
