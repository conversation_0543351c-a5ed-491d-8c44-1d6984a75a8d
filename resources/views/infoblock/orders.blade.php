@if(count($html) > 0)
    <div class="row">
        @foreach($html as $k => $counts)
            <div class="col-12 col-sm-6 col-md">
                @if($counts)
                    <div class="card card-outline card-{{ $counts['class'] }}">
                        @if($k != '')
                            <div class="card-header">
                                <h3 class="card-title"><b>{{ $k }}</b> @isset($counts['helper']) <x-helper color="gray" name="{{ $counts['helper'] }}" /> @endisset</h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse"><i class="fa fa-minus"></i></button>
                                </div>
                            </div>
                        @endif
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                {!! $counts['items'] !!}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        @endforeach
    </div>
    @if(!empty($is_leads))
        <div>
            <a role="button" onclick="$(`select[data-id=importance]`).val(`0`).trigger(`change`).trigger(`select2:select`)" class="btn btn-default">🔥 ВАЖНЫЕ</a>
            <a role="button" onclick="$(`select[data-id=importance]`).val(`1`).trigger(`change`).trigger(`select2:select`)" class="btn btn-default"><i class="text-red fa fa-lg fa-circle-exclamation"></i> Замечания</a>
            <a role="button" onclick="$(`#filters-cancel`).click()" class="btn btn-default"><i class="fas fa-times text-danger"></i> Очистить</a>
        </div>
    @endif
@endif