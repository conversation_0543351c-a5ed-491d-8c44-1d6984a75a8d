<div class="row">
    @if(count($html) > 0)
        @foreach($html as $k => $counts)
            <div class="col-12 col-sm-6 col-md">
                @if($counts)
                    <div class="card card-outline card-{{ $counts['class'] }}">
                        @if($k != '')
                            <div class="card-header">
                                <h3 class="card-title"><b>{{ $k }}</b></h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse"><i
                                            class="fa fa-minus"></i></button>
                                </div>
                            </div>
                        @endif
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                {!! $counts['items'] !!}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        @endforeach
        <div class="col-12 col-sm-6 col-md">
            <div class="small-box bg-danger">
                <div class="inner"><i class="fa fa-bus"></i><p>Не указана доставка:</p> <h3>{{  $not_specified }}</h3></div>
            </div>
        </div>
    @endif
</div>
