<hr>
@if($delivery)
	<div class="row mb-3">
		<div class="col-lg-2">
			<img class="img-fluid" src="https://alfamart24.ru{{$delivery['logo']}}" alt="{{$delivery['name']}}">
		</div>
		<div class="col-lg-10">
			<h2>Статус груза - {{ $delivery['order_number'] }}<small class="float-right">Текущий статус: <b>{{$delivery['status']['name']}}</b> ({{ \Carbon\Carbon::parse($delivery['status']['date'])->format('d.m.Y') }})</small></h2>
			<h3 class="mb-3">Транспортная компания "{{$delivery['name']}}"</h3>

			<div class="row mb-4">
				<div class="col-lg-6">
					<h4>Отправитель груза:</h4>
					{{ $delivery['sender'] }}
				</div>
				<div class="col-lg-6">
					<h4>Оплата до терминала ТК:</h4>
					{{ $delivery['delivery_terminal'] ??  '-'}}
				</div>
			</div>

			<div class="row mb-4">
				<div class="col-lg-6">
					<h4>Получатель груза</h4>
					<div class="row">
						<div class="col-lg-2">ФИО:</div>
						<div class="col-lg-10">{{ $delivery['recipient']['fio'] }}</div>
					</div>
					<div class="row">
						<div class="col-lg-2">Телефон:</div>
						<div class="col-lg-10">{{ $delivery['recipient']['phone'] }}</div>
					</div>
					<div class="row">
						<div class="col-lg-2">Адрес:</div>
						<div class="col-lg-10">{{ $delivery['recipient']['address'] }}</div>
					</div>
				</div>
				<div class="col-lg-6">
					<h4>Сведения о грузе</h4>
					<div class="row">
						<div class="col-lg-2">Мест:</div>
						<div class="col-lg-10">{{ $delivery['cargo']['places'] }}</div>
					</div>
					<div class="row">
						<div class="col-lg-2">Вес:</div>
						<div class="col-lg-10">{{ $delivery['cargo']['weight'] }} кг.</div>
					</div>
					<div class="row">
						<div class="col-lg-2">Объем:</div>
						<div class="col-lg-10">{{ $delivery['cargo']['volume'] }}</div>
					</div>
					@isset($delivery['cargo']['dimension'])
						<div class="row">
							<div class="col-lg-2">Габариты:</div>
							<div class="col-lg-10">{{ $delivery['cargo']['dimension'] }} м2</div>
						</div>
					@endisset

                    @if($delivery['name']=='E-KIT')
                        <div class="row mt-4">
                            <div class="col-lg-6">
                                <h4>Инфо о доставке:</h4>
                                {{ $delivery['info'] }}
                            </div>
                            <div class="col-lg-6">
                                <h4>Оплата получателем:</h4>
                                {{ $delivery['receiverpays'] ??  '-'}}
                            </div>
                        </div>
                    @endif
				</div>
			</div>
			{{--<div class="col-lg-12">--}}
			@if(!empty($delivery['nprice']))
			<h4>Стоимость товаров (НП): {{ $delivery['nprice'] }} руб.</h4>
			@endif
			<h4>Общая стоимость доставки: {{ $delivery['price'] }} руб.</h4>
			{{--</div>--}}
		</div>
	</div>
	@if($delivery['name']=='E-KIT' && !empty($delivery['cargo']) && !empty($delivery['cargo']['packages']))
		<table class="table">
			<tr>
				<th style="width: 150px;"></th>
				<th>Вес</th>
				<th>Объем</th>
				<th>Габариты ДхШхВ</th>
			</tr>
			<?php $vol_sum = 0; $mass_sum = 0; ?>
			@foreach($delivery['cargo']['packages'] as $index => $package)
				@continue($package['mass'] == 0)
				<?php
					$vol = $package['length'] * $package['width'] * $package['height'];
					$vol_sum += $vol;
					$mass_sum += $package['mass'];
				?>
				<tr>
					<td>{{$index+1}}</td>
					<td>{{$package['mass']}}</td>
					<td>{{round( $vol / 1000000, 2)}}</td>
					<td>{{(float)$package['length']}} x {{(float)$package['width']}} x {{(float)$package['height']}}</td>
				</tr>
			@endforeach
			<tr>
				<th>Итого:</th>
				<th>{{round( $mass_sum, 2)}}</th>
				<th>{{round( $vol_sum / 1000000, 2)}}</th>
				<th></th>
			</tr>
		</table>
	@endif
	<div class="h4">История статусов</div>
	@if(!in_array($delivery['name'],['GTD','E-KIT']))
		Временно отсутсвует.
	@else
		<div style="display:inline-block;max-width:80vw;overflow-y:auto;">
			<ul class="delivery-timeline">
				@foreach(array_reverse($delivery['statuses']) as $status)
					<li>
						<div class="text-nowrap">
							<time>{{ \Carbon\Carbon::parse($status['date'] .' '. $status['time'])->format('d.m.Y H:i') }}</time>
							<br>
							<h4><b>{{ $status['name'] }}</b></h4>
						</div>
					</li>
				@endforeach
			</ul>
		</div>
	@endif
@else
	<div class="row">
		<div class="col-lg-2">
			<img src="/img/mult/launchpad.png" alt="Зиг Заг" width="100">
		</div>
		<div class="col-lg-10">
			<h3>Внимание!</h3>
			<p>Не удалось получить от перевозчика данные по заказу. Проверь номер!</p>
		</div>
	</div>
@endif
