<div clss="row">
    <div class="col-12">
        @if(count($checkList))
        <form wire:submit.prevent="save">
            <div class="row">
                <div class="col-6">
                    <table class="table" width="350px">
                        <th colspan="2" class="text-center">
                            Утро
                        </th>
                        @foreach($checkList->where('subtype_id', 0) as $list)
                        <tr>
                            <td>{{ $list->item }}</td>
                            <td width="10%"><input wire:model="morning.{{ $list->id }}" type="checkbox"/></td>
                            <td>{{ array_key_exists($list->id, $morning) && $morning[$list->id] ? \Carbon\Carbon::parse($morning[$list->id])->setTimezone(config('app.timezone'))->format('H:i') : '-' }}</td>
                        </tr>
                        @endforeach
                    </table>
                </div>
                <div class="col-6">
                    <table class="table" width="350px">
                        <th colspan="2" class="text-center">
                            Вечер
                        </th>
                        @foreach($checkList->where('subtype_id', 1) as $list)
                            <tr>
                                <td>{{ $list->item }}</td>
                                <td width="10%"><input wire:model="evening.{{ $list->id }}" type="checkbox"/></td>
                                <td>{{ array_key_exists($list->id, $evening) && $evening[$list->id] ? \Carbon\Carbon::parse($evening[$list->id])->setTimezone(config('app.timezone'))->format('H:i') : '-' }}</td>
                            </tr>
                        @endforeach
                    </table>
                </div>
            </div>
{{--            <div class="row">--}}
{{--                <div class="col-12">--}}
{{--                    <label for="comment">Комментарий</label>--}}
{{--                    <textarea wire:model.debounce.1000ms="comment" name="comment" id="comment" class="col-12" cols="30" rows="4"></textarea>--}}
{{--                </div>--}}
{{--            </div>--}}
        </form>
        @endif
    </div>
</div>
