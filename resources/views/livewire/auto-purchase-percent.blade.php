<div>

    <form wire:submit.prevent="save">
        <div class="form-group">
            <label for="supplier">Поставщик</label>
            <div
                wire:ignore
                x-data="{ tomSelect: null }"
                x-init="
        tomSelect = new TomSelect($refs.select, {
            create: false,
            sortField: {
                field: 'text',
                direction: 'asc'
            },
            placeholder: 'Выберите поставщика',
            allowEmptyOption: true,
            items: [],
            persist: false,
            onChange: function(value) {
                $wire.set('selectedSupplier', value);
                @this.changeSupplier();
            }
        })
    "
            >
                <select
                    x-ref="select"
                    wire:model="selectedSupplier"
                    class="form-control"
                >
                    <option value="">Выберите поставщика</option>
                    @foreach($suppliers as $id=>$supplier)
                        <option value="{{ $id }}">{{ $supplier }}</option>
                    @endforeach
                </select>
            </div>

        </div>
        <div class="form-group">
            <label for="catalog-section">Раздел каталога</label>
            <select id="catalog-section" class="form-control" wire:model="selectedCatalog">
                <option value="0">Выберете раздел</option>
                @foreach($catalogs as $id=>$catalog)
                    <option value="{{ $id }}">{{ $catalog }} [{{ $id }}]</option>
                @endforeach
            </select>
        </div>
        <div class="form-group">
            <label for="percentage">Процент</label>
            <input wire:model="percent" type="number" id="percentage" class="form-control"
                   placeholder="Введите процент">
        </div>
        @if($selectedSupplier && $selectedCatalog && $percent)
            <button type="submit" class="btn btn-primary">Сохранить</button>
        @endif
    </form>

    @if(count($data))
        <hr>
        <div>
            {{--        Категория: {{ $category_name ?? ' --- ' }}--}}
            {{--        <br>--}}
            {{--        Поставщик: {{ $supplier_name ?? ' --- ' }}--}}
            {{--        <br>--}}
            Процент: {{ $percent ?? ' --- ' }}
        </div>

        @foreach($data as $d)
            <div>
                {{ $d['id'] }} - {{ $d['name'] }} - {{ $d['purchase_price'] }} ({{ $d['diff']}} )
                <s>{{ $d['old_price'] }}</s>
            </div>
        @endforeach
    @endif
</div>
