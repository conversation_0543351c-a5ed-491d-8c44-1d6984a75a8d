@php $key = config('franchise.key'); @endphp
<table width="100%">
    <tbody>
    <tr>
        <td>
            <img src="{{asset('img/doc_logo.jpg')}}" alt="Логотип Альфамарт24">
        </td>
    </tr>
    <tr>
        <td>
            &nbsp;
        </td>
    </tr>

    <tr>
        <td>
            <table class="pp border">
                <tbody>
                <tr>
                    <td rowspan="2" colspan="2">{{ config("alfamart.franchise.{$key}.recipient") }}</td>
                    <td class="border" style="width: 54px;" valign="bottom">БИК</td>
                    <td class="border" style="width: 218px;" valign="bottom">{{ config("alfamart.franchise.{$key}.bik") }}</td>
                </tr>
                <tr>
                    <td class="border" style="width: 54px;" rowspan="2" valign="bottom">Сч. №</td>
                    <td class="border" style="width: 218px;" rowspan="2" valign="bottom">{{ config("alfamart.franchise.{$key}.invoice") }}</td>
                </tr>
                <tr>
                    <td colspan="2">Банк получателя</td>
                </tr>
                <tr>
                    <td class="border">ИНН 6670313810</td>
                    <td class="border">КПП 667001001</td>
                    <td class="border text-center" style="width: 54px;" rowspan="3" valign="top">Сч. №</td>
                    <td class="border" style="width: 218px;" rowspan="3" valign="top">40702810416540019731</td>
                </tr>
                <tr>
                    <td colspan="2">Общество с ограниченной ответственностью "АЛЬФАМАРТ 24"</td>
                </tr>
                <tr>
                    <td colspan="2">Получатель</td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>

    <tr>
        <td>
            <h3>Счет на оплату № {{ $invoice }} от {{ $date }} г.</h3>
        </td>
    </tr>

    <tr>
        <td><hr style="border: #0b0b0b 1px solid; margin: 5px 0;"></td>
    </tr>

    <tr>
        <td>
            <table class="ii">
                <tbody>
                <tr>
                    <td valign="top">Поставщик: </td>
                    <td>
                        <b>{{ config("alfamart.franchise.{$key}.legal") }}</b>
                    </td>
                </tr>
                <tr>
                    <td valign="top">Покупатель: </td>
                    <td>
                        <b>{{ $order->bayer->fio }}, {{ $order->bayer->address }}, тел.:&nbsp;{{ $order->bayer->phone }}</b>
                    </td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>

    <tr>
        <td>&nbsp;</td>
    </tr>

    <tr>
        <td>
            <table class="pp" cellpadding="4">
                <tbody>
                <tr>
                    <td class="border text-center" width="20">№</td>
                    <td class="border text-center" width="340">Наименование<br>товара</td>
                    <td class="border text-center" width="60">Единица<br>изме-<br>рения</td>
                    <td class="border text-center" width="50">Коли-<br>чество</td>
                    <td class="border text-center" width="120">Цена</td>
                    <td class="border text-center" width="120">Сумма</td>
                </tr>
                <?php
                $i=0;
                ?>
                @foreach($cart as $item)
                    <?php $i = $i + 1?>
                <tr>
                    <td class="border text-right" valign="top">{{ $i }}</td>
                    <td class="border">{{ $item->name }}</td>
                    <td class="border text-center" valign="bottom"></td>
                    <td class="border text-right" valign="bottom">{{ $item->quantity }}</td>
                    <td class="border text-right" valign="bottom">{{number_format((int) $item->price, 2, ',', ' ')}}</td>
                    <td class="border text-right" valign="bottom">{{number_format((int) $item->price * $item->quantity, 2, ',', ' ')}}</td>
                </tr>
                @endforeach

                <tr>
                    <td class="text-right" colspan="5"><b>Итого:</b></td>
                    <td class="border text-right"><b> {{number_format((int) $total_sum, 2, ',', ' ')}} </b></td>
                </tr>
                <tr>
                    <td class="text-right" colspan="5"><b>В том числе НДС:</b></td>
                    {{--<td class="border text-right"><b>{{number_format((int) $total_sum * 0.18, 2, ',', ' ')}}</b></td>--}}
                    @if($order->nds)
                    <td class="border text-right"><b>{{number_format((int) ($total_sum * (1-100/118)), 2, ',', ' ')}}</b></td>
                    @else
                    <td class="border text-right"><b> Без НДС </b></td>
                    @endif
                </tr>
                </tbody>
            </table>
        </td>
    </tr>

    <tr>
        <td>&nbsp;</td>
    </tr>

    <tr>
        <td>
            Всего наименований {{ $cart->count() }}, на сумму {{number_format((int) $total_sum, 2, ',', ' ')}} руб.<br>
            <b>{{ ucfirst(digit_text($total_sum, 'ru', true)) }}</b>
        </td>
    </tr>

    {{--<tr>--}}
        {{--<td>&nbsp;</td>--}}
    {{--</tr>--}}
    {{--<tr>--}}
        {{--<td>&nbsp;</td>--}}
    {{--</tr>--}}


    <tr>
        <td>
            <table width="100%">
                <tr>
                    <td>
                        <img src="{{asset('img/press.png')}}" height="218" alt="Печать">
                    </td>
                    <td align="right" valign="middle">Бухгалтер____________________________</td>
                </tr>
            </table>
        </td>
    </tr>

    </tbody>
</table>
<style type="text/css">
    body {
        padding-top: 20px;
        padding-bottom: 40px;
    }
    /* Custom container */
    .container {
        margin: 0 auto;
        width:772px;
    }
    .container-narrow > hr {
        margin: 30px 0;
    }
    .border {
        border: 1px solid #000;
    }
    .border-top {
        border-top: 1px solid #000;
    }
    .pp {
        width:100%;
    }
    .pp td {
        padding: 3px;
    }

    .ii td {
        padding: 3px;
    }
</style>