@php $key = config('franchise.key'); @endphp
<p>Добрый день!</p>
<p>Просьба выставить счет и сориентировать по наличию/готовности (если нужна предоплата за заказной товар, то процент предоплаты) <span style="background-color: #01FF70">ОТВЕТНЫМ ПИСЬМОМ</span>.</p>
<p style="text-decoration: underline">Перед выставлением счета просьба внимательно проверить и написать, если что-то будет отличаться:</p>

<ol>
    <li>Название</li>
    <li>Размер</li>
    <li>Цвет</li>
    <li>Кол-во</li>
    <li>Ед. измерения</li>
</ol>

<h4>Также в ответном письме укажите, пожалуйста, какой дополнительный/сопутствующий товар можно предложить к заказу клиенту. Напишите его характеристики и стоимость.</h4>

@if(count($cart))
    <?php $i = 0; ?>
<table style="width: 100%; border-collapse: collapse; font-size: 13px;">
    <tr>
        <th style="border: 1px solid #ddd; padding: 6px;">№</th>
        <th style="border: 1px solid #ddd; padding: 6px;">Наименование, характеристика</th>
        <th style="border: 1px solid #ddd; padding: 6px;">Кол-во</th>
        <th style="border: 1px solid #ddd; padding: 6px;">Ед.</th>
    </tr>
    @foreach($cart as $item)
    <tr>
        <td style='border: 1px solid #ddd; padding: 6px;'>{{ ++$i }}</td>
        <td style='border: 1px solid #ddd; padding: 6px;'>{{ $item->name }}</td>
        <td style='border: 1px solid #ddd; padding: 6px;'>{{ $item->quantity }}</td>
        <td style='border: 1px solid #ddd; padding: 6px;'>{{ ($item->unit) ? \App\Models\Cart::UNITS[$item->unit] : 'шт' }}</td>
    </tr>
    @endforeach
    </table>
@endif
@if($order->comment)
<p>Комментарий:&nbsp;{{ $order->comment }}</p>
@endif
<p>&nbsp;</p>
<p>Спасибо, ждем счет!</p>

<p>С уважением, интернет-магазин Alfamart24.ru!<br>
По всем вопросам звонить на {{ config("alfamart.franchise.{$key}.phone_supplier") }}</p>