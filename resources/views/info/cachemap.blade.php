<div class="container">
    <table class="table table-hover table-condensed">
        <thead>
            <tr>
                <th colspan="2">Ключ</th>
                <th style="text-align: center">Время</th>
                <th style="text-align: right">Действие</th>
            </tr>
        </thead>
        @foreach($cache_keys as $k=>$group)
            <tbody>
                <tr class="parent">
                    <td>
                    @if(count($group) <= 100)
                        <i class="fa fa-chevron-down"></i>
                    @endif
                    </td>
                    <td>{{ !empty($k) ? $k . '*' : 'Другое' }} ({{ count($group) }})</td>
                    <td style="text-align: center"></td>
                    <td style="text-align: right">
                    @if(!empty($k))
                        <a class="btn btn-xs btn-danger" target="_blank" href="/cache/{{ $k }}/del">очистить</a></td>
                    @endif
                </tr>
                @if(count($group) <= 100)
                    @foreach($group as $key)
                        <tr class="cchild">
                            <td colspan="2">{{ str_replace($k,'',$key['key']) }}</td>
                            <td style="text-align: center">{{ $key['time'] }}</td>
                            <td style="text-align: right"><a class="btn btn-xs btn-warning" target="_blank" href="/cache/{{ $key['key'] }}/view">показать</a> <a class="btn btn-xs btn-danger" target="_blank" href="/cache/{{ $key['key'] }}/del">очистить</a></td>
                        </tr>
                    @endforeach
                @endif
            </tbody>
        @endforeach
    </table>
</div>

<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap-theme.min.css" integrity="sha384-rHyoN1iRsVXV4nD0JutlnGaslCJuC7uwjduW9SVrLvRYooPp2bWYgmgJQIXwl/Sp" crossorigin="anonymous">
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" />
<style>
.parent ~ .cchild {
  display: none;
}
.open .parent ~ .cchild {
  display: table-row;
}
.parent {
  cursor: pointer;
}
tbody {
  color: #212121;
}
.open {
  background-color: #e6e6e6;
}

.open .cchild {
  background-color: #999;
  color: white;
}
.parent > *:last-child {
  width: 30px;
}
.parent i {
  transform: rotate(0deg);
  transition: transform .3s cubic-bezier(.4,0,.2,1);
  margin: -.5rem;
  padding: .5rem;

}
.open .parent i {
  transform: rotate(180deg)
}
.table-hover>tbody>tr.cchild:hover {
    background-color: #3e3e3e;
}
</style>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
<script>
$('table').on('click', 'tr.parent', function(){
  $(this).closest('tbody').toggleClass('open');
});
</script>