<div class="mb-3">
    {!! Breadcrumbs::render('page', $rubric, $page) !!}
</div>
{{--<div class="text-right"><a class="btn btn-default btn-light" href="{{URL::current().'/pdf'}}">PDF</a></div>--}}

<div class="card card-widget meeting-item mt-3" style="font-family: Mulish,serif; color: #061A32;">
    <div class="card-header">
        <div class="user-block">
            <span class="username ml-0" style="font-weight: 900;font-size: 2em;line-height: 35px;">{{ $page->title }}</span>
            @if(!empty($page->anons))
                <div>{{ $page->anons }}</div>
            @endif
        </div>
        @if(auth()->user()->isSuperAdmin() || auth()->user()->hasRole(['topsales','seniorsales','subdostavka']) || auth()->user()->hasRole('personal'))
        <div class="card-tools">
            <a class="btn btn-primary btn-xs" target="_blank" href="/school_pages/{{$page->id.'/edit'}}">Редактировать</a>
        </div>
        @endif
    </div>
    <div class="card-body" style="font-weight: 600;font-size: 18px;line-height: 23px">
        {!! $page->body !!}
    </div>
    @if($page->files)
        <div class="card-footer">
            <div class="list-group list-group-flush">
                @foreach($page->files as $file)
                    <a class="list-group-item list-group-item-action text-secondary" target="_blank" href="{{ $file['url'] }}" title="{{ $file['desc'] }}"><i class="fa fa-file-alt"></i> {{ !empty($file['title']) ? $file['title'] : $file['orig'] }}</a>
                @endforeach
            </div>
        </div>
    @endif
    <div class="card-footer">
        @if($page->users)
            <div class="d-flex mb-3">
                @foreach($page->users as $u)
                    <div style="margin-right: 5px;height: 30px; width: 30px; overflow: hidden; border-radius: 50%;" tabindex="0" data-toggle="popover" data-placement="top" data-trigger="hover" data-content="{{ $u['name'] }}">
                        <img style="width: 100%; height: auto;min-width: 100%; min-height: 100%;" src="{{ $u['image'] }}" alt="{{ $u['name'] }}"/>
                    </div>
                @endforeach
            </div>
        @endif
        @if(empty($page->views) || !in_array(auth()->id(), $page->views))
            <div class="btn btn-secondary btnread" onclick="clickChangelog({{$page->id}})">Отметить прочитанным</div>
        @endif
    </div>
</div>

<script>
    function clickChangelog(id) {
        fetch(`/school_pages/click/${id}`);
        $('.btnread').remove()
    }
</script>