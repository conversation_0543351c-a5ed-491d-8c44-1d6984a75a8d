
    <div class="row">
        <div class="col-lg-10 col-lg-offset-1">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="row">
                        <div class="col-md-12 col-lg-6">
                            {!! Breadcrumbs::render('home') !!}
                        </div>
                        <div class="col-md-12 col-lg-6">
                            <blockquote class="blockquote-reverse"><p>Я никогда не позволял, чтобы мои школьные занятия мешали моему образованию!</p>
                                <footer>Марк Твен</footer>
                            </blockquote>
                        </div>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="col-md-12">
                        @foreach($rubrics->chunk(4) as $rubric_items)
                            <div class="row">
                                @foreach($rubric_items as $rubric)
                                    <div class="col-md-6 col-lg-3 card">
                                        <a href="{{URL::route('rubric', ['id'=>$rubric->id])}}" class="card-block">
                                            @if($rubric->image)
                                                <div class="row">
                                                    <div class="col-md-4"><img src="{{ config('crm.franchise.school.image_path') }}{{ $rubric->image }}" class="img-fluid" alt=""></div>
                                                    <div class="col-md-8"><div class="row" style="margin-right: 15px;"><p>{{$rubric->name}}</p></div></div>
                                                </div>
                                            @else
                                                <p>{{$rubric->name}}</p>
                                            @endif
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
