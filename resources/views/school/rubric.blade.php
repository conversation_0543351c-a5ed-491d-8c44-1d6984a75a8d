@isset($rubric)
<div class="mb-3">
    {!! Breadcrumbs::render('rubric', $rubric) !!}
</div>
@endisset

@if(isset($rubric) && $rubric->files)
    <div class="card card-primary">
        <div class="card-header">
            <div class="list-group list-group-flush">
                @foreach($rubric->files as $file)
                    <a class="list-group-item list-group-item-action text-secondary" target="_blank" href="{{ $file['url'] }}" title="{{ $file['desc'] }}"><i class="fa fa-file-alt"></i> {{ !empty($file['title']) ? $file['title'] : $file['orig'] }}</a>
                @endforeach
            </div>
        </div>
    </div>
@endif

<div class="users" style="max-width: 1440px">
    <div class="containers">
        <h1>{{ $rubric->title ?? '' }}</h1>

        @if(isset($rubrics))
            @if(!empty($rubric) && $rubric->id == 5)
                <div class="row row-cols-1 row-cols-md-2 row-cols-xl-4" style="row-gap: 20px; margin-bottom: 20px">
                    <div class="col">
                        <x-school.card url="/certification"
                                       image="/files/school/image_13.png"
                                       title="План обучения"
                        />
                    </div>
                    <div class="col">
                        <x-school.card url="/practical_tasks"
                                       image="/files/school/image_13.png"
                                       title="Практические задания"
                        />
                    </div>
                    @if(isset($pages))
                        @if($item = $pages->find(302))
                            <div class="col">
                                <x-school.card :url="route('school.page', ['rubric_id' => $item->rubric_id, 'page_id' => $item->id])"
                                               image="/files/school/image_13.png"
                                               :title="$item->title"
                                               :subtitle="$item->anons"
                                               :updated="$item->updated ?? null"
                                />
                            </div>
                        @endif
                        @if($item = $rubrics->find(258))
                            <div class="col">
                                <x-school.card :url="route('school.rubric', ['rubric_id' => $item->id])"
                                               :image="$item->image"
                                               :title="$item->title"
                                               :subtitle="$item->alias"
                                               :color="!$item->parent_id ? 'primary' : 'secondary'"
                                               :updated="$item->updated ?? null"
                                               :rubric="$item"
                                />
                            </div>
                        @endif
                    @endif
                </div>
            @endif

            <div class="row row-cols-1 row-cols-md-2 row-cols-xl-4" style="row-gap: 20px">
                @foreach($rubrics as $item)
                    @continue($item->id == 258)
                    <div class="col">
                        <x-school.card :url="route('school.rubric', ['rubric_id' => $item->id])"
                                       :image="$item->image"
                                       :title="$item->title"
                                       :subtitle="$item->alias"
                                       :color="!$item->parent_id ? 'primary' : 'secondary'"
                                       :updated="$item->updated ?? null"
                                       :rubric="$item"
                        />
                    </div>
                @endforeach
                @if(empty($rubric->parent_id) && empty($pages))
                    <div class="col">
                        <x-school.card :url="route('admin.dictionary')"
                                       image="/files/school/image_7.png"
                                       title="СЛОВАРИК"
                                       subtitle="Внутрикорпоративный словарь используемых слов и значений"
                        />
                    </div>
                @endif
                @if(!empty($rubric) && $rubric->id == 38)
                    <div class="col">
                        <x-school.card url="/catalog"
                                       image="/files/school/image_7.png"
                                       title="ПАМЯТКА"
                                       subtitle="Обучение по всем категориям товаров интернет-магазина"
                        />
                    </div>
                @endif
            </div>
        @endif

        @if(isset($pages))
            <div class="row row-cols-1 row-cols-md-2 row-cols-xl-4" style="row-gap: 20px">
                @foreach($pages as $item)
                    @continue($item->id == 302)
                    <div class="col">
                        <x-school.card :url="route('school.page', ['rubric_id' => $item->rubric_id, 'page_id' => $item->id])"
                                       image="/files/school/image_12.png"
                                       :title="$item->title"
                                       :subtitle="$item->anons"
                                       :color="!$item->parent_id ? 'primary' : 'secondary'"
                                       :updated="$item->updated ?? null"
                        />
                    </div>
                @endforeach
            </div>
        @endif

    </div>
</div>
