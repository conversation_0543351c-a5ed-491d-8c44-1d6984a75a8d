<!doctype html>
<html lang="ru">
<head>
    <base target="_blank">
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link media="all" type="text/css" rel="stylesheet" href="/packages/sleepingowl/default/css/admin-app.css">
    <link media="all" type="text/css" rel="stylesheet" href="/css/crm.css?v=25">
</head>
<body>
<div class="container-fluid">
<form action="/calcs/send">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="form-group">
                <input type="text" name="title" value="{{ $title }}" class="form-control" style="width: 100%"/>
            </div>
            <div class="form-inline" style="width: 100%">
                {!! Form::select('credit-type', $credit_types, '', $attrs) !!}
                @isset($pay_types)
                    {!! Form::select('pay-type', $pay_types, '', $attrs) !!}
                @endisset
                @isset($carts_options)
                    {!! Form::select('goods', $carts_options, '', str_replace('width:170','width:350',$attrs)) !!}
                @endisset
                <a class="btn btn-warning float-right" id="send_email">ОТПРАВИТЬ</a>
            </div>
            <div class="clear"></div>
            <div class="form-group">
                <div class="form-group form-element-wysiwyg ">
                    <label for="email_msg" class="control-label"></label>
                    <textarea id="email_msg" name="email_msg" cols="50" rows="10">
                        <?php
                            $data_calc = array_merge($data,['tt_code'=> $tt_code]);
                            if (isset($carts)) $data_calc = array_merge($data_calc,['cart'=> current($carts)]);
                        ?>
                        {!! view("calc.{$data['bank']}", $data_calc) !!}
                    </textarea>
                </div>
            </div>
        </div>
    </div>
</form>
</div>

<script src="/packages/sleepingowl/default/js/admin-app.js"></script>
<script src="/js/components.js?v=25"></script>
<script src="/packages/sleepingowl/ckeditor/ckeditor.js"></script>
<script>
    Admin.WYSIWYG.switchOn('email_msg', 'ckeditor', {"height":500,"extraPlugins":"justify",
        "uploadUrl":"/ckeditor/upload/image","filebrowserUploadUrl":"/ckeditor/upload/image"})
</script>
<script>
@isset($carts)
    let goods = [];
    @foreach($carts as $cart)
    goods.push({name: `{!! $cart['name'] !!}`, brand: `{!! $cart['brand'] !!}`, price: '{!! $cart['price'] !!}', quantity: '{!! $cart['quantity'] !!}'});
    @endforeach
@endisset
    $(document).ready(function () {
        CKEDITOR.config.allowedContent = true;
        CKEDITOR.config.protectedSource.push(/<span[^>]*><\/span>/g);
        CKEDITOR.config.jqueryOverrideVal = true;

        $('a[href="#"]').on("click", function(event) { event.preventDefault() });

        $('select').select2().on('select2:select', function (e) {
            let name = $(this).attr('name');
            let editor = CKEDITOR.instances.email_msg;
            let data = e.params.data;
            let html = '';
            if (name !== 'goods') {
                html = setText(editor.getData(), "#"+name, data.id!==''?data.text:'');
            } else {
                html = setText(editor.getData(), "#good-name", data.id!==''?goods[data.id]['name']:'');
                html = setText(html, "#good-brand", data.id!==''?goods[data.id]['brand']:'');
                html = setText(html, "#good-price", data.id!==''?goods[data.id]['price']:'');
                html = setText(html, "#good-quantity", data.id!==''?goods[data.id]['quantity']:'');
            }
            editor.setData(html);
        });

        let setText = function(text, selector, data) {
            let wrapped = $("<div>" + text + "</div>");
            wrapped.find(selector).text(data);
            return wrapped.html();
        }
    });

    $('#send_email').click(function (e) {
        e.preventDefault();
        $('#send_email').addClass('disabled').text('Отправка...');
        let data = JSON.stringify({
            msg: CKEDITOR.instances.email_msg.getData(),
            title: $('input[name="title"]').val(),
            bank: '{{ $data['bank'] }}'
        })

        $.ajax({
            url: '/calcs/send',
            type: 'post',
            dataType: 'json',
            contentType: 'application/json',
            data: data,
            beforeSend: function (xhr) {
                xhr.setRequestHeader('X-CSRF-TOKEN', $('meta[name="csrf-token"]').attr('content'));
            },
            success: function() {
                Admin.Messages.success('Успешно', 'Сообщение отправлено!');
                $('#send_email').removeClass('disabled');
                $('#send_email').text('Отправить')
            },
            error: function(data) {
                Admin.Messages.error('Ошибка', data.error)
                $('#send_email').removeClass('disabled');
                $('#send_email').text('Отправить')
            }
        });
    });
</script>

</body>
</html>
