<div id="pos-credit-container"></div>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
<!-- Optional: include a polyfill for ES6 Promises for IE11 -->
<script src="https://cdn.jsdelivr.net/npm/promise-polyfill"></script>
<script src="https://my.pochtabank.ru/sdk/v2/pos-credit.js"></script>
<style>.swal2-input {margin-top: .3em} .swal2-label {font-size: 1.3em;color: #f95a05;font-weight: bold;}</style>
<script>
    (async () => {
        const {value: formValues} = await Swal.fire({
            title: 'Сумма корзины: {{ $paid_amount }}р.',
            html:
                '<label class="swal2-label">Сумма кредита</label><input id="amountCredit" class="swal2-input" placeholder="Сумма кредита" value="{{ $paid_amount }}">'+
                '<label class="swal2-label">Срок кредита</label><select id="termCredit" class="swal2-input" placeholder="Срок кредита">'+
                    '<option value="3">3 месяца</option>'+
                    '<option value="6">6 месяцев</option>'+
                    '<option value="12" selected>12 месяцев</option>'+
                    '<option value="24">24 месяца</option>'+
                    '<option value="36">36 месяцев</option>'+
                    '<option value="60">60 месяцев</option>'+
                '</select>'+
                '<label class="swal2-label">Сумма первоначального взноса (ПВ)</label><input id="firstPayment" class="swal2-input" placeholder="Сумма ПВ" value="{{ ceil($paid_amount*.1) }}">'+
                '<label class="swal2-label">Сумма товара</label><input id="price" class="swal2-input" placeholder="Сумма товара" value="{{ $item['price'] }}">'+
	            '<div>Марка товара: <b>{{ $item['mark'] }}</b></div>'+
	            '<div>Наименование товара/Модель: <b>{{ $item['model'] }}</b></div>'+
	            '<div>ФИО: <b>{{ $data['fio'] }}</b></div>'+
	            '<div>Телефон: <b>{{ $data['phone'] }}</b></div>'+
	            '<p>&nbsp;</p>'
	        ,
            focusConfirm: false,
            preConfirm: () => {
	            let out = [
                    parseInt(document.getElementById('amountCredit').value),
                    parseInt(document.getElementById('termCredit').value),
                    parseInt(document.getElementById('firstPayment').value),
                    parseInt(document.getElementById('price').value)
                ]
	            if (!out[0] || !out[1] || !out[2] || !out[3]) return Swal.showValidationMessage("Заполнены не все поля!");
	            if (out[2] >= out[0]) return Swal.showValidationMessage("Недопустимое значение ПВ");
                if (out[2] < out[0]*.1) return Swal.showValidationMessage("Мин. сумма ПВ: 10%");
                if (out[3] <= 0) return Swal.showValidationMessage("Укажите цену товара!");
                return out;
            }
        })

        if (formValues) {
            // Swal.fire(JSON.stringify(formValues))
            window.PBSDK.posCreditV2.mount('#pos-credit-container', {
				operId: '{{ $uuid }}',
	            productCode: 'VTB_EXP_DOM_BIG_19', //Код продукта 1-го уровня. Обязательно
	            // product2Code: null, // Код продукта 2-го уровня. Опционально
	            amountCredit: formValues[0], // Сумма кредита. Обязательно
	            // Обязательна передача одного из параметров – termCredit или annualPayment!
	            termCredit: formValues[1], // Срок кредита в месяцах. Опционально
	            // annualPayment: 20000, // Сумма ежемесячного платежа. Опционально
	            firstPayment: formValues[2], // Сумма первоначального взноса. Обязательно
	            ttCode: '0601001011021', // Код торговой точки. Обязательно
	            toCode: '060100101102', // Идентификатор ТО. Обязательно
	            ttName: 'Г ЕКАТЕРИНБУРГ, УЛ ТУРГЕНЕВА, Д. 13, ОФ. 417', // Адрес ТТ. Опционально
	            fullName: '{{ $data['fio'] }}', // ФИО. Опционально
	            phone: '{{ preg_replace("/^(8|7)/", '', $data['phone']) }}', // Телефон в формате 9161232323. Опционально
	            brokerAgentId: 'NON_BROKER', // Идентификатор Агента. Обязательно
	            returnUrl: 'https://alfamart24.ru', // URL интернет-магазина
	            order: [    // Заказ, обязательно должна быть хотя бы одна позиция заказа.
	            {
					category: 245, // Код категории товара из справочника категорий. Обязательно
					mark: '{{ $item['mark'] }}',     // Марка товара. Обязательно
                    model: '{{ $item['model'] }}', // Наименование товара/Модель. Обязательно
                    quantity: 1,       // Количество товара, целое число. Обязательно
                    price: formValues[3]   // Цена товара в рублях. Обязательно, больше 0
                },
            ]
        });

        // подписка на событие завершения заполнения заявки
        window.PBSDK.posCreditV2.on('done', function(id){
            console.log('Id заявки: ' + id)
        });
        }
    })()
   // При необходимости можно убрать виджет вызвать unmount
   // window.PBSDK.posCreditV2.unmount('#pos-credit-container');
</script>
