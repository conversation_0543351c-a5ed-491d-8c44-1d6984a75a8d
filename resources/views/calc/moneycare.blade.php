<div id="pos-credit-container"></div>
{{--<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>--}}
<link rel="stylesheet" href="/js/libs/sweetalert2/sweetalert2.min.css?v=build">
<script src="/js/libs/sweetalert2/sweetalert2.min.js?v=build"></script>
<script type="text/javascript" src="/js/calc/russkiy-standart/jquery-1.6.min.js"></script>

<style>.swal2-input {margin-top: .3em} .swal2-label {display:block;font-size: 1.3em;color: #f95a05;font-weight: bold;} .swal2-textarea {margin-left: auto;margin-right: auto;} .swal2-actions {margin-top: 0;}</style>

<script>
/***
    orderId *	String	Код заказа из внешней системы.
    pointId *	String	Код торговой точки.
    lastName 	String	Фамилия
    firstName *	String	Имя
    secondName	String	Отчество
    phone *	String	Номер телефона заемщика (10 цифр, без использования метасимволов)
    creditTypes	String[]	Classic - кредит
    installment – рассрочка. Если ничего не указано, разрешены все типы.
    creditPeriod	Integer	Срок кредита в месяцах, при выборе Передается (необязательно) при СreditTypes = classic
    installmentPeriods	Integer[]	Список разрешенных сроков по рассрочке, передается (необязательно) при creditTypes= installment, массив допустимых сроков
    maxDiscount	Float	Максимальный размер скидки (%) по рассрочке (для калькулятора - рассрочки с большей скидкой не будут выводиться)
    downpayment	Float	Размер первоначального взноса, в рублях.
    formSuccessUrl *	String	Ссылка переадресации пользователя после успешного заполнения кредитной заявки. Если ссылка не будет передана, пользователь будет возвращен на страницу из заголовка Referer.
    formCancelUrl *	String	Ссылка переадресации пользователя после неуспешного (отмены) заполнения кредитной заявки. Если ссылка не будет передана, пользователь будет возвращен на страницу из заголовка Referer.
Параметры в goods
    good *		Позиция в заявке.
    type	Enum	Тип позиции в заявке. product - товар, service - доп. услуга. По-умолчанию - product.
    groupId *	String	Идентификатор группы (см. товарный справочник)
    Price *	Float	Цена.
    Title	String	Наименование товара
    Count *	Integer	Количество.
Параметры используемые для корректной логистики заявок в ПО брокера (Используемость см. пункт 2, 3, 4, 5)
    generateForm	Boolean	Сформировать форму для заполнения кредитной заявки и вернуть ссылку на нее в ответе. Для вызова формы передавать generateForm = true
    entrypointCode	Enum	Точка входа в процесс (переменная чувствительна к регистру)
    Используется в случаях работы без запуска формы для заполнения кредитной заявки.
    createOnlineForCallCenter – передача информации для обработки колл центром Брокера
    createPoint - передача информации на Торговую точку для обработки оператором с дальнейшей передачей заявки в колл центр Брокера
    onlinePointId	String	Код торговой точки.
    При использовании данного кода, PointID становится кодом торговой точки подписания (магазина)/выдачи товара
 */

    (async () => {
        const {value: formValues} = await Swal.fire({
            title: 'Сумма корзины: {{ $paid_amount }}р.',
            html:
                '<label class="swal2-label">Сумма товара</label><input id="price" class="swal2-input" placeholder="Сумма товара" value="{{ $paid_amount }}">'+
                '<label class="swal2-label">Сумма первоначального взноса (ПВ)</label><input id="firstPayment" class="swal2-input" placeholder="Сумма ПВ" value="0">'+
                '<label class="swal2-label">Категория товара</label><select id="group_id" class="swal2-input">' +
                    '<option value="" disabled selected>Выбрать..</option>'+
                    '<option value="PROCH">Мебель прочее</option>'+
                    '<option value="sp25">Мотороллер / мопед / скутер</option>'+
                    '<option value="Doors">Двери</option>'+
                '</select>'+
                '<textarea class="swal2-textarea" placeholder="Комментарий..." id="comment" style="display: flex;"></textarea>'+
                '<div style="text-align: left;">'+
                    '<p>ФИО: <b>{{ $client->fio }}</b></p>'+
                    '<p>Телефон: <b>{{ $data['phone'] }}</b></p>'+
                    '<p>Наименование товара: <b>{{ $item['model'] }}</b></p>'+
                '</div>'
	        ,
            focusConfirm: false,
            backdrop: false,
            preConfirm: () => {
	            let out = [
                    parseFloat(document.getElementById('price').value.replace(/\s/g, '')),
                    parseFloat(document.getElementById('firstPayment').value.replace(/\s/g, '')),
                    document.getElementById('comment').value,
                    document.getElementById('group_id').value
                ]
                console.log(out,out[1] < 0)
	            if (!checkNumber(out[0]) || !checkNumber(out[1]) || !out[0] || out[1] < 0) return Swal.showValidationMessage("Заполнены не все поля!");
                // if (out[1] < out[0]*.1) return Swal.showValidationMessage("Мин. сумма ПВ: 20%");
                if (out[0] <= 0) return Swal.showValidationMessage("Укажите цену товара!");
                if (out[3].length == 0) return Swal.showValidationMessage("Выберите категорию!");
                return out;
            }
        })
        <?php
            $pointId = $isrd ? '050420221555' : '171220211509';
//            $pointId = /*config('franchise.key') == 'ekb' ?*/ '171220211509'/* : '050420221555'*/;
//            $groupId = config('franchise.key') == 'ekb' ? 'PROCH' : 'Test';
        ?>
        if (formValues) {
            let data = {
                "orderId": "{{ $id }}",
                "pointId": "{{ $pointId }}",
                "generateForm": false,
                "entrypointCode": "createOnlineForCallCenter",
                "downpayment": formValues[1],
                "comment": formValues[2],
                "lastName": "{{ $client->last_name }}",
                "firstName": "{{ $client->first_name }}",
                "secondName": "{{ $client->middle_name }}",
                "phone": "{{ substr(preg_replace('/^7/','',$data['phone']),0,10) }}",
                "goods": [{"type":"product","title":"{{ $item['model'] }}","groupId":formValues[3],"price": formValues[0],"count":1}],
                // "creditTypes":["installment"],
                // "installmentPeriods":["24","36"],
                "formSuccessUrl": "https://alfamart24.ru/api/moneycare/success/{{ $id }}",
                "formCancelUrl": "https://alfamart24.ru/api/moneycare/cancel/{{ $id }}"
            }

            $.ajax({
                url: '/api/moneycare/create',
                type: 'POST',
                dataType: 'json',
                data,
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-CSRF-TOKEN', '{{ csrf_token() }}');
                },
                success: function(data){
                    console.log(formValues, data)
                    if (data.accepted === "true") Swal.fire('Успешно!', `<p>Id заявки: ${data.id}</p>`, 'success')
                    else Swal.fire('Ошибка!', 'Произошла ошибка', 'error');
                }
            })
        }
    })()

function checkNumber(x) {

    // check if the passed value is a number
    if(typeof x == 'number' && !isNaN(x)){

        // check if it is integer
        if (Number.isInteger(x)) {
            console.log(`${x} is integer.`);
            return true
        }
        else {
            console.log(`${x} is a float value.`);
            return true
        }

    } else {
        console.log(`${x} is not a number`);
        return false;
    }
}
</script>
