@php
    $tax = config('alfamart.robokassa.tax');
    $tax_p = round($tax * 100) . '%';
    $sum = $paid_amount + config('alfamart.robokassa.sum');
@endphp
<div class="row">
    <div class="col-md-6">
        <div class="panel panel-default">
            <form action="" method="get">
            <div class="panel-body">
                <div class="form-group">
                    <label>Заказ #</label>
                    <input type="text" class="form-control" name="id" value="{{ $id ?? '' }}">
                </div>
                <div class="form-group">
                    <label>Сумма к оплате</label>
                    <input type="text" class="form-control" name="paid_amount" value="{{ $paid_amount ?? '' }}">
                </div>
                <div class="form-group">
                    <label>Тип оплаты</label>
                    <select name="pay_type" class="form-control">
                        <option value="-1" {{ $pay_type == -1 ? 'selected' : '' }}>Предоплата/оплата 100%</option>
                        <option value="1" {{ $pay_type == 1 ? 'selected' : '' }}>Доплата</option>
                        {{--<option value="2" {{ $pay_type == 2 ? 'selected' : '' }}>Доплата/2й платеж</option>--}}
                        {{--<option value="3" {{ $pay_type == 3 ? 'selected' : '' }}>Доплата/3й платеж</option>--}}
                        <option value="0" {{ $pay_type == 0 ? 'selected' : '' }}>Доплата окончательный</option>
                    </select>
                </div>
            </div>
            <div class="panel-footer">
                <button type="submit" class="btn btn-success">Пересчитать</button>
            </div>
            </form>
        </div>
    </div>
    <div class="col-md-6">
        <div class="panel panel-default">
            <div class="panel-body">
                @if($pay_type==-1)
                    <div class="form-group">
                        <label style="width: 100%;">20% предоплата <span class="float-right"><b class="text-danger">{{ number_format($paid_amount*.2, 2, ',', ' ') }} руб.</b>{!! $paid_amount*.2>=$sum?' + '.$tax_p.' <small>('.$tax*$paid_amount*.2.' руб)</small>' : '' !!}</span></label>
                        <input type="text" class="form-control" value="{{ $paid_amount>0 ? $robo10 : '' }}">
                    </div>
                    <div class="form-group">
                        <label style="width: 100%;">40% предоплата <span class="float-right"><b class="text-danger">{{ number_format($paid_amount*.4, 2, ',', ' ') }} руб.</b>{!! $paid_amount*.4>=$sum?' + '.$tax_p.' <small>('.$tax*$paid_amount*.4.' руб)</small>' : '' !!}</span></label>
                        <input type="text" class="form-control" value="{{ $paid_amount>0 ? $robo40 : '' }}">
                    </div>
                    <div class="form-group">
                        <label style="width: 100%;">50% предоплата <span class="float-right"><b class="text-danger">{{ number_format($paid_amount*.5, 2, ',', ' ') }} руб.</b>{!! $paid_amount*.5>=$sum?' + '.$tax_p.' <small>('.$tax*$paid_amount*.5.' руб)</small>' : '' !!}</span></label>
                        <input type="text" class="form-control" value="{{ $paid_amount>0 ? $robo50 : '' }}">
                    </div>

                    <div class="form-group">
                        <label style="width: 100%;">70% предоплата <span class="float-right"><b class="text-danger">{{ number_format($paid_amount*.7, 2, ',', ' ') }} руб.</b>{!! $paid_amount*.7>=$sum?' + '.$tax_p.' <small>('.$tax*$paid_amount*.7.' руб)</small>' : '' !!}</span></label>
                        <input type="text" class="form-control" value="{{ $paid_amount>0 ? $robo70 : '' }}">
                    </div>
                    <hr>
                    <div class="form-group">
                        <label style="width: 100%;">100% оплата <span class="float-right"><b class="text-danger">{{ number_format($paid_amount, 2, ',', ' ') }} руб.</b>{!! $paid_amount>=$sum?' + '.$tax_p.' <small>('.$tax*$paid_amount.' руб)</small>' : '' !!}</span></label>
                        <input type="text" class="form-control" value="{{ $paid_amount>0 ? $robolink : '' }}">
                    </div>
                @else
                    <div class="form-group">
                        <label style="width: 100%;">Доплата {{ $pay_type>0 ? $pay_type : 'окончательная' }}: <span class="float-right"><b class="text-danger">{{ number_format($paid_amount, 2, ',', ' ') }} руб.</b>{!! $paid_amount>=$sum?' + '.$tax_p.' <small>('.$tax*$paid_amount.' руб)</small>' : '' !!}</span></label>
                        <input type="text" class="form-control" value="{{ $paid_amount>0 ? $postpay : '' }}">
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
