<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<script type="text/javascript" src="/js/calc/russkiy-standart/jquery-1.6.min.js"></script>
<script type="text/javascript" src="/js/calc/russkiy-standart/calc.js"></script>
<head>
    <title>Анкета на оформление кредита в Банке Русский Стандарт<</title>
    <style type="text/css">
        .form-field {
            border: 1px solid #ccc;
        }

        .style1 {
            width: 632px;
        }

        .style2 {
            width: 359px;
            border-style: solid;
            border-width: 0px;
            border-color: White;
        }

        .style3 {
            width: 92px;
            border-style: solid;
            padding-left: 3px;
            padding-right: 3px;
            border-width: 0px;
            border-color: White;
        }

        .style4 {
            width: 37px;
            border-style: solid;
            border-width: 1px;
            border-color: White;
        }

        #Text19 {
            width: 498px;
        }

        #Button1 {
            width: 100%;
        }

        .namelabel {
            width: 80px;
            display: block;
            float: left;
        }

        body {
            font-family: 'Helvetica Neue', Helvetica, sans-serif;
            font-size: 11pt;
        }

        .form-container {
            border: 1px solid #d1dff0;

            -webkit-border-radius: 8px;
            -moz-border-radius: 8px;
            border-radius: 8px;

            padding: 20px;

        }

        .form-field {
            border: 1px solid silver;
            background: #ffffff;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
            color: #636363;
            -webkit-box-shadow: rgba(255, 255, 255, 0.4) 0 1px 0, inset rgba(000, 000, 000, 0.7) 0 0px 0px;
            -moz-box-shadow: rgba(255, 255, 255, 0.4) 0 1px 0, inset rgba(000, 000, 000, 0.7) 0 0px 0px;
            box-shadow: rgba(255, 255, 255, 0.4) 0 1px 0, inset rgba(000, 000, 000, 0.7) 0 0px 0px;
            padding: 1px;

        }

        .form-field:focus {
            background: #fff;
            color: black;
        }

        .form-container h2 {
            text-shadow: #fdf2e4 0 1px 0;
            font-size: 18px;
            margin: 0 0 10px 0;
            font-weight: bold;
            text-align: center;
        }

        .form-title {
            margin-bottom: 10px;
            color: #284570;
            text-shadow: #fdf2e4 0 1px 0;
        }

        .submit-container {
            margin: 8px 0;
            text-align: right;
        }

        .submit-button {
            border: 1px solid #447314;
            background: #6aa436;
            background: -webkit-gradient(linear, left top, left bottom, from(#8dc059), to(#6aa436));
            background: -webkit-linear-gradient(top, #8dc059, #6aa436);
            background: -moz-linear-gradient(top, #8dc059, #6aa436);
            background: -ms-linear-gradient(top, #8dc059, #6aa436);
            background: -o-linear-gradient(top, #8dc059, #6aa436);
            background-image: -ms-linear-gradient(top, #8dc059 0%, #6aa436 100%);
            -webkit-border-radius: 4px;
            -moz-border-radius: 4px;
            border-radius: 4px;
            -webkit-box-shadow: rgba(255, 255, 255, 0.4) 0 1px 0, inset rgba(255, 255, 255, 0.4) 0 1px 0;
            -moz-box-shadow: rgba(255, 255, 255, 0.4) 0 1px 0, inset rgba(255, 255, 255, 0.4) 0 1px 0;
            box-shadow: rgba(255, 255, 255, 0.4) 0 1px 0, inset rgba(255, 255, 255, 0.4) 0 1px 0;
            text-shadow: #addc7e 0 1px 0;
            color: #31540c;
            font-family: helvetica, serif;
            padding: 8.5px 18px;
            font-size: 14px;
            text-decoration: none;
            vertical-align: middle;
        }

        .submit-button:hover {
            border: 1px solid #447314;
            text-shadow: #31540c 0 1px 0;
            background: #6aa436;
            background: -webkit-gradient(linear, left top, left bottom, from(#8dc059), to(#6aa436));
            background: -webkit-linear-gradient(top, #8dc059, #6aa436);
            background: -moz-linear-gradient(top, #8dc059, #6aa436);
            background: -ms-linear-gradient(top, #8dc059, #6aa436);
            background: -o-linear-gradient(top, #8dc059, #6aa436);
            background-image: -ms-linear-gradient(top, #8dc059 0%, #6aa436 100%);
            color: #fff;
        }

        .submit-button:active {
            text-shadow: #31540c 0 1px 0;
            border: 1px solid #447314;
            background: #8dc059;
            background: -webkit-gradient(linear, left top, left bottom, from(#6aa436), to(#6aa436));
            background: -webkit-linear-gradient(top, #6aa436, #8dc059);
            background: -moz-linear-gradient(top, #6aa436, #8dc059);
            background: -ms-linear-gradient(top, #6aa436, #8dc059);
            background: -o-linear-gradient(top, #6aa436, #8dc059);
            background-image: -ms-linear-gradient(top, #6aa436 0%, #8dc059 100%);
            color: #fff;
        }

        .title1 {
            margin-left: 20px;
            font-size: 15pt;
        }
    </style>


    <link href="/js/calc/russkiy-standart/css.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<form id="anketa" name="Anketa" class='form-container' action="test.htm" method="get" onsubmit="check();">
    <div id="top">

        <table>
            <tr>
                <td>
                    <img alt="logo" src="/js/calc/russkiy-standart/logo2.png"/></td>
                <td>
                    <div id="ttd" class="title1">Оформление заказа в кредит<br/></div>
                </td>
            </tr>
        </table>


    </div>
    <div id="main">
        <p><span class='namelabel'>Город</span>
            <select id="MainCity" class="form-field" style="width:200px;"></select>
        <p><span class='namelabel'>TPL</span>
            <select id="MainTT" class="form-field" style="width:200px;"></select>

            <br/><br/>
        <p><span class='namelabel'>№ заказа</span><input id="TextBox4s_konk" runat="server" class="form-field"
                                                         maxlength="10" name="data"
                                                         onkeypress="return checkinput2(event)"
                                                         style="width:200px; text-align: center;" value="{!! request()->get('id')  !!}"
                                                         type="text"/><br/><br/>
        <table id="tb1" class="style1">
            <tr id="tr1">
                <td class="style4"><strong>№</strong></td>
                <td class="style2"><strong>Наименование</strong> <strong>товара</strong></td>
                <td class="style3"><strong>Количество</strong></td>
                <td><strong>Цена</strong></td>
            </tr>
            <tr id="tr2">
                <td class="style4">1</td>
                <td class="style2"><input id="Text1" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk0" runat="server" class="form-field" maxlength="10"
                                          name="data0" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk18" runat="server" class="form-field" maxlength="10" name="data18"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4">2</td>
                <td class="style2"><input id="Text2" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk1" runat="server" class="form-field" maxlength="10"
                                          name="data1" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk36" runat="server" class="form-field" maxlength="10" name="data36"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4">3</td>
                <td class="style2"><input id="Text3" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk2" runat="server" class="form-field" maxlength="10"
                                          name="data2" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk37" runat="server" class="form-field" maxlength="10" name="data37"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4"> 4</td>
                <td class="style2"><input id="Text4" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk3" runat="server" class="form-field" maxlength="10"
                                          name="data3" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk38" runat="server" class="form-field" maxlength="10" name="data38"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4"> 5</td>
                <td class="style2"><input id="Text5" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk4" runat="server" class="form-field" maxlength="10"
                                          name="data4" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk39" runat="server" class="form-field" maxlength="10" name="data39"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4">6</td>
                <td class="style2"><input id="Text6" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk5" runat="server" class="form-field" maxlength="10"
                                          name="data5" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk40" runat="server" class="form-field" maxlength="10" name="data40"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4">7</td>
                <td class="style2"><input id="Text7" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk6" runat="server" class="form-field" maxlength="10"
                                          name="data6" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk41" runat="server" class="form-field" maxlength="10" name="data41"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4"> 8</td>
                <td class="style2"><input id="Text8" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk7" runat="server" class="form-field" maxlength="10"
                                          name="data7" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk42" runat="server" class="form-field" maxlength="10" name="data42"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4">9</td>
                <td class="style2"><input id="Text9" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk8" runat="server" class="form-field" maxlength="10"
                                          name="data8" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk43" runat="server" class="form-field" maxlength="10" name="data43"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4">10</td>
                <td class="style2"><input id="Text10" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk9" runat="server" class="form-field" maxlength="10"
                                          name="data9" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk44" runat="server" class="form-field" maxlength="10" name="data44"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4"> 11</td>
                <td class="style2"><input id="Text11" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk10" runat="server" class="form-field" maxlength="10"
                                          name="data10" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk45" runat="server" class="form-field" maxlength="10" name="data45"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4">12</td>
                <td class="style2"><input id="Text12" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk11" runat="server" class="form-field" maxlength="10"
                                          name="data11" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk46" runat="server" class="form-field" maxlength="10" name="data46"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4">13</td>
                <td class="style2"><input id="Text13" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk12" runat="server" class="form-field" maxlength="10"
                                          name="data12" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk47" runat="server" class="form-field" maxlength="10" name="data47"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4"> 14</td>
                <td class="style2"><input id="Text14" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk13" runat="server" class="form-field" maxlength="10"
                                          name="data13" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk48" runat="server" class="form-field" maxlength="10" name="data48"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4"> 15</td>
                <td class="style2"><input id="Text15" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk14" runat="server" class="form-field" maxlength="10"
                                          name="data14" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk49" runat="server" class="form-field" maxlength="10" name="data49"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4">16</td>
                <td class="style2"><input id="Text16" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk15" runat="server" class="form-field" maxlength="10"
                                          name="data15" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk50" runat="server" class="form-field" maxlength="10" name="data50"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4"> 17</td>
                <td class="style2"><input id="Text17" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk16" runat="server" class="form-field" maxlength="10"
                                          name="data16" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk51" runat="server" class="form-field" maxlength="10" name="data51"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4">18</td>
                <td class="style2"><input id="Text18" class="form-field" style="width: 100%; text-align: center;"
                                          type="text" onkeyup="calcf3();"/></td>
                <td class="style3"><input id="TextBox4s_konk17" runat="server" class="form-field" maxlength="10"
                                          name="data17" onkeypress="return checkinput2(event)"
                                          onkeyup="calcf(); che(event);" style="width:100%; text-align: center;"
                                          type="text" value="0"/></td>
                <td><input id="TextBox4s_konk52" runat="server" class="form-field" maxlength="10" name="data52"
                           onkeypress="return checkinput(event)" onkeyup="calcf2(); che2(event);"
                           style="width:97%; text-align: center;" type="text" value="0"/></td>
            </tr>
            <tr>
                <td class="style4"></td>
                <td class="style2"></td>
                <td class="style3"></td>
                <td></td>
            </tr>
            <tr>
                <td class="style4"> &nbsp;</td>
                <td class="style2"> Всего:</td>
                <td class="style3"><input id="TextBox4s_konk54" runat="server" class="form-field" maxlength="10"
                                          name="data54" style="width:100%; text-align: center;" type="text"
                                          readonly="readonly" value="0"/></td>
                <td><input id="TextBox4s_konk53" runat="server" class="form-field" maxlength="10" name="data53"
                           style="width:97%; text-align: center;" type="text" readonly="readonly" value="0"/></td>
            </tr>
            <tr>
                <td class="style4">&nbsp;</td>
                <td class="style2"> &nbsp;</td>
                <td class="style3"> &nbsp;</td>
                <td><input id="Button1" type="button" value="Далее" class="submit-button" onclick="proverka()"/><br/>
                </td>
            </tr>
        </table>
        </p>
        <script type="text/javascript" src="/js/calc/russkiy-standart/info.js"></script>
    </div>
</form>


<script type="text/javascript">

    ttxx();

    $(document).ready(function () {
        var goods = {!! json_encode($goods) !!};
        var table = $('#tb1');
        $.each(goods, function(index, value) {
            var tr = table.find('tr:eq('+(index+1)+') .style2');
            tr.find('input').val(value.catalog);
            tr.next().find('input').val(value.quantity);
            tr.next().next().find('input').val(value.price);
        });
        calcf3();
        calcf(); che(event);
        calcf2(); che2(event);
//$('#Button1').click(function () {proverka();});


        var a = $('#MainCity').val().split(",");
        settpl(a);


        $('#MainCity').change(function () {
            var a = $(this).val().split(",");
            settpl(a);
        });
    });

    $('#MainTT').change(function () {
        TT = $(this).val();

    });

    function settpl(a) {
        $('#MainTT').html('');
        for (var i = 0, max = a.length; i < max; i++) {
            $('#MainTT').append('<option value="' + a[i] + '">' + a[i] + '</option>');
        }
        TT = $('#MainTT').val();

    }

</script>
</body>
</html>
