@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-body text-center p-5">
            <img src="/img/logo.svg" alt="" class="img-fluid mb-3">
            <img src="/img/Alfamart24.ru%20CRM.svg" alt="" class="img-fluid mb-4">
            <form method="POST" action="{{ route('login') }}">
                @csrf

                <div class="form-group">
                    <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" name="email" value="{{ old('email') }}" required autocomplete="email" autofocus placeholder="{{ __('E-Mail Address') }}">

                    @error('email')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>

                <div class="form-group">
                    <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" name="password" required autocomplete="current-password" placeholder="{{ __('Password') }}">

                    @error('password')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>

                <div class="form-group">
                    <div class="custom-control custom-checkbox text-left">
                        <input class="custom-control-input" type="checkbox" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}>

                        <label class="custom-control-label" for="remember">
                            {{ __('Remember Me') }}
                        </label>
                    </div>
                </div>

                <div class="form-group mb-0">
                    <button type="submit" class="btn btn-primary btn-block text-white">
                        {{ __('Login') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('head-styles')
    <style>
        html, body {
            height: 100%;
        }

        body {
            background: url('/img/back.png') no-repeat;
            background-size: cover;

            display: -ms-flexbox;
            display: flex;
            -ms-flex-align: center;
            align-items: center;
        }

        #app {
            width: 100%;
            max-width: 360px;
            padding: 15px;
            margin: auto;
        }

        .card {
            width: 360px;
        }
    </style>
@endpush