<div class="form-group d-flex">
    <div class="btn-group btn-group-sm">
{{--        <button onclick="get_template('wa_vk')" type="button" class="btn btn-success"><i class="fab fa-whatsapp" aria-hidden="true"></i> Отклик VK</button>--}}
{{--        <button onclick="get_template('wa_questions')" type="button" class="btn btn-success"><i class="fab fa-whatsapp" aria-hidden="true"></i> Вопросы при телефонном интервью</button>--}}
{{--        <button onclick="get_template('wa_missed_sales')" type="button" class="btn btn-success"><i class="fab fa-whatsapp" aria-hidden="true"></i> Недозвон продажник</button>--}}
{{--        <button onclick="get_template('wa_sms')" type="button" class="btn btn-success"><i class="fab fa-whatsapp" aria-hidden="true"></i> СМС приглашение</button>--}}
{{--        <button onclick="get_template('wa_missed_logist')" type="button" class="btn btn-success"><i class="fab fa-whatsapp" aria-hidden="true"></i> Недозвон логист</button>--}}
{{--        <button onclick="get_template('wa_calling')" type="button" class="btn btn-success"><i class="fab fa-whatsapp" aria-hidden="true"></i> Прозвон вакансий</button>--}}


        <button onclick="get_template('newsletter_logistics')" type="button" class="btn btn-success mr-1">Логист рассылка</button>
        <button onclick="get_template('newsletter_manager')" type="button" class="btn btn-success mr-1">Менеджер рассылка</button>
        <button onclick="get_template('approve')" type="button" class="btn btn-success mr-1">Утвердить</button>
        <button onclick="get_template('missed_manager')" type="button" class="btn btn-success mr-1">Недозвон менеджер</button>
        <button onclick="get_template('address_turgeneva')" type="button" class="btn btn-success mr-1">Адрес Тургенева</button>
        <button onclick="get_template('address_pervomaiskaya')" type="button" class="btn btn-success mr-1">Адрес Первомайская</button>
        <button onclick="get_template('relevance')" type="button" class="btn btn-success mr-1">Актуальность</button>
        <button onclick="get_template('reject')" type="button" class="btn btn-success">Отказ</button>
    </div>
    <input type="hidden" id="template-type" value="">
</div>

{{--@include('doc.order.partials.attach')--}}

@push('footer-scripts')
    <script>
        $(document).ready(function () {
            CKEDITOR.config.allowedContent = true;
            // CKEDITOR.config.defaultLanguage = 'ru';

            $('a[href="#"]').on("click", function(event){
                event.preventDefault()
            });
            // get_template('order');
        })

        function get_template(type) {
            // $('.attachments, .invoice, .contract').hide();
            $.ajax({
                url: '/emails/candidate_template',
                type: 'get',
                data: {
                    id: '{{ $id }}',
                    type: type
                },
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('X-CSRF-TOKEN', $('meta[name="csrf-token"]').attr('content'));
                },
                success: function (data) {
                    if (data.error !== undefined) {
                        return Admin.Messages.error('Ошибка', data.error);
                    }
                    $('#template-type').val(type);
                    try {
                        CKEDITOR.instances.email_msg.setData(data);
                    } catch (e) {
                        console.log(e)
                    }
                    // if (type === 'order') $('.attachments, .invoice, .contract').show();
                    // if (type === 'nonorder') $('.attachments, .invoice').show();
                    // if (type === 'order_credit') $('.attachments, .contract').show();
                }
            })
        }
    </script>
@endpush
