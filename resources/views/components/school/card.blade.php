@props(['url' => '#', 'image' => null, 'title' => null, 'subtitle' => null, 'color' => 'primary', 'updated' => null, 'rubric' => null])
<div class="card" style="max-width: 317px; height: 340px; border-radius: 18px; padding: 30px 10px 30px 30px;overflow: hidden; word-break: break-word;">
{{--    @if($image) --}}{{--https://via.placeholder.com/148x148--}}
        <img src="{{ $image ?? '/files/school/image_3.png' }}" style="width: 148px; height: 148px; right: 0; bottom: 0; position: absolute"/>
{{--    @endif--}}
    @if(!empty($rubric) && (auth()->user()->isSuperAdmin() || auth()->user()->hasRole(['topsales','seniorsales','subdostavka']) || auth()->user()->hasRole('personal')))
        <div style="position: absolute; top: 10px; left: 25px;z-index: 12; transform: rotate(0);">
            <a target="_blank" href="/school_rubrics/{{$rubric->id.'/edit'}}" role="button" class="btn btn-primary btn-xs">редактировать</a>
        </div>
    @endif
    <div class="d-flex flex-column" style="font-family: Mulish,serif; color: #061A32;gap: 20px; z-index: 1;">
        @if($updated)
            <small class="text-muted" style="position: absolute; top: 10px; right: 25px;z-index: 10">{{ $updated }}</small>
        @endif
        <div style="font-weight: 900;font-size: 2em;line-height: 35px;overflow: hidden;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 3;">{{ mb_ucfirst(mb_strtolower($title)) }}</div>
        @if($subtitle)
            <div style="font-weight: 600;font-size: 18px;line-height: 23px">{{ mb_ucfirst(mb_strtolower($subtitle)) }}</div>
        @endif
    </div>
    <div class="mt-auto" style="z-index: 10">
        <a title="{{ $title }}" href="{{ $url }}" class="btn btn-lg btn-{{ $color }} stretched-link" style="padding: 12px 20px 15px;border-radius: 8px;">Перейти</a>
    </div>
</div>