<!doctype html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Отчет по логистам за месяц</title>
    <link media="all" type="text/css" rel="stylesheet" href="/packages/sleepingowl/default/css/admin-app.css">
    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    @livewireStyles
    <style>
        .toolt {
            position: relative;
            display: inline-block;
            color: black;
        }

        .toolttext {
            visibility: hidden;
            background-color: white;
            border: 1px solid #0d7a38;
            border-radius: 4px;
            padding: 2pt;
            width: 200px;
            /* Position the tooltip */
            position: absolute;
            z-index: 1;
        }
        .tleft {
            margin-left: -200px;
        }
        .toolt:hover .toolttext {
            visibility: visible;
        }
    </style>
</head>
<body class="bg-white">

<div class="row">
    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Все выполненные дела</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ $allTasksComplete->count() }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allTasksComplete->get() as $task)--}}
{{--                    <a href="/tasks/{{ $task->id }}/edit" target="_blank">{{ $task->id }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>
    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Дел было поставлено </p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ $allTasksSets->count() }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allTasksSets->get() as $task)--}}
{{--                    <a href="/tasks/{{ $task->id }}/edit" target="_blank">{{ $task->id }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>
    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Все перенесенные дела</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ $allTasksTransfer['count'] }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allTasksTransfer['ids'] as $task)--}}
{{--                    <a href="/tasks/{{ $task }}/edit" target="_blank">{{ $task }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>
    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Поступило дел</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ $allTasksNew->count() }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allTasksNew->get() as $task)--}}
{{--                    <a href="/tasks/{{ $task->id }}/edit" target="_blank">{{ $task->auditable_id }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>
    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Новых заказов</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ count($allNew) }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allNew as $task)--}}
{{--                    <a href="/order_news/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>
    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Претензии</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ count($allPretense) }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allPretense as $task)--}}
{{--                    <a href="/order_news/{{ $task['auditable_id'] }}/edit" target="_blank">{{ $task['auditable_id'] }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>
    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Возвраты</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ count($allReturn) }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allReturn as $task)--}}
{{--                    <a href="/order_news/{{ $task['auditable_id'] }}/edit" target="_blank">{{ $task['auditable_id'] }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Суд</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ count($allClaim) }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allClaim as $task)--}}
{{--                    <a href="/order_news/{{ $task['auditable_id'] }}/edit" target="_blank">{{ $task['auditable_id'] }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Обработано дел по А</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ $allTasksProcessedA->count() }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allTasksProcessedA->get() as $task)--}}
{{--                    <a href="/tasks/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Обработано дел по B</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ $allTasksProcessedB->count() }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allTasksProcessedB->get() as $task)--}}
{{--                    <a href="/tasks/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Обработано дел по C</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ $allTasksProcessedC->count() }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allTasksProcessedC->get() as $task)--}}
{{--                    <a href="/tasks/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Обработано дел по D</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ $allTasksProcessedD->count() }}</h3>
            </div>
{{--            <div x-show="show"--}}
{{--                 style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">--}}
{{--                @foreach($allTasksProcessedD->get() as $task)--}}
{{--                    <a href="/tasks/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;--}}
{{--                @endforeach--}}
{{--            </div>--}}
        </div>
    </div>
    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Сложные дела</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ $allByType[2] }}</h3>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Средние дела</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ $allByType[1] }}</h3>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}" @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Легкие дела</p>
                <h3 @click="show=!show" style="cursor: pointer;">{{ $allByType[0] }}</h3>
            </div>
        </div>
    </div>

</div>

@foreach($managers as $key=>$manager)
    <div class="row mb-4">
        <div class="col-12">
            <div class="row">
                <div class="col-12">
                    <h1 title="{{ $key }}">{{ $manager['name'] }}</h1>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <b>Общая статистика</b>
                    <div class="row">
                        <div class="col-md-10">Выполненные дела</div>
                        <div class="col-md-2 toolt">
                            {{ $manager['all']['complete']->count() }}
                            <span class="toolttext">
                                 @foreach($manager['all']['complete']->get() as $task)
                                    <a href="/tasks/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;
                                @endforeach
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10">Дел было поставлено</div>
                        <div class="col-md-2 toolt">
                            {{ $manager['all']['sets']->count() }}
                            <span class="toolttext">
                                 @foreach($manager['all']['sets']->get() as $task)
                                    <a href="/tasks/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;
                                @endforeach
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10">Перенесенные дела</div>
                        <div class="col-md-2 toolt">
                            {{ $manager['all']['transfer']->count() }}
                            <span class="toolttext">
                                @foreach($manager['all']['transfer']->get() as $task)
                                    <a href="/tasks/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;
                                @endforeach
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10">Поступило дел</div>
                        <div class="col-md-2 toolt">
                            {{ $manager['all']['new']->count() }}
                            <span class="toolttext">
                                @foreach($manager['all']['new']->get() as $task)
                                    <a href="/tasks/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;
                                @endforeach
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <b>Статистика по статусам</b>
                    @php
                        $cartStat = \App\Models\CartStatus::get();
                    @endphp
                    @foreach($manager['statuses'] as $status=>$orders)
                        @php
                            $stat = $cartStat->where('slug', $status)->first();
                        @endphp
                        <div class="row">
                            <div
                                class="col-md-10">{!!  $stat ? '<span style="color: '.$stat->color.'">'.$stat->name.'</span>' : 'Без статуса' !!}</div>
                            <div class="col-md-2 toolt">
                                {{ count($orders) }}
                                <span class="toolttext">
                                    @foreach($orders as $order)
                                        <a href="/order_news/{{ $order['id'] }}/edit" target="_blank">{{ $order['id'] }}</a>&nbsp;
                                    @endforeach
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
                <div class="col-md-3">
                    <b>Сложность дел</b>
                    <div class="row">
                        <div class="col-md-10">Сложная</div>
                        <div class="col-md-2 toolt">
                            {{ $manager['complexity'][2]->count() }}
                            <span class="toolttext">
                                @foreach($manager['complexity'][2]->get() as $task)
                                    <a href="/tasks/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;
                                @endforeach
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10">Средняя</div>
                        <div class="col-md-2 toolt">
                            {{ $manager['complexity'][1]->count() }}
                            <span class="toolttext">
                                @foreach($manager['complexity'][1]->get() as $task)
                                    <a href="/tasks/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;
                                @endforeach
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10">Легкая</div>
                        <div class="col-md-2 toolt">
                            {{ $manager['complexity'][0]->count() }}
                            <span class="toolttext">
                                @foreach($manager['complexity'][0]->get() as $task)
                                    <a href="/tasks/{{ $task['id'] }}/edit" target="_blank">{{ $task['id'] }}</a>&nbsp;
                                @endforeach
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <b>Типы дел</b>
                    @php
                        $taskTyp = \App\Models\TaskType::get();
                    @endphp
                    @foreach($manager['types'] as $type=>$tasks)
                        @php
                            $t = $taskTyp->where('slug', $type)->first();
                        @endphp
                        <div class="row">
                            <div
                                class="col-md-10">{!!  $t ? '<span style="color: '.$t->color.'">'.$t->name.'</span>' : 'Без типа' !!}</div>
                            <div class="col-md-2 toolt">
                                {{ count($tasks) }}
                                <span class="toolttext">
                                    @foreach($tasks as $ta)
                                        <a href="/tasks/{{ $ta['id'] }}/edit" target="_blank">{{ $ta['id'] }}</a>&nbsp;
                                    @endforeach
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
                <div class="col-md-3">
                    <b>Статистика по категориям</b>
                    <div class="row">
                        <div class="col-md-10">Обработано дел по А</div>
                        <div class="col-md-2 toolt">
                            {{ $manager['category']['A']->count() }}
                            <span class="toolttext">
                                @foreach($manager['category']['A']->get() as $task)
                                    <a href="/tasks/{{ $task->id }}/edit" target="_blank">{{ $task->id }}</a>&nbsp;
                                @endforeach
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10">Обработано дел по В</div>
                        <div class="col-md-2 toolt">
                            {{ $manager['category']['B']->count() }}
                            <span class="toolttext">
                                 @foreach($manager['category']['B']->get() as $task)
                                    <a href="/tasks/{{ $task->id }}/edit" target="_blank">{{ $task->id }}</a>&nbsp;
                                @endforeach
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10">Обработано дел по С</div>
                        <div class="col-md-2 toolt">
                            {{ $manager['category']['C']->count() }}
                            <span class="toolttext">
                                @foreach($manager['category']['C']->get() as $task)
                                    <a href="/tasks/{{ $task->id }}/edit" target="_blank">{{ $task->id }}</a>&nbsp;
                                @endforeach
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10">Обработано дел по D</div>
                        <div class="col-md-2 toolt">
                            {{ $manager['category']['D']->count() }}
                            <span class="toolttext">
                                @foreach($manager['category']['D']->get() as $task)
                                    <a href="/tasks/{{ $task->id }}/edit" target="_blank">{{ $task->id }}</a>&nbsp;
                                @endforeach
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endforeach

@livewireScripts
</body>
</html>
