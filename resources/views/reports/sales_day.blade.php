<!doctype html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Lead</title>
    <link media="all" type="text/css" rel="stylesheet" href="/packages/sleepingowl/default/css/admin-app.css">
    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.8.2/dist/alpine.min.js" defer></script>
    @livewireStyles
</head>
<body class="bg-white">


<div class="row">

    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}"  @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Лиды новые обработано</p>
                <h3 @click="show=!show" style="cursor: pointer">{{ count($allAuditLeads) }}</h3>
            </div>
            <div x-show="show" style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">
                @if($allAuditLeads)
                @foreach($allAuditLeads as $lead)
                    <a href="/order_calls/{{ $lead['auditable_id'] }}/edit" target="_blank">{{ $lead['auditable_id'] }}</a>&nbsp;
                @endforeach
                @else
                    Нет лидов
                @endif
            </div>
        </div>
    </div>


    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}"  @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Лиды старые обработано</p>
                <h3 @click="show=!show" style="cursor: pointer">{{ array_sum(\Arr::pluck($users, 'oldLeadsCount')) }}</h3>
            </div>
            <div x-show="show" style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">
                @foreach(Arr::flatten(\Arr::pluck($users, 'oldLeads')) as $lead)
                    <a href="/order_calls/{{ $lead->id }}/edit" target="_blank">{{ $lead->id }}</a>&nbsp;
                @endforeach
            </div>
        </div>
    </div>


    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}"  @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Договора</p>
                <h3 @click="show=!show" style="cursor: pointer">{{ array_sum(\Arr::pluck($users, 'transferCount')) }}</h3>
            </div>
            <div x-show="show" style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">
                @foreach(\Arr::collapse((\Arr::pluck($users, 'transfer'))) as $lead)
                    <a href="/order_calls/{{ $lead['id'] }}/edit" target="_blank">{{ $lead['id'] }}</a>&nbsp;
                @endforeach
            </div>
        </div>
    </div>


    <div class="col-xl-2 col-lg-4 col-sm-6">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Оборот</p>
                <h3>{{ array_sum(\Arr::pluck($users, 'turnover')) }}</h3>
            </div>
        </div>
    </div>


    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}"  @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Отправлено на оплату</p>
                <h3 @click="show=!show" style="cursor: pointer">{{ array_sum(\Arr::pluck($users, 'sendpayCount')) }}</h3>
            </div>
            <div x-show="show" style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">
                @foreach(\Arr::collapse((\Arr::pluck($users, 'sendpay'))) as $lead)
                    <a href="/order_calls/{{ $lead['id'] }}/edit" target="_blank">{{ $lead['id'] }}</a>&nbsp;
                @endforeach
            </div>
        </div>
    </div>


    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}"  @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Рассрочек отправлено</p>
                <h3 @click="show=!show" style="cursor: pointer">{{ array_sum(\Arr::pluck($users, 'creditCount')) }}</h3>
            </div>
            <div x-show="show" style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">
                @foreach(\Arr::collapse((\Arr::pluck($users, 'credit'))) as $lead)
                    <a href="/order_calls/{{ $lead['id'] }}/edit" target="_blank">{{ $lead['id'] }}</a>&nbsp;
                @endforeach
            </div>
        </div>
    </div>


    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}"  @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Рассрочек одобрено</p>
                <h3 @click="show=!show" style="cursor: pointer">{{ array_sum(\Arr::pluck($users, 'creditOkCount')) }}</h3>
            </div>
            <div x-show="show" style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">
                @foreach(\Arr::collapse((\Arr::pluck($users, 'creditOk'))) as $lead)
                    <a href="/order_calls/{{ $lead['id'] }}/edit" target="_blank">{{ $lead['id'] }}</a>&nbsp;
                @endforeach
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}"  @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Дел на сегодня</p>
                <h3 @click="show=!show" style="cursor: pointer">{{ array_sum(\Arr::pluck($users, 'nowCount')) }}</h3>
            </div>
            <div x-show="show" style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">
                @foreach(\Arr::collapse((\Arr::pluck($users, 'now'))) as $lead)
                    <a href="/order_calls/{{ $lead['id'] }}/edit" target="_blank">{{ $lead['id'] }}</a>&nbsp;
                @endforeach
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}"  @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Лидов пришло за день</p>
                <h3 @click="show=!show" style="cursor: pointer">{{ count($newLeadsOnDay) }}</h3>
            </div>
            <div x-show="show" style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">
                @if(count($newLeadsOnDay))
                @foreach($newLeadsOnDay as $lead)
                    <a href="/order_calls/{{ $lead['id'] }}/edit" target="_blank">{{ $lead['id'] }}</a>&nbsp;
                @endforeach
                    @else
                        Нет лидов
                    @endif
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}"  @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Отказы по новым лидам</p>
                <h3 @click="show=!show" style="cursor: pointer">{{ count($rejectLeads) }}</h3>
            </div>
            <div x-show="show" style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">
                @if(count($rejectLeads))
                @foreach($rejectLeads as $lead)
                    <a href="/order_calls/{{ $lead['id'] }}/edit" target="_blank">{{ $lead['id'] }}</a>&nbsp;
                @endforeach
                    @else
                        Нет лидов
                    @endif
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}"  @click.away="show=false">
        <div class="small-box bg-green">
            <div class="inner">
                <p>Недозвоны по новым лидам</p>
                <h3 @click="show=!show" style="cursor: pointer">{{ count($noanswerLeads) }}</h3>
            </div>
            <div x-show="show" style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">
                @if(count($noanswerLeads))
                @foreach($noanswerLeads as $lead)
                    <a href="/order_calls/{{ $lead['id'] }}/edit" target="_blank">{{ $lead['id'] }}</a>&nbsp;
                @endforeach
                    @else
                        Нет лидов
                    @endif
            </div>
        </div>
    </div>


    {{--    <div class="col-xl-2 col-lg-4 col-sm-6">--}}
    {{--        <div class="small-box bg-green">--}}
    {{--            <div class="inner">--}}
    {{--                <p>Замеров</p>--}}
    {{--                <h3>{{ array_sum(\Arr::pluck($users, 'measure')) }}</h3>--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--    </div>--}}
    {{--    --}}
    {{--    <div class="col-xl-2 col-lg-4 col-sm-6">--}}
    {{--        <div class="small-box bg-green">--}}
    {{--            <div class="inner">--}}
    {{--                <p>СУПЕРПАКЕТ</p>--}}
    {{--                <h3>{{ array_sum(\Arr::pluck($users, 'super')) }}</h3>--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--    </div>--}}

    {{--    <div class="col-xl-2 col-lg-4 col-sm-6">--}}
    {{--        <div class="small-box bg-green">--}}
    {{--            <div class="inner">--}}
    {{--                <p>Дропшипинг</p>--}}
    {{--                <h3>{{ array_sum(\Arr::pluck($users, 'dropship')) }}</h3>--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--    </div>--}}


    @if($key == 'ekb')
        <div class="col-xl-2 col-lg-4 col-sm-6" x-data="{show: false}"  @click.away="show=false">
            <div class="small-box bg-green">
                <div class="inner">
                    <p>Ждем на встречу</p>
                    <h3 @click="show=!show" style="cursor: pointer">{{ array_sum(\Arr::pluck($users, 'meetCount')) }}</h3>
                </div>
            </div>
            <div x-show="show" style="position: absolute; z-index: 1000; background-color: white; overflow-y: scroll; height: 150px; width: 100%; display: none; padding: 4px;">
                @foreach(\Arr::collapse((\Arr::pluck($users, 'meet'))) as $lead)
                    <a href="/order_calls/{{ $lead['id'] }}/edit" target="_blank">{{ $lead['id'] }}</a>&nbsp;
                @endforeach
            </div>
        </div>
    @endif
</div>

<div class="row mb-5">
    @foreach($reportByСlass as $k=>$class)
        <div class="col-2">
            <div class="text-center h3 bg-{{$class['color']}} color-white">{{ $k }}&nbsp;</div>
            <table width="100%">
                <tr>
                    <td width="90%">Оборот</td>
                    <td>{{ $class['turnover'] }}</td>
                </tr>
                <tr>
                    <td>Лиды</td>
                    <td>{{ $class['leads'] }}</td>
                </tr>
                <tr>
                    <td>Договора</td>
                    <td>{{ $class['transfer'] }}</td>
                </tr>
                <tr>
                    <td>Рассрочек отпр.</td>
                    <td>{{ $class['credit'] }}</td>
                </tr>
                <tr>
                    <td>Отказы</td>
                    <td>{{ $class['reject'] }}</td>
                </tr>
                <tr>
                    <td>Недозвоны</td>
                    <td>{{ $class['noanswer'] }}</td>
                </tr>
                <tr>
                    <td>Отправлено на оплату</td>
                    <td>{{ $class['sendpay'] }}</td>
                </tr>
            </table>
        </div>
    @endforeach
</div>

<div class="row mb-5">
    @foreach($users as $user)
        <div class="col-6 card" style="width: 18rem;">
            <div class="card-body position-relative">
            @if(!empty($user['class']))
                <div class="position-absolute" style="right: 0;">
                @foreach(explode(',',$user['class']) as $user_class)
                    <span class="text-white badge badge-{{ optional($user['classColor'])[$user_class] }}">{{ $user_class }}</span>
                @endforeach
                </div>
            @endif
                <div class="row">
                    <div class="col-12">
                        <h2 title="{{ $user['id'] }}">{{ $user['fi'] }}</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <h4>Кол-во дел на сегодня</h4>
                        <span class="text-danger h5"><i class="fas fa-history"></i> {{ $user['nowCount'] }}</span>
                        <h4>Кол-во дел на завтра</h4>
                        <span class="text-danger h5"><i class="fas fa-history"></i> {{ $user['tomorrow'] }}</span>
                    </div>
                    <div class="col font">
                        <h2>Твой результат</h2>
                        <span class="text-danger h1"><i
                                class="fas fa-chart-line"></i>   {{ $user['turnover'] }} р.</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        Лидов:
                        <div class="row">
                            <div class="col">
                                <div class="progress">
                                    <div
                                        class="progress-bar bg-{{  $user['leadsPercentColor'] }} progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        aria-valuenow="{{ $user['leadsPercent'] }}" aria-valuemin="0"
                                        aria-valuemax="100"
                                        style="width: {{ $user['leadsPercent'] }}%">{{ $user['leadsCount'] }}</div>
                                </div>
                            </div>
                            <div class="col-2 text-{{  $user['leadsPercentColor'] }}">{{ $user['leadsCount'] }}</div>
                        </div>
                        Старых лидов:
                        <div class="row">
                            <div class="col">
                                <div class="progress">
                                    <div class="progress-bar bg-success progress-bar-striped progress-bar-animated"
                                         role="progressbar"
                                         aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"
                                         style="width: {{ $user['leadsPercent'] }}%">{{ count($user['oldLeads']) }}</div>
                                </div>
                            </div>
                            <div class="col-2 text-success">{{ count($user['oldLeads']) }}</div>
                        </div>

                        Договоров:
                        <div class="row">
                            <div class="col">
                                <div class="progress">
                                    <div
                                        class="progress-bar bg-{{ $user['transferPercentColor'] }} progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        aria-valuenow="{{ $user['transferPercent'] }}"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        style="width: {{ $user['transferPercent'] }}%">{{ $user['transferCount'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-2 text-{{ $user['transferPercentColor'] }}">{{ $user['transferCount'] }}</div>
                        </div>
                        Оборот:
                        <div class="row">
                            <div class="col">
                                <div class="progress">
                                    <div
                                        class="progress-bar bg-{{ $user['turnoverPercentColor'] }} progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        aria-valuenow="{{ $user['turnoverPercent'] }}"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        style="width: {{ $user['turnoverPercent'] }}%">
                                        {{ $user['turnover'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-2 text-{{ $user['turnoverPercentColor'] }}">{{ $user['turnover'] }}</div>
                        </div>
                        Отправлен на оплату:
                        <div class="row">
                            <div class="col">
                                <div class="progress">
                                    <div
                                        class="progress-bar bg-{{  $user['sendpayPercentColor'] }} progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        aria-valuenow="{{ $user['sendpayPercent'] }}"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        style="width: {{ $user['sendpayPercent'] }}%">{{ $user['sendpayCount'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-2 text-{{ $user['sendpayPercentColor'] }}">{{ $user['sendpayCount'] }}</div>
                        </div>
{{--                        Замер:--}}
{{--                        <div class="row">--}}
{{--                            <div class="col">--}}
{{--                                <div class="progress">--}}
{{--                                    <div--}}
{{--                                        class="progress-bar  bg-{{ $user['measurePercentColor'] }} progress-bar-striped progress-bar-animated"--}}
{{--                                        role="progressbar"--}}
{{--                                        aria-valuenow="{{ $user['measurePercent'] }}"--}}
{{--                                        aria-valuemin="0"--}}
{{--                                        aria-valuemax="100"--}}
{{--                                        style="width: {{ $user['measurePercent'] }}%"> {{ $user['measure'] }}--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                            <div class="col-2 text-{{ $user['measurePercentColor'] }}">{{ $user['measure'] }}</div>--}}
{{--                        </div>--}}
                        Рассрочка:
                        <div class="row">
                            <div class="col">
                                <div class="progress">
                                    <div
                                        class="progress-bar  bg-{{ $user['creditPercentColor'] }} progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        aria-valuenow="{{ $user['creditPercent'] }}"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        style="width: {{ $user['creditPercent'] }}%"> {{ $user['creditCount'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-2 text-{{ $user['creditPercentColor'] }}">{{ $user['creditCount'] }}</div>
                        </div>
                        Рассрочек одобрено:
                        <div class="row">
                            <div class="col">
                                <div class="progress">
                                    <div
                                        class="progress-bar  bg-{{ $user['creditOkPercentColor'] }} progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        aria-valuenow="{{ $user['creditOkPercent'] }}"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        style="width: {{ $user['creditOkPercent'] }}%"> {{ $user['creditOkCount'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-2 text-{{ $user['creditOkPercentColor'] }}">{{ $user['creditOkCount'] }}</div>
                        </div>
{{--                        СУПЕРПАКЕТ:--}}
{{--                        <div class="row">--}}
{{--                            <div class="col">--}}
{{--                                <div class="progress">--}}
{{--                                    <div--}}
{{--                                        class="progress-bar  bg-{{ $user['superPercentColor'] }} progress-bar-striped progress-bar-animated"--}}
{{--                                        role="progressbar"--}}
{{--                                        aria-valuenow="{{ $user['superPercent'] }}"--}}
{{--                                        aria-valuemin="0"--}}
{{--                                        aria-valuemax="100"--}}
{{--                                        style="width: {{ $user['superPercent'] }}%"> {{ $user['super'] }}--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                            <div class="col-2 text-{{ $user['superPercentColor'] }}">{{ $user['super'] }}</div>--}}
{{--                        </div>--}}
{{--                        Дропшиппинг:--}}
{{--                        <div class="row">--}}
{{--                            <div class="col">--}}
{{--                                <div class="progress">--}}
{{--                                    <div--}}
{{--                                        class="progress-bar  bg-{{ $user['dropPercentColor'] }} progress-bar-striped progress-bar-animated"--}}
{{--                                        role="progressbar"--}}
{{--                                        aria-valuenow="{{ $user['dropPercent'] }}"--}}
{{--                                        aria-valuemin="0"--}}
{{--                                        aria-valuemax="100"--}}
{{--                                        style="width: {{ $user['dropPercent'] }}%"> {{ $user['drop'] }}--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                            <div class="col-2 text-{{ $user['dropPercentColor'] }}">{{ $user['drop'] }}</div>--}}
{{--                        </div>--}}
                        @if($key == 'ekb')
                            Ждем на встречу:
                            <div class="row">
                                <div class="col">
                                    <div class="progress">
                                        <div
                                            class="progress-bar  bg-{{ $user['meetPercentColor'] }} progress-bar-striped progress-bar-animated"
                                            role="progressbar"
                                            aria-valuenow="{{ $user['meetPercent'] }}"
                                            aria-valuemin="0"
                                            aria-valuemax="100"
                                            style="width: {{ $user['meetPercent'] }}%"> {{ $user['meetCount'] }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-2 text-{{ $user['meetPercentColor'] }}">{{ $user['meetCount'] }}</div>
                            </div>
                        @endif
                        Время работы:
                        <div class="row">
                            <div class="col">
                                <div class="progress">
                                    <div
                                        class="progress-bar  bg-{{ $user['workPercentColor'] }} progress-bar-striped progress-bar-animated"
                                        role="progressbar"
                                        aria-valuenow="{{ $user['workPercent'] }}"
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                        style="width: {{ $user['workPercent'] }}%"> {{ $user['work'] }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-2 text-{{ $user['workPercentColor'] }}">{{ $user['work'] }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card col-6" id="user{{ $user['id'] }}">
            <h4>Лиды 🔥:</h4>
            <div style="height: 200px;">
                <div x-data="{open: true}" class="card card-primary card-outline card-outline-tabs">
                    <div class="card-header p-0 border-bottom-0">
                        <ul class="nav nav-tabs" id="custom-tabs-four-tab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link"
                                   :class="{'active': open == true}"
                                   @click="open=true"
                                   id="custom-tabs-four-home-tab"
                                   data-toggle="pill"
                                   href="#custom-tabs-four-home"
                                   role="tab"
                                   aria-controls="custom-tabs-four-home"
                                   aria-selected="false">Сегодня ({{ count($user['fireLeadsNow']) }})</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link"
                                   :class="{'active': open == false}"
                                   @click="open=false"
                                   id="custom-tabs-four-profile-tab"
                                   data-toggle="pill"
                                   href="#custom-tabs-four-profile"
                                   role="tab"
                                   aria-controls="custom-tabs-four-profile"
                                   aria-selected="false">Завтра ({{ count($user['fireLeads']) }})</a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body overflow-auto" style="height: 170px;">
                        <div class="tab-content" id="custom-tabs-four-tabContent">
                            <div class="tab-pane"
                                 :class="{ 'fade active show': open == true }"
                                 id="custom-tabs-four-home" role="tabpanel" aria-labelledby="custom-tabs-four-home-tab">
                                <table width="100%" class="table h-100">
                                    <thead>
                                    <tr>
                                        <td>Лид</td>
                                        {{--                        <td>Товар</td>--}}
                                        <td>Город</td>
                                        <td>Сумма</td>
                                        <td>Статус</td>
                                        <td>Вероятность</td>
                                    </tr>
                                    </thead>
                                    @foreach($user['fireLeadsNow'] as $lead)
                                        <tr>
                                            <td><a href="/order_calls/{{ $lead->id }}/edit"
                                                   target="_blank">{{ $lead->id }}</td>
                                            {{--                            <td>0</td>--}}
                                            <td>{{ $lead->city }}</td>
                                            <td>{{ $lead->good_price }}</td>
                                            <td>
                                                {{ $lead->order_status->name }}<br>
                                                @if($lead->order_status->slug == 'REJECT')
                                                    <span style="font-weight: bold; font-size: 12px;">{{ $lead->reason->title }}</span><br>
                                                    <span style="font-size: 12px;">{{ $lead->terms }}</span>
                                                @endif
                                                @if(in_array($lead->order_status->slug, ['SENDPAY','INFO5','INFO4']))
                                                    <span style="font-size: 12px;">{{ $lead->action }}</span>
                                                @endif
                                            </td>
                                            <td class="text-center">{{ $lead->probability.'%' ?: '-' }}</td>
                                        </tr>
                                    @endforeach
                                </table>
                            </div>
                            <div class="tab-pane" :class="{ 'fade active show': open == false}"
                                 id="custom-tabs-four-profile" role="tabpanel"
                                 aria-labelledby="custom-tabs-four-profile-tab">
                                <table width="100%" class="table h-100">
                                    <thead>
                                    <tr>
                                        <td>Лид</td>
                                        {{--                        <td>Товар</td>--}}
                                        <td>Город</td>
                                        <td>Сумма</td>
                                        <td>Статус</td>
                                        <td>Вероятность</td>
                                    </tr>
                                    </thead>
                                    @foreach($user['fireLeads'] as $lead)
                                        <tr>
                                            <td><a href="/order_calls/{{ $lead->id }}/edit"
                                                   target="_blank">{{ $lead->id }}</td>
                                            {{--                            <td>0</td>--}}
                                            <td>{{ $lead->city }}</td>
                                            <td>{{ $lead->good_price }}</td>
                                            <td>
                                                {{ $lead->order_status->name }}<br>
                                                @if($lead->order_status->slug == 'REJECT')
                                                    <span style="font-weight: bold; font-size: 12px;">{{ $lead->reason->title }}</span><br>
                                                    <span style="font-size: 12px;">{{ $lead->terms }}</span>
                                                @endif
                                                @if(in_array($lead->order_status->slug, ['SENDPAY','INFO5','INFO4']))
                                                    <span style="font-size: 12px;">{{ $lead->action }}</span>
                                                @endif
                                            </td>
                                            <td class="text-center">{{ $lead->probability.'%' ?: '-' }}</td>
                                        </tr>
                                    @endforeach
                                </table>
                            </div>
                        </div>
                    </div>
                    <!-- /.card -->
                </div>
            </div>
            <h4 class="mt-4">Все лиды ({{ count($user['leads']) }}):</h4>
            <div class="overflow-hidden" style="height: 100%;">
                <div x-data="{open: true}" class="card card-primary card-outline card-outline-tabs"
                     style="height: 100%;">
                    <div class="card-header p-0 border-bottom-0">
                        <ul class="nav nav-tabs" id="custom-tabs-four-tab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link"
                                   :class="{'active': open == true}"
                                   @click="open=true"
                                   id="custom-tabs-four-home-tab"
                                   data-toggle="pill"
                                   href="#custom-tabs-four-home"
                                   role="tab"
                                   aria-controls="custom-tabs-four-home"
                                   aria-selected="false">Новые ({{ count($user['leads']) }})</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link"
                                   :class="{'active': open == false}"
                                   @click="open=false"
                                   id="custom-tabs-four-profile-tab"
                                   data-toggle="pill"
                                   href="#custom-tabs-four-profile"
                                   role="tab"
                                   aria-controls="custom-tabs-four-profile"
                                   aria-selected="false">Старые ({{ count($user['oldLeads']) }})</a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body overflow-auto" style="height: 170px;">
                        <div class="tab-content" id="custom-tabs-four-tabContent">
                            <div class="tab-pane"
                                 :class="{ 'fade active show': open == true }"
                                 id="custom-tabs-four-home" role="tabpanel" aria-labelledby="custom-tabs-four-home-tab">
                                <table width="100%" class="table h-100">
                                    <thead>
                                    <tr>
                                        <td>Лид</td>
                                        {{--                        <td>Товар</td>--}}
                                        <td>Город</td>
                                        <td>Сумма</td>
                                        <td>Статус</td>
                                        <td>Вероятность</td>
                                    </tr>
                                    </thead>
                                    @foreach($user['leads'] as $lead)
                                        <tr>
                                            <td><a href="/order_calls/{{ $lead->id }}/edit"
                                                   target="_blank">{{ $lead->id }}</td>
                                            {{--                            <td>0</td>--}}
                                            <td>{{ $lead->city }}</td>
                                            <td>{{ $lead->good_price }}</td>
                                            <td>
                                                {{ $lead->order_status->name }}<br>
                                                @if($lead->order_status->slug == 'REJECT')
                                                    <span style="font-weight: bold; font-size: 12px;">{{ $lead->reason->title }}</span><br>
                                                    <span style="font-size: 12px;">{{ $lead->terms }}</span>
                                                @endif
                                                @if(in_array($lead->order_status->slug, ['SENDPAY','INFO5','INFO4']))
                                                    <span style="font-size: 12px;">{{ $lead->action }}</span>
                                                @endif
                                            </td>
                                            <td class="text-center">{{ $lead->probability.'%' ?: '-' }}</td>
                                        </tr>
                                    @endforeach
                                </table>
                            </div>
                            <div class="tab-pane" :class="{ 'fade active show': open == false}"
                                 id="custom-tabs-four-profile" role="tabpanel"
                                 aria-labelledby="custom-tabs-four-profile-tab">
                                <table width="100%" class="table h-100">
                                    <thead>
                                    <tr>
                                        <td>Лид</td>
                                        {{--                        <td>Товар</td>--}}
                                        <td>Город</td>
                                        <td>Сумма</td>
                                        <td>Статус</td>
                                        <td>Вероятность</td>
                                    </tr>
                                    </thead>
                                    @foreach($user['oldLeads'] as $lead)
                                        <tr>
                                            <td><a href="/order_calls/{{ $lead->id }}/edit"
                                                   target="_blank">{{ $lead->id }}</td>
                                            {{--                            <td>0</td>--}}
                                            <td>{{ $lead->city }}</td>
                                            <td>{{ $lead->good_price }}</td>
                                            <td>
                                                {{ $lead->order_status->name }}<br>
                                                @if($lead->order_status->slug == 'REJECT')
                                                    <span style="font-weight: bold; font-size: 12px;">{{ $lead->reason->title }}</span><br>
                                                    <span style="font-size: 12px;">{{ $lead->terms }}</span>
                                                @endif
                                                @if(in_array($lead->order_status->slug, ['SENDPAY','INFO5','INFO4']))
                                                    <span style="font-size: 12px;">{{ $lead->action }}</span>
                                                @endif
                                            </td>
                                            <td class="text-center">{{ $lead->probability.'%' ?: '-' }}</td>
                                        </tr>
                                    @endforeach
                                </table>
                            </div>
                        </div>
                    </div>
                    <!-- /.card -->
                </div>


                {{--                <table width="100%" class="table h-100">--}}
                {{--                    <thead>--}}
                {{--                    <tr>--}}
                {{--                        <td>Лид</td>--}}
                {{--                        <td>Товар</td>--}}
                {{--                        <td>Город</td>--}}
                {{--                        <td>Сумма</td>--}}
                {{--                        <td>Статус</td>--}}
                {{--                        <td>Вероятность</td>--}}
                {{--                    </tr>--}}
                {{--                    </thead>--}}
                {{--                    @foreach($user['leads'] as $lead)--}}
                {{--                        <tr>--}}
                {{--                            <td><a href="/order_calls/{{ $lead->id }}/edit" target="_blank">{{ $lead->id }}</td>--}}
                {{--                            <td>0</td>--}}
                {{--                            <td>{{ $lead->city }}</td>--}}
                {{--                            <td>{{ $lead->good_price }}</td>--}}
                {{--                            <td>{{ $lead->order_status->name }}</td>--}}
                {{--                            <td class="text-center">{{ $lead->probability.'%' ?: '-' }}</td>--}}
                {{--                        </tr>--}}
                {{--                    @endforeach--}}
                {{--                </table>--}}
            </div>
        </div>

    @endforeach
</div>

<livewire:report-to-do :reportId="$report_id" type="0" :franchisee="$key"/>
@livewireScripts
</body>
</html>
