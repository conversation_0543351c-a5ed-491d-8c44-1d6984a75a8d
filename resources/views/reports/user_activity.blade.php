@if(auth()->user()->isAdmin() || auth()->user()->hasRole(['topsales','subdostavka']))
    @foreach($years as $year)
        <div class="input-group mb-2">
            <div class="input-group-prepend">
                <a tabindex="0" role="button" {!! !$current && $current_year == $year ? 'aria-pressed="true"' : '' !!} class="btn btn-danger {{ !$current && $current_year == $year ? 'active' : '' }}" href="/user_activity/{{ $year }}">{{ $year }}</a>
            </div>
            <div class="btn-group">
                @foreach($months as $k => $month)
                    <a tabindex="0" role="button" {!! $current_year == $year && $current && $k == $current-1 ? 'aria-pressed="true"' : '' !!} class="btn btn-default btn-light {{ $current_year == $year && $current && $k == $current-1 ? 'active' : '' }}" href="/user_activity/{{ $year }}/{{ $k+1 }}">{{ $month }}</a>
                @endforeach
            </div>
        </div>
    @endforeach
    <hr>
@endif

@php
    $dt = \Carbon\Carbon::createFromDate($current_year, $current);
@endphp

<h2>Активность пользователей за {{ $current ? $months[$current - 1] : '' }} {{ $current_year }}</h2>
<table class="table table-bordered table-sm table-striped table-hover">
    <tr>
        <th></th>
        @for($i=1;$i<=$dt->daysInMonth;$i++)
            <th class="text-center text-sm">{{ $i }}</th>
        @endfor
        <th class="text-red">Итого</th>
    </tr>

    @foreach($users as $wd_user)
        <tr>
            <td>
                <div class="py-1" style="padding-left: 40px; position:relative;">
                    <div style="position:absolute;left: 4px;top: 4px; height: 30px; width: 30px; overflow: hidden; border-radius: 50%;">
                        <img style="width: 100%; height: auto;min-width: 100%; min-height: 100%;" src="{{ $wd_user['image'] }}" alt=""/>
                    </div>
                    @foreach($wd_user['class'] as $u_class){!! $u_class !!}@endforeach
                    {{ $wd_user['name'] }}
                    <div class="text-muted small">{{ $wd_user['role'] ?? '-' }}</div>
                </div>
            </td>
            @for($i=1;$i<=$dt->daysInMonth;$i++)
                <td class="text-center text-sm">{{ $wd_user['by_day'][$i] ?? '-' }}</td>
            @endfor
            <th class="text-red">{{ $wd_user['count'] }}</th>
        </tr>
    @endforeach
</table>