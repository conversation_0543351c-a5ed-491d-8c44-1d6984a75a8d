@foreach($catalogs as $catalog)
    <?php
        $ids = array_merge($catalog->descendants->pluck('id')->all(),[$catalog->id]);
        $lead = $leads->whereIn('catalog_id', $ids);
        $order = $orders->whereIn('lead_id', $lead->pluck('id')->all());
        $conv = $lead->count() > 0 ? 100 * $order->count() / $lead->count() : 0;
        $status = '';
        if ($order->count() > 2) {
            if ($conv > 10) $status = 'class="bg-green"';
            if ($conv > 20) $status = 'class="bg-yellow"';
        }
    ?>
    <tr style="font-weight: {{900-$catalog->depth*($is_children?200:300)}};" {!! $status !!}>
        <td>{!! str_repeat('&nbsp;',$catalog->depth*2) !!}{{ $catalog->title }}</td>
        <td align="center">{{ $lead->count() }}</td>
        <td align="center">{{ $order->count() }}</td>
        <td align="center">{{ number_format($conv, 2,'.',' ') }}%</td>
    </tr>

    @if($catalog->children->count())
        @if(!$is_children)
            @if($catalog->children->first()->children->count())
                @include('reports.parts.region_catalog_tree_item',['catalogs' => $catalog->children])
            @endif
        @else
            @include('reports.parts.region_catalog_tree_item',['catalogs' => $catalog->children])
        @endif
    @endif
@endforeach