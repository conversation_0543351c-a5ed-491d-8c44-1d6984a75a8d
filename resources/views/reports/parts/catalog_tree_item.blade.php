@foreach($catalogs as $catalog)
    <?php
        $ids = array_merge($catalog->descendants->pluck('id')->all(),[$catalog->id]);
        $leads = $cats->whereIn('catalog', $ids);
    ?>
    <tr style="font-weight: {{900-$catalog->depth*300}};">
        <td title="{{ $catalog->descendants->pluck('title')->implode("\n") }}">{!! str_repeat('&nbsp;',$catalog->depth*2) !!}{{ $catalog->title }}</td>
    @if($current)<td>{{ number_format(optional($plan_sales)[$catalog->id], 0,'.',' ') }}</td>@endif
        <td class="text-nowrap">{!!number_format($leads->sum('carts'),0,',',' ')!!}</td>
    @if($current)<td>{{ number_format(optional($plan_cats)[$catalog->id], 0,'.',' ') }}</td>@endif
        <td title="{{ $leads->pluck('id')->implode(', ') }}">{{ $leads->count() }}</td>
        <td>{{ $leads->where('order','>',0)->count() }}</td>
    @if($current)<td>{{ number_format(optional($plan_conv)[$catalog->id],1,'.',' ') }}</td>@endif
        <td>{{ round($leads->count() > 0 ? 100 * $leads->where('order','>',0)->count() / $leads->count() : 0, 2) }}</td>
    @if($current)<td>{{ number_format(optional($plan_average)[$catalog->id], 0,'.',' ') }}</td>@endif
        <td class="text-nowrap">{!!$leads->where('order','>',0)->count() ? number_format($leads->sum('carts')/$leads->where('order','>',0)->count() ?? 0,0,',',' ') : 0 !!}</td>
    </tr>

    @if($catalog->children->count() && $catalog->children->first()->children->count())
        @include('reports.parts.catalog_tree_item',['catalogs' => $catalog->children])
    @endif
@endforeach