@foreach($catalogs as $catalog)
    <?php
        $ids = array_merge($catalog->descendants->pluck('id')->all(),[$catalog->id]);
    ?>
    <tr style="font-weight: {{900-$catalog->depth*300}};">
        <td class="fixed-first text-nowrap" title="{{ $catalog->descendants->pluck('title')->implode("\n") }}">{!! str_repeat('&nbsp;',$catalog->depth*2) !!}{{ $catalog->title }}</td>
        @foreach($regions as $region)
            <?php
                $class_color = '';
                $supps = '';
                if ($catalog->children->count() && !$catalog->children->first()->children->count()) {
                    $supps = $suppliers[$region->id] ?? [];
                    $supps = array_filter($supps, fn($item) => in_array($item, $ids), ARRAY_FILTER_USE_KEY);
                    $supps = array_sum(array_values($supps));
                    switch ($supps) {
                        case 0: $class_color = ''; break;
                        case 1: $class_color = 'bg-danger'; break;
                        case 2:case 3: $class_color = 'bg-warning'; break;
                        case 4:case 5: $class_color = 'bg-success'; break;
                        default: $class_color = 'bg-primary';
                    }
                }
            ?>
            <td class="{{ $class_color }}" align="center">{{ $supps }}</td>
        @endforeach
    </tr>

    @if($catalog->children->count() && $catalog->children->first()->children->count())
        @include('reports.parts.catalog_tree_supplier_item',['catalogs' => $catalog->children])
    @endif
@endforeach