<template>
    <div class="body">
        <div id="frame">
            <div id="sidepanel">
                <!--                <div id="profile">
                                    <div class="wrap">
                                        <img id="profile-img" :src="user.image" class="online" alt="" />
                                        <p>{{ user.name }}</p>
                                        <i class="fa fa-chevron-down expand-button" aria-hidden="true"></i>
                                        <div id="status-options" v-if="statusOptions">
                                            <ul>
                                                <li id="status-online" class="active"><span class="status-circle"></span> <p>Online</p></li>
                                                <li id="status-away"><span class="status-circle"></span> <p>Away</p></li>
                                                <li id="status-busy"><span class="status-circle"></span> <p>Busy</p></li>
                                                <li id="status-offline"><span class="status-circle"></span> <p>Offline</p></li>
                                            </ul>
                                        </div>
                                        <div id="expanded">
                                            <label for="twitter"><i class="fa fa-facebook fa-fw" aria-hidden="true"></i></label>
                                            <input name="twitter" type="text" value="mikeross" />
                                            <label for="twitter"><i class="fa fa-twitter fa-fw" aria-hidden="true"></i></label>
                                            <input name="twitter" type="text" value="ross81" />
                                            <label for="twitter"><i class="fa fa-instagram fa-fw" aria-hidden="true"></i></label>
                                            <input name="twitter" type="text" value="mike.ross" />
                                        </div>
                                    </div>
                                </div>
                                <div id="search">
                                    <label><i class="fa fa-search" aria-hidden="true"></i></label>
                                    <input type="text" placeholder="Search contacts..." />
                                </div>-->
                <div id="contacts">
                    <ul class="mb-4">
                        <li :class="activeRoom.id===room.id?'active':''" class="contact" v-for="room in rooms" @click="activeRoom=room">
                            <div class="wrap">
                                <div class="badge badge-pill badge-primary position-absolute p-2" v-if="unreadCount(room.id) > 0">{{ unreadCount(room.id) }}</div>
                                <div class="wrap-img"><img class="img-fluid" src="/img/logo.png" alt=""/></div>
                                <div class="meta clearfix">
                                    <p class="name mb-0">{{ room.name }}</p>
                                    <p class="preview d-none d-md-block">Пользователей: {{ room.users.length }}</p>
                                </div>
                            </div>
                        </li>
                    </ul>
                    <p class="text-center border-bottom">Пользователи в группе:</p>
                    <ul>
                        <li class="contact" v-for="user in users">
                            <div class="wrap">
                                <span class="contact-status online"></span> <!--online busy away-->
                                <div class="wrap-img">
                                    <img :src="user.image" alt=""/>
                                </div>
                                <div class="meta">
                                    <p class="name">{{ user.fi }}</p>
                                    <p class="preview"><span v-if="user.typing">набирает текст...</span>&nbsp;</p>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                <!--                <div id="bottom-bar">
                                    <button id="addcontact"><i class="fa fa-user-plus fa-fw" aria-hidden="true"></i> <span>Add contact</span></button>
                                    <button id="settings"><i class="fa fa-cog fa-fw" aria-hidden="true"></i> <span>Settings</span></button>
                                </div>-->
            </div>
            <div class="content">
                <div class="contact-profile">
                    <img v-if="activeRoom" src="/img/logo.png" alt=""/>
                    <p v-if="activeRoom">{{ activeRoom.name }}</p>
                    <div class="social-media">
                        <!--                        <i class="fa fa-facebook" aria-hidden="true"></i>-->
                        <!--                        <i class="fa fa-twitter" aria-hidden="true"></i>-->
                        <!--                        <i class="fa fa-instagram" aria-hidden="true"></i>-->
                        <i v-if="isLoading">{{ isLoading }}</i>
                    </div>
                </div>
                <div class="messages">
                    <ul>
                        <li :class="{ sent: isCurrentUser(message.user_id), replies: !isCurrentUser(message.user_id), unread: isUnread(message) }"
                            :id="message.id"
                            v-for="message in messages"
                            v-on:mouseenter="hoverMessage(message)"
                        >
                            <div class="text-center" v-if="isNewMessage(message)">Новые сообщения</div>
                            <div class="mb-1"><b v-if="message.user_id!==user.id">{{ getUser(message.user_id).fi }}</b> <small class="text-xs text-muted ml-2">{{ formatDate(message.created_at) }}</small></div>
                            <div class="wrap-img"><img :src="getUser(message.user_id).image" alt=""/></div>
                            <p v-html="parseMessage(message.message)"></p>
                        </li>
                    </ul>
                </div>
                <div class="message-input wrap">
                    <div class="input-group">
                        <textarea id="send-textarea" class="form-control" rows="1" type="text" placeholder="Напишите ваше сообщение..." v-model="textMessage"
                                  @keydown.enter.exact.prevent
                                  @keyup.enter.exact="sendMessage"
                                  @keydown.enter.ctrl.exact="newline"
                                  @keyup="sendTypingEvent"
                        ></textarea>
                        <div class="input-group-append">
                            <!--                        <i class="fa fa-paperclip attachment" aria-hidden="true"></i>-->
                            <button class="submit" @click="sendMessage"><i class="fa fa-paper-plane" aria-hidden="true"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        props: ['user'],
        data() {
            return {
                messagesAll: [],
                users: [],
                usersAll: [],
                activeUsers: [],
                rooms: [],
                textMessage: '',
                isLoading: false,
                activeRoom: null,
            }
        },
        http: {
            headers: {
                'X-CSRF-TOKEN': Admin.token
            }
        },
        watch: {
            activeRoom: function (n, o) {
                if (o && o.id === n.id) return;
                this.scrollToBottom();
            }
        },
        computed: {
            channel() {
                return window.Echo.join('room.' + this.activeRoom.id);
            },
            messages() {
                return this.messagesAll.filter(m => m.room_id === this.activeRoom.id);
            }
        },
        methods: {
            unreadCount(id) {
                return this.messagesAll.filter(m => m.room_id === id && m.is_read === 0 && m.user_id !== this.user.id).length;
            },
            fetchRooms() {
                this.isLoading = 'загрузка чатов..';
                this.$http.get('/im/rooms').then(response => {
                    this.rooms = response.data;
                    this.activeRoom = this.rooms.length ? this.rooms[0] : null;
                    this.isLoading = false;

                    this.setupRooms();
                    this.fetchUsers();
                });
            },
            fetchUsers() {
                this.isLoading = 'загрузка пользователей...';
                this.$http.get('/im/users').then(response => {
                    this.usersAll = response.data;
                    this.isLoading = false;

                    this.fetchMessages();
                });
            },
            fetchMessages() {
                // this.messages = [];
                this.isLoading = 'загрузка сообщений...';
                this.$http.get('/im/messages', {params: {room_id: this.activeRoom.id}}).then(response => {
                    this.messagesAll = response.data;
                    this.scrollToBottom();
                    this.isLoading = false;
                });
            },
            getUser(id) {
                return this.usersAll.find(item => item.id === id) || {fi:`Пользователь ${id}`,image:'/images/avatar.jpg'}
            },
            isCurrentUser(id) {
                return id === this.user.id;
            },
            isUnread(message) {
                return message.is_read === 0;
            },
            isNewMessage(message) {
                return !this.isCurrentUser(message.user_id) && this.activeRoom.message_id === message.id;
            },
            parseMessage(message) {
                let re = /(https?:\/\/[^\s]+)/g,
                    imageRegex = /\.(png|jpg|jpeg|gif)$/,
                    imgs = [];

                [...message.matchAll(re)].forEach(link => {
                    let url = link[1];
                    if (url.match(imageRegex)) {
                        message = message.replace(url+'', '')
                        imgs.push(`<a class="d-inline-block mr-2 mb-2" href="${url}" target="_blank"><img width="200" class="img-fluid rounded" src="${url}"></a>`)
                    }
                    else message = message.replace(url+'', `<a href="${url}" target="_blank">${url}</a>`)
                })
                if (message.trim().length > 0 && imgs.length > 0) message += '<hr>';

                return message + imgs.join('');
            },
            hoverMessage: _.throttle(function (message) {
                if (!this.isCurrentUser(message.user_id) && this.isUnread(message)) {
                    console.log(message.id)
                    this.$http.post('/im/messages/read', {message_id: message.id, room_id: this.activeRoom.id})
                        .then(({data}) => {
                            this.messagesAll
                                .filter(m => data.includes(m.id))
                                .forEach((m, index) => {
                                    this.$set(this.messagesAll[index], 'is_read', 1);
                                });
                        })
                        .catch(e => {
                            console.log(e)
                        })
                }
            }, 500),
            sendMessage: _.throttle(function () {
                if (this.validateMessage()) return;
                let message = {
                    message: this.textMessage,
                    room_id: this.activeRoom.id,
                    user_id: this.user.id,
                    is_read: 0,
                    created_at: moment().format()
                }
                this.textMessage = '';
                this.isLoading = 'Отправка...'

                this.$http.post('/im/messages', message)
                    .then(({data}) => {
                        message.id = data.id
                        this.messagesAll.push(message);
                        this.isLoading = false;
                        this.scrollToBottom();
                    })
                    .catch(e => {
                        this.textMessage = message.message;
                        this.isLoading = 'Произошла ошибка отправки';
                    });
            }, 500),
            validateMessage() {
                return this.textMessage.trim() === ''
            },
            newline() {
                this.textMessage = `${this.textMessage}\n`;
            },
            sendTypingEvent() {
                if (this.activeRoom)
                    this.channel
                        .whisper('typing', this.user);
            },
            formatDate(date) {
                return moment(date).format('DD.MM HH:mm:ss')
            },
            scrollTo(id) {
                this.$nextTick(function () {
                    if (id === undefined) id = this.activeRoom.message_id;
                    if (id === 0) return;
                    document.getElementById(this.activeRoom.message_id + '').scrollIntoView(true)
                    $('#send-textarea').focus();
                })
            },
            scrollToBottom() {
                this.$nextTick(function () {
                    let container = this.$el.querySelector(".messages");
                    container.scrollTop = container.scrollHeight + 100;
                    $('#send-textarea').focus();
                })
            },
            setupRooms() {
                this.rooms.forEach(room => {
                    Echo.join('room.' + room.id)

                        .here((users) => {
                            this.users = users;
                        })
                        .joining((user) => {
                            if (this.users.filter(u => u.id === user.id).length === 0) this.users.push(user);
                        })
                        .leaving((user) => {
                            this.users = this.users.filter(u => u.id !== user.id);
                        })
                        .listenForWhisper('typing', ({id, name}) => {
                            this.users.forEach((user, index) => {
                                if (user.id === id) {
                                    user.typing = true;

                                    if (user.typingTimer) clearTimeout(user.typingTimer);
                                    user.typingTimer = setTimeout(() => {
                                        user.typing = false;
                                        this.$set(this.users, index, user);
                                    }, 2000);

                                    this.$set(this.users, index, user);
                                }
                            });
                        })

                        .listen('MessageSent', (event) => {
                            let message = {
                                id: event.message.id,
                                message: event.message.message,
                                is_read: 0,
                                room_id: event.message.room_id,
                                user_id: event.message.user_id,
                                created_at: event.message.created_at
                            }
                            this.messagesAll.push(message);

                            this.users.forEach((user, index) => {
                                if (user.id === event.user.id) {
                                    user.typing = false;
                                    this.$set(this.users, index, user);
                                }
                            });

                            this.scrollToBottom();
                        })
                        .listen('MessageRead', (event) => {
                            this.messagesAll
                                .filter(m => event.messages.includes(m.id))
                                .forEach((m, index) => {
                                    m.is_read = 1
                                    this.messagesAll.splice(index, 1, m)
                                });
                        })
                        .listen('MessageNew', (event) => {

                        })
                });
            }
        },
        created() {
            this.fetchRooms();
        }
    }
</script>

<style scoped>
    html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
        margin: 0;
        padding: 0;
        border: 0;
        font-size: 100%;
        vertical-align: baseline
    }

    article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
        display: block
    }

    body {
        line-height: 1
    }

    ol, ul {
        list-style: none
    }

    blockquote, q {
        quotes: none
    }

    blockquote:before, blockquote:after, q:before, q:after {
        content: '';
        content: none
    }

    table {
        border-collapse: collapse;
        border-spacing: 0
    }

    .body {
        display: flex;
        /*align-items: center;*/
        /*justify-content: center;*/
        /*min-height: 100vh;*/
        /*background: #27ae60;*/
        font-size: 1em;
        letter-spacing: 0.1px;
        color: #32465a;
        text-rendering: optimizeLegibility;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.004);
        -webkit-font-smoothing: antialiased;
    }

    #frame {
        width: 100%;
        min-width: 360px;
        /*max-width: 1000px;*/
        height: 85vh;
        min-height: 200px;
        max-height: 720px;
        background: #E6EAEA;
    }

    @media screen and (max-width: 360px) {
        #frame {
            width: 100%;
            height: 100vh;
        }
    }

    #frame #sidepanel {
        float: left;
        min-width: 280px;
        max-width: 280px;
        width: 40%;
        height: 100%;
        background: #2c3e50;
        color: #f5f5f5;
        overflow: hidden;
        position: relative;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel {
            width: 58px;
            min-width: 58px;
        }
    }

    #frame #sidepanel #profile {
        width: 80%;
        margin: 25px auto;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile {
            width: 100%;
            margin: 0 auto;
            padding: 5px 0 0 0;
            background: #32465a;
        }
    }

    #frame #sidepanel #profile.expanded .wrap {
        height: 210px;
        line-height: initial;
    }

    #frame #sidepanel #profile.expanded .wrap p {
        margin-top: 20px;
    }

    #frame #sidepanel #profile.expanded .wrap i.expand-button {
        -moz-transform: scaleY(-1);
        -o-transform: scaleY(-1);
        -webkit-transform: scaleY(-1);
        transform: scaleY(-1);
        filter: FlipH;
        -ms-filter: "FlipH";
    }

    #frame #sidepanel #profile .wrap {
        height: 60px;
        line-height: 60px;
        overflow: hidden;
        -moz-transition: 0.3s height ease;
        -o-transition: 0.3s height ease;
        -webkit-transition: 0.3s height ease;
        transition: 0.3s height ease;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile .wrap {
            height: 55px;
        }
    }

    #frame #sidepanel #profile .wrap img {
        width: 50px;
        border-radius: 50%;
        padding: 3px;
        border: 2px solid #e74c3c;
        height: auto;
        float: left;
        cursor: pointer;
        -moz-transition: 0.3s border ease;
        -o-transition: 0.3s border ease;
        -webkit-transition: 0.3s border ease;
        transition: 0.3s border ease;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile .wrap img {
            width: 40px;
            margin-left: 4px;
        }
    }

    #frame #sidepanel #profile .wrap img.online {
        border: 2px solid #2ecc71;
    }

    #frame #sidepanel #profile .wrap img.away {
        border: 2px solid #f1c40f;
    }

    #frame #sidepanel #profile .wrap img.busy {
        border: 2px solid #e74c3c;
    }

    #frame #sidepanel #profile .wrap img.offline {
        border: 2px solid #95a5a6;
    }

    #frame #sidepanel #profile .wrap p {
        float: left;
        margin-left: 15px;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile .wrap p {
            display: none;
        }
    }

    #frame #sidepanel #profile .wrap i.expand-button {
        float: right;
        margin-top: 23px;
        font-size: 0.8em;
        cursor: pointer;
        color: #435f7a;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile .wrap i.expand-button {
            display: none;
        }
    }

    #frame #sidepanel #profile .wrap #status-options {
        position: absolute;
        opacity: 0;
        visibility: hidden;
        width: 150px;
        margin: 70px 0 0 0;
        border-radius: 6px;
        z-index: 99;
        line-height: initial;
        background: #435f7a;
        -moz-transition: 0.3s all ease;
        -o-transition: 0.3s all ease;
        -webkit-transition: 0.3s all ease;
        transition: 0.3s all ease;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile .wrap #status-options {
            width: 58px;
            margin-top: 57px;
        }
    }

    #frame #sidepanel #profile .wrap #status-options.active {
        opacity: 1;
        visibility: visible;
        margin: 75px 0 0 0;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile .wrap #status-options.active {
            margin-top: 62px;
        }
    }

    #frame #sidepanel #profile .wrap #status-options:before {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 8px solid #435f7a;
        margin: -8px 0 0 24px;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile .wrap #status-options:before {
            margin-left: 23px;
        }
    }

    #frame #sidepanel #profile .wrap #status-options ul {
        overflow: hidden;
        border-radius: 6px;
    }

    #frame #sidepanel #profile .wrap #status-options ul li {
        padding: 15px 0 30px 18px;
        display: block;
        cursor: pointer;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile .wrap #status-options ul li {
            padding: 15px 0 35px 22px;
        }
    }

    #frame #sidepanel #profile .wrap #status-options ul li:hover {
        background: #496886;
    }

    #frame #sidepanel #profile .wrap #status-options ul li span.status-circle {
        position: absolute;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin: 5px 0 0 0;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile .wrap #status-options ul li span.status-circle {
            width: 14px;
            height: 14px;
        }
    }

    #frame #sidepanel #profile .wrap #status-options ul li span.status-circle:before {
        content: '';
        position: absolute;
        width: 14px;
        height: 14px;
        margin: -3px 0 0 -3px;
        background: transparent;
        border-radius: 50%;
        z-index: 0;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile .wrap #status-options ul li span.status-circle:before {
            height: 18px;
            width: 18px;
        }
    }

    #frame #sidepanel #profile .wrap #status-options ul li p {
        padding-left: 12px;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #profile .wrap #status-options ul li p {
            display: none;
        }
    }

    #frame #sidepanel #profile .wrap #status-options ul li#status-online span.status-circle {
        background: #2ecc71;
    }

    #frame #sidepanel #profile .wrap #status-options ul li#status-online.active span.status-circle:before {
        border: 1px solid #2ecc71;
    }

    #frame #sidepanel #profile .wrap #status-options ul li#status-away span.status-circle {
        background: #f1c40f;
    }

    #frame #sidepanel #profile .wrap #status-options ul li#status-away.active span.status-circle:before {
        border: 1px solid #f1c40f;
    }

    #frame #sidepanel #profile .wrap #status-options ul li#status-busy span.status-circle {
        background: #e74c3c;
    }

    #frame #sidepanel #profile .wrap #status-options ul li#status-busy.active span.status-circle:before {
        border: 1px solid #e74c3c;
    }

    #frame #sidepanel #profile .wrap #status-options ul li#status-offline span.status-circle {
        background: #95a5a6;
    }

    #frame #sidepanel #profile .wrap #status-options ul li#status-offline.active span.status-circle:before {
        border: 1px solid #95a5a6;
    }

    #frame #sidepanel #profile .wrap #expanded {
        padding: 100px 0 0 0;
        display: block;
        line-height: initial !important;
    }

    #frame #sidepanel #profile .wrap #expanded label {
        float: left;
        clear: both;
        margin: 0 8px 5px 0;
        padding: 5px 0;
    }

    #frame #sidepanel #profile .wrap #expanded input {
        border: none;
        margin-bottom: 6px;
        background: #32465a;
        border-radius: 3px;
        color: #f5f5f5;
        padding: 7px;
        width: calc(100% - 43px);
    }

    #frame #sidepanel #profile .wrap #expanded input:focus {
        outline: none;
        background: #435f7a;
    }

    #frame #sidepanel #search {
        border-top: 1px solid #32465a;
        border-bottom: 1px solid #32465a;
        font-weight: 300;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #search {
            display: none;
        }
    }

    #frame #sidepanel #search label {
        position: absolute;
        margin: 10px 0 0 20px;
    }

    #frame #sidepanel #search input {
        /*font-family: "proxima-nova", "Source Sans Pro", sans-serif;*/
        padding: 10px 0 10px 46px;
        width: calc(100% - 25px);
        border: none;
        background: #32465a;
        color: #f5f5f5;
    }

    #frame #sidepanel #search input:focus {
        outline: none;
        background: #435f7a;
    }

    #frame #sidepanel #search input::-webkit-input-placeholder {
        color: #f5f5f5;
    }

    #frame #sidepanel #search input::-moz-placeholder {
        color: #f5f5f5;
    }

    #frame #sidepanel #search input:-ms-input-placeholder {
        color: #f5f5f5;
    }

    #frame #sidepanel #search input:-moz-placeholder {
        color: #f5f5f5;
    }

    #frame #sidepanel #contacts {
        height: 100%;
        /*height: calc(100% - 177px);*/
        overflow-y: auto;
        overflow-x: hidden;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #contacts {
            height: calc(100% - 149px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        #frame #sidepanel #contacts::-webkit-scrollbar {
            display: none;
        }
    }

    #frame #sidepanel #contacts.expanded {
        height: calc(100% - 334px);
    }

    #frame #sidepanel #contacts::-webkit-scrollbar {
        width: 8px;
        background: #2c3e50;
    }

    #frame #sidepanel #contacts::-webkit-scrollbar-thumb {
        background-color: #243140;
    }

    #frame #sidepanel #contacts ul li.contact {
        position: relative;
        padding: 10px 0 15px 0;
        font-size: 0.9em;
        cursor: pointer;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #contacts ul li.contact {
            padding: 6px 0 46px 8px;
        }
    }

    #frame #sidepanel #contacts ul li.contact:hover {
        background: #32465a;
    }

    #frame #sidepanel #contacts ul li.contact.active {
        background: #32465a;
        border-left: 5px solid #435f7a;
    }

    #frame #sidepanel #contacts ul li.contact.active span.contact-status {
        border: 2px solid #32465a !important;
    }

    #frame #sidepanel #contacts ul li.contact .wrap {
        width: 88%;
        margin: 0 auto;
        position: relative;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #contacts ul li.contact .wrap {
            width: 100%;
        }
    }

    #frame #sidepanel #contacts ul li.contact .wrap span {
        position: absolute;
        left: 0;
        margin: -2px 0 0 -2px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        border: 2px solid #2c3e50;
        background: #95a5a6;
    }

    #frame #sidepanel #contacts ul li.contact .wrap span.online {
        background: #2ecc71;
    }

    #frame #sidepanel #contacts ul li.contact .wrap span.away {
        background: #f1c40f;
    }

    #frame #sidepanel #contacts ul li.contact .wrap span.busy {
        background: #e74c3c;
    }

    #frame #sidepanel #contacts ul li.contact .wrap .wrap-img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        float: left;
        margin-right: 10px;
        overflow: hidden;
    }

    #frame #sidepanel #contacts ul li.contact .wrap img {
        width: 100%;
        height: auto;
        min-width: 100%;
        min-height: 100%;
        /*border-radius: 50%;*/
        /*float: left;*/
        /*margin-right: 10px;*/
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #contacts ul li.contact .wrap .wrap-img {
            margin-right: 0px;
        }
    }

    #frame #sidepanel #contacts ul li.contact .wrap .meta {
        padding: 5px 0 0 0;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #contacts ul li.contact .wrap .meta {
            display: none;
        }
    }

    #frame #sidepanel #contacts ul li.contact .wrap .meta .name {
        font-weight: 600;
    }

    #frame #sidepanel #contacts ul li.contact .wrap .meta .preview {
        margin: 5px 0 0 0;
        padding: 0 0 1px;
        font-weight: 400;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        -moz-transition: 1s all ease;
        -o-transition: 1s all ease;
        -webkit-transition: 1s all ease;
        transition: 1s all ease;
    }

    #frame #sidepanel #contacts ul li.contact .wrap .meta .preview span {
        position: initial;
        border-radius: initial;
        background: none;
        border: none;
        padding: 0 2px 0 0;
        margin: 0 0 0 1px;
        opacity: .5;
    }

    #frame #sidepanel #bottom-bar {
        position: absolute;
        width: 100%;
        bottom: 0;
    }

    #frame #sidepanel #bottom-bar button {
        float: left;
        border: none;
        width: 50%;
        padding: 10px 0;
        background: #32465a;
        color: #f5f5f5;
        cursor: pointer;
        font-size: 0.85em;
        /*font-family: "proxima-nova", "Source Sans Pro", sans-serif;*/
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #bottom-bar button {
            float: none;
            width: 100%;
            padding: 15px 0;
        }
    }

    #frame #sidepanel #bottom-bar button:focus {
        outline: none;
    }

    #frame #sidepanel #bottom-bar button:nth-child(1) {
        border-right: 1px solid #2c3e50;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #bottom-bar button:nth-child(1) {
            border-right: none;
            border-bottom: 1px solid #2c3e50;
        }
    }

    #frame #sidepanel #bottom-bar button:hover {
        background: #435f7a;
    }

    #frame #sidepanel #bottom-bar button i {
        margin-right: 3px;
        font-size: 1em;
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #bottom-bar button i {
            font-size: 1.3em;
        }
    }

    @media screen and (max-width: 735px) {
        #frame #sidepanel #bottom-bar button span {
            display: none;
        }
    }

    #frame .content {
        float: right;
        width: 60%;
        height: 100%;
        overflow: hidden;
        position: relative;
    }

    @media screen and (max-width: 735px) {
        #frame .content {
            width: calc(100% - 58px);
            min-width: 300px !important;
        }
    }

    @media screen and (min-width: 900px) {
        #frame .content {
            width: calc(100% - 280px);
        }
    }

    #frame .content .contact-profile {
        width: 100%;
        height: 60px;
        line-height: 60px;
        background: #f5f5f5;
    }

    #frame .content .contact-profile img {
        width: 40px;
        border-radius: 50%;
        float: left;
        margin: 9px 12px 0 9px;
    }

    #frame .content .contact-profile p {
        float: left;
    }

    #frame .content .contact-profile .social-media {
        float: right;
    }

    #frame .content .contact-profile .social-media i {
        margin-left: 14px;
        cursor: pointer;
    }

    #frame .content .contact-profile .social-media i:nth-last-child(1) {
        margin-right: 20px;
    }

    #frame .content .contact-profile .social-media i:hover {
        color: #435f7a;
    }

    #frame .content .messages {
        height: auto;
        min-height: calc(100% - 93px);
        max-height: calc(100% - 93px);
        overflow-y: scroll;
        overflow-x: hidden;
    }

    @media screen and (max-width: 735px) {
        #frame .content .messages {
            max-height: calc(100% - 105px);
        }
    }

    #frame .content .messages::-webkit-scrollbar {
        width: 8px;
        background: transparent;
    }

    #frame .content .messages::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.3);
    }

    #frame .content .messages ul li {
        display: inline-block;
        clear: both;
        float: left;
        padding: 5px 15px 15px 15px;
        width: 100%;
        font-size: 0.9em;
    }

    #frame .content .messages ul li.unread {
        background-color: #dee2e6;
    }

    #frame .content .messages ul li:nth-last-child(1) {
        margin-bottom: 20px;
    }

    #frame .content .messages ul li.sent div {
        text-align: right;
    }

    #frame .content .messages ul li.replies div {
        text-align: left;
    }

    #frame .content .messages ul li.sent div span {
        float: left;
    }

    #frame .content .messages ul li.replies div span {
        float: right;
    }

    #frame .content .messages ul li.sent .wrap-img {
        margin: 0 0 0 8px;
    }

    #frame .content .messages ul li.sent p {
        background: #435f7a;
        color: #f5f5f5;
        float: right;
    }

    #frame .content .messages ul li.replies .wrap-img {
        float: left;
        margin: 0 8px 0 0;
    }

    #frame .content .messages ul li.replies p {
        background: #f5f5f5;
    }

    #frame .content .messages ul li .wrap-img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        float: right;
    }

    #frame .content .messages ul li img {
        width: 100%;
        height: auto;
        min-width: 100%;
        min-height: 100%;
    }

    #frame .content .messages ul li p {
        display: inline-block;
        padding: 10px 15px;
        border-radius: 10px;
        max-width: 80%;
        line-height: 130%;
        white-space: break-spaces;
        word-break: break-word;
    }

    @media screen and (max-width: 735px) {
        #frame .content .messages ul li p {
            max-width: 100%;
            /*max-width: 300px;*/
        }
    }

    #frame .content .message-input {
        position: absolute;
        bottom: 0;
        width: 100%;
        z-index: 99;
    }

    #frame .content .message-input.wrap {
        /*position: relative;*/
    }

    #frame .content .message-input.wrap textarea {
        /*font-family: "proxima-nova",  "Source Sans Pro", sans-serif;*/
        /*float: left;*/
        height: inherit;
        border: none;
        /*width: calc(100% - 90px);*/
        padding: 11px 32px 10px 8px;
        /*font-size: 0.8em;*/
        color: #32465a;
        max-height: 3em;
    }

    @media screen and (max-width: 735px) {
        #frame .content .message-input.wrap textarea {
            padding: 15px 32px 16px 8px;
        }
    }

    #frame .content .message-input.wrap textarea:focus {
        outline: none;
    }

    #frame .content .message-input.wrap .attachment {
        position: absolute;
        right: 60px;
        z-index: 4;
        margin-top: 10px;
        font-size: 1.1em;
        color: #435f7a;
        opacity: .5;
        cursor: pointer;
    }

    @media screen and (max-width: 735px) {
        #frame .content .message-input.wrap .attachment {
            margin-top: 17px;
            right: 65px;
        }
    }

    #frame .content .message-input.wrap .attachment:hover {
        opacity: 1;
    }

    #frame .content .message-input.wrap button {
        /*float: right;*/
        border: none;
        width: 50px;
        padding: 12px 0;
        cursor: pointer;
        background: #32465a;
        color: #f5f5f5;
    }

    @media screen and (max-width: 735px) {
        #frame .content .message-input.wrap button {
            padding: 16px 0;
        }
    }

    #frame .content .message-input.wrap button:hover {
        background: #435f7a;
    }

    #frame .content .message-input.wrap button:focus {
        outline: none;
    }
</style>

<style>
    #frame .content .messages ul li a {
        font-weight: bold;
    }
    #frame .content .messages ul li.sent a {
        color: white!important;
    }
</style>
