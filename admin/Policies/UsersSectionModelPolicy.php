<?php

namespace Admin\Policies;

use Admin\Http\Sections\Users;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UsersSectionModelPolicy
{

    use HandlesAuthorization;

    public function before(User $user, $ability, Users $section, User $item = null)
    {
        if ($user->isSuperAdmin()) {
            if ($ability != 'display' && $ability != 'create' && !is_null($item) && $item->id <= 2 && $item->id == 10) {
                return false;
            }

            return true;
        }
    }

    /**
     * @param User $user
     * @param Users $section
     * @param User $item
     *
     * @return bool
     */
    public function display(User $user, Users $section, User $item)
    {
        return $user->isSuperAdmin() or ($user->hasRole('personal') || $user->id == 130 || $user->id == 179 || $user->id == 298);
    }

    /**
     * @param User $user
     * @param Users $section
     * @param User $item
     *
     * @return bool
     */
    public function create(User $user, Users $section, User $item)
    {
        return $user->isSuperAdmin() or ($user->hasRole('personal') || $user->id == 130 || $user->id == 179 || $user->id == 298);
    }

    /**
     * @param User $user
     * @param Users $section
     * @param User $item
     *
     * @return bool
     */
    public function edit(User $user, Users $section, User $item)
    {
        return $user->isSuperAdmin() or (($user->hasRole('personal') || $user->hasRole('seniorsales') || $user->id == 130 || $user->id == 179 || $user->id == 298) && !$item->hasRole('superadmin'));  //$user->isSuperAdmin() ?? ($item->id > 2 and $item->id != 10);
    }

    /**
     * @param User $user
     * @param Users $section
     * @param User $item
     *
     * @return bool
     */
    public function delete(User $user, Users $section, User $item)
    {
        return $user->isSuperAdmin();
    }
}
