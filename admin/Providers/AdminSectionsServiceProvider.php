<?php

namespace Admin\Providers;

use Conner\Tagging\Model\Tag;
use Illuminate\Routing\Router;
use OwenIt\Auditing\Models\Audit;
use SleepingOwl\Admin\Contracts\Navigation\NavigationInterface;
use SleepingOwl\Admin\Contracts\Template\MetaInterface;
use SleepingOwl\Admin\Contracts\Widgets\WidgetsRegistryInterface;
use SleepingOwl\Admin\Providers\SleepingOwlServiceProvider as ServiceProvider;

class AdminSectionsServiceProvider extends ServiceProvider
{
    /**
     * @var array
     */
    protected $widgets = [
        // \Admin\Widgets\DashboardMap::class,
        \Admin\Widgets\Changelog::class,
        \Admin\Widgets\NavigationNotifications::class,
        \Admin\Widgets\NavigationUserBlock::class,
    ];

    /**
     * @var array
     */
    protected $sections = [
        //Клиенты
        'App\Models\ClientNew' => 'Admin\Http\Sections\ClientsNew',
        'App\Models\ClientReg' => 'Admin\Http\Sections\ClientsReg',
        'App\Models\ClientBuy' => 'Admin\Http\Sections\ClientsBuy',
        'App\Models\ClientUr' => 'Admin\Http\Sections\ClientsUrs',
        'App\Models\ClientUrNew' => 'Admin\Http\Sections\ClientsUrsNew',
        'App\Models\Client' => 'Admin\Http\Sections\Clients',
        'App\Models\Bonus' => 'Admin\Http\Sections\Bonuses',
        'App\Models\Contract' => 'Admin\Http\Sections\Contracts',

        //Заказы
//        'App\Models\Order' => 'Admin\Http\Sections\Orders',
        'App\Models\OrderNew' => 'Admin\Http\Sections\OrderNews',
//        'App\Models\OrderCart' => 'Admin\Http\Sections\OrderCarts',
        'App\Models\Cart' => 'Admin\Http\Sections\Carts',
        'App\Models\CartTiny' => 'Admin\Http\Sections\CartTinys',

        //Логисту
//        'App\Models\OrderLogist' => 'Admin\Http\Sections\OrderLogists',
        'App\Models\OrderSupplier' => 'Admin\Http\Sections\OrderSuppliers',
//        'App\Models\OrderDelivery' => 'Admin\Http\Sections\OrderDeliveries',
        'App\Models\OrderRoadlist' => 'Admin\Http\Sections\OrderRoadlists',
        'App\Models\DocRegistration' => 'Admin\Http\Sections\DocRegistrations',
        'App\Models\DeliveryService' => 'Admin\Http\Sections\DeliveryServices',
        'App\Models\Lawyer' => 'Admin\Http\Sections\Lawyers',
//        'App\Models\Goods' => 'Admin\Http\Sections\Goods',
        'App\Models\Claim' => 'Admin\Http\Sections\Claims',

        // Склад
        'App\Models\StoreGood' => 'Admin\Http\Sections\StoreGoods',
        'App\Models\StoreTmc' => 'Admin\Http\Sections\StoreTmcs',

        // Лиды
        'App\Models\OrderCall' => 'Admin\Http\Sections\OrderCalls',
        'App\Models\ExpiredLead' => 'Admin\Http\Sections\ExpiredLeads',
        'App\Models\Lead' => 'Admin\Http\Sections\Leads', // Simple
        'App\Models\LeadDelete' => 'Admin\Http\Sections\LeadDeletes',

        'App\Models\Task' => 'Admin\Http\Sections\Tasks',
        'App\Models\RejectReport' => 'Admin\Http\Sections\RejectReports',
        'App\Models\CartReport' => 'Admin\Http\Sections\CartReports',
        'App\Models\WorkNote' => 'Admin\Http\Sections\WorkNotes',

        // Заявки
//        'App\Models\OrderCondition' => 'Admin\Http\Sections\OrderConditions',
//        'App\Models\OrderSubscrible' => 'Admin\Http\Sections\OrderSubscrible',

        // Поставщики
//        'App\Models\BrandSupplier' => 'Admin\Http\Sections\BrandSuppliers',
//        'App\Models\SupplierBrand' => 'Admin\Http\Sections\SupplierBrands',
        'App\Models\SupplierPricelist' => 'Admin\Http\Sections\SupplierPricelists',
//        'App\Models\City' => 'Admin\Http\Sections\Cities',
//        'App\Models\Region' => 'Admin\Http\Sections\Regions',
        'App\Models\Brand' => 'Admin\Http\Sections\Brands',
        'App\Models\Supplier' => 'Admin\Http\Sections\Suppliers',

        // Монтажники
//        'App\Models\MeasureCity' => 'Admin\Http\Sections\MeasurersCity',
        'App\Models\Measurer' => 'Admin\Http\Sections\Measurers',
        'App\Models\MeasureSpec' => 'Admin\Http\Sections\MeasuresSpec',
        'App\Models\MeasureRegion' => 'Admin\Http\Sections\MeasurersRegion',
//        'App\Models\MeasureRegionOld' => 'Admin\Http\Sections\MeasurersRegionOld',
//        'App\Models\MeasurersLink' => 'Admin\Http\Sections\MeasurersLinks',
        'App\Models\Service' => 'Admin\Http\Sections\Services',

        // Транспортные компании
        'App\Models\TransportCompany\Local' => 'Admin\Http\Sections\TransportCompaniesLocal',
        'App\Models\TransportCompany\Intercity' => 'Admin\Http\Sections\TransportCompaniesIntercity',
        'App\Models\TransportCompany\Courier' => 'Admin\Http\Sections\TransportCompaniesCourier',
        'App\Models\TransportCompany\CityCourier' => 'Admin\Http\Sections\TransportCompaniesCityCourier',
        'App\Models\TransportCompany\CityIntercity' => 'Admin\Http\Sections\TransportCompaniesCityIntercity',
        'App\Models\Geo\CityTerminal' => 'Admin\Http\Sections\CityTerminals',

        // Настройки
        'App\Models\OrderStatus' => 'Admin\Http\Sections\OrderStatuses',
        'App\Models\RejectReason' => 'Admin\Http\Sections\RejectReasons',
        'App\Models\CartStatus' => 'Admin\Http\Sections\CartStatuses',
        'App\Models\SupplierStatus' => 'Admin\Http\Sections\SupplierStatuses',
        'App\Models\SuppliersStatus' => 'Admin\Http\Sections\SuppliersStatuses',
        'App\Models\ServiceStatus' => 'Admin\Http\Sections\ServiceStatuses',
        'App\Models\TaskStatus' => 'Admin\Http\Sections\TaskStatuses',
        'App\Models\TaskType' => 'Admin\Http\Sections\TaskTypes',
        'App\Models\ContragentDebptsStatus' => 'Admin\Http\Sections\ContragentDebptsStatuses',
        'App\Models\CandidateStatus' => 'Admin\Http\Sections\CandidateStatuses',
        'App\Models\OrderType' => 'Admin\Http\Sections\OrderTypes',
        'App\Models\GoodsCategory' => 'Admin\Http\Sections\GoodsCategories',
        'App\Models\PaymentType' => 'Admin\Http\Sections\PaymentTypes',
        'App\Models\Employee' => 'Admin\Http\Sections\Employees',
        'App\Models\AverageWeight' => 'Admin\Http\Sections\AverageWeights',
        'App\Models\MeanVolume' => 'Admin\Http\Sections\MeanVolumes',
        'App\Models\PackingType' => 'Admin\Http\Sections\PackingTypes',
        'App\Models\EmailTemplate' => 'Admin\Http\Sections\EmailTemplates',
        'App\Models\Bank' => 'Admin\Http\Sections\Banks',
        'App\Models\CandidateReason' => 'Admin\Http\Sections\CandidateReasons',
        'App\Models\FormSurcharge' => 'Admin\Http\Sections\FormSurcharges',
        'App\Models\ExpenseType' => 'Admin\Http\Sections\ExpenseTypes',
        'App\Models\Franchise' => 'Admin\Http\Sections\Franchises',
        'App\Models\TtCode' => 'Admin\Http\Sections\TtCodes',
        'App\Models\CheckType' => 'Admin\Http\Sections\CheckTypes',
        'App\Models\ReportCheckList' => 'Admin\Http\Sections\ReportCheckLists',
        'App\Models\Config\Salary' => 'Admin\Http\Sections\Salaries',
        'App\Models\Config\SalaryLogist' => 'Admin\Http\Sections\SalaryLogists',
        'App\Models\Config\AnalyticConfig' => 'Admin\Http\Sections\AnalyticConfigs',

        //Информация
        'App\Models\EmailLog' => 'Admin\Http\Sections\EmailLogs',

        //Бухгалтерия
        'App\Models\Expense' => 'Admin\Http\Sections\Expenses',
        'App\Models\Money' => 'Admin\Http\Sections\Moneys',
        'App\Models\WorkDay' => 'Admin\Http\Sections\WorkDays',

        //HR
        'App\Models\Candidate' => 'Admin\Http\Sections\Candidates',
        'App\Models\HrReport' => 'Admin\Http\Sections\HrReports',
        'App\Models\Vacancy' => 'Admin\Http\Sections\Vacancies',

        'App\Models\CrmFranchise' => 'Admin\Http\Sections\CrmFranchises',
        'App\Models\FranchiseStatus' => 'Admin\Http\Sections\FranchiseStatuses',

        //Маркетинг
        'App\Models\Competitor' => 'Admin\Http\Sections\Competitors',
        'App\Models\Stock' => 'Admin\Http\Sections\Stocks',
        'App\Models\Banner' => 'Admin\Http\Sections\Banners',
        'App\Models\Discount' => 'Admin\Http\Sections\Discounts',
        'App\Models\Instruction' => 'Admin\Http\Sections\Instructions',
        'App\Models\Margin' => 'Admin\Http\Sections\Margins',
//        'App\Models\MarginCity' => 'Admin\Http\Sections\MarginCities',
//        'App\Models\MarginRegion' => 'Admin\Http\Sections\MarginRegions',
        'App\Models\Delivery' => 'Admin\Http\Sections\Deliveries',
        'App\Models\Analytic' => 'Admin\Http\Sections\Analytics',
        'App\Models\Mailer\Channel' => 'Admin\Http\Sections\Channels',
        'App\Models\Mailer\Subscriber' => 'Admin\Http\Sections\Subscribers',
        'App\Models\Mailer\Scenario' => 'Admin\Http\Sections\Scenarios',
        'App\Models\Mailer\Email' => 'Admin\Http\Sections\Emails',
        'App\Models\News' => 'Admin\Http\Sections\News',
        'App\Models\Page' => 'Admin\Http\Sections\Pages',
        'App\Models\Slider' => 'Admin\Http\Sections\Sliders',
        'App\Models\Widgets\Measurer' => 'Admin\Http\Sections\MeasurersWidget',
        'App\Models\NewsCategory' => 'Admin\Http\Sections\NewsCategories',
        'App\Models\PhotoReview' => 'Admin\Http\Sections\PhotoReviews',
        'App\Models\Faq' => 'Admin\Http\Sections\Faqs',
        'App\Models\BlackList' => 'Admin\Http\Sections\BlackLists',

        // SEO
        'App\Models\SEO\Seo' => 'Admin\Http\Sections\SEO\Seos',
        'App\Models\SEO\Modifier' => 'Admin\Http\Sections\SEO\Modifiers',
        'App\Models\SEO\FriendlyUrl' => 'Admin\Http\Sections\SEO\FriendlyUrls',
        'App\Models\SEO\Catalog' => 'Admin\Http\Sections\SEO\Catalogs',
        'App\Models\SEO\Menu' => 'Admin\Http\Sections\SEO\Menus',
        'App\Models\SEO\CatalogLink' => 'Admin\Http\Sections\SEO\CatalogLinks',
        'App\Models\SEO\FriendlyLink' => 'Admin\Http\Sections\SEO\FriendlyLinks',
        Tag::class => 'Admin\Http\Sections\SEO\Tags',

        // SEARCH
        'App\Models\Search\SearchCatalog' => 'Admin\Http\Sections\Search\Catalogs',
        'App\Models\SearchLog' => 'Admin\Http\Sections\Search\Logs',
        'App\Models\Search\Synonym' => 'Admin\Http\Sections\Search\Synonyms',
        'App\Models\Search\Index' => 'Admin\Http\Sections\Search\Index',
        'App\Models\Search\Hit' => 'Admin\Http\Sections\Search\Hits',

        //Отдел рекламаций
        'App\Models\ReviewsList' => 'Admin\Http\Sections\ReviewsLists',
        'App\Models\ContragentDebpt' => 'Admin\Http\Sections\ContragentDebpts',

        // Школа
        'App\Models\School\Page' => 'Admin\Http\Sections\SchoolPages',
        'App\Models\School\Rubric' => 'Admin\Http\Sections\SchoolRubrics',
        'App\Models\CatalogCertification' => 'Admin\Http\Sections\CatalogCertification',


        // Supsuper
        'App\Models\Supsuper\Catalog' => 'Admin\Http\Sections\Supsuper\Catalog',
        'App\Models\Catalog' => 'Admin\Http\Sections\Supsuper\Catalogs',
        'App\Models\CatalogLogist' => 'Admin\Http\Sections\Supsuper\CatalogsLogists',
        // Скрытое
//        'App\Models\CartItem' => 'Admin\Http\Sections\CartItem',


        // RIDERS DRIVE
        'App\Models\RidersDrive\ContentContactrd' => 'Admin\Http\Sections\RidersDrive\ContentContactrds',
        'App\Models\RidersDrive\SEO\Seord' => 'Admin\Http\Sections\RidersDrive\SEO\Seords',
        'App\Models\RidersDrive\Bannerrd' => 'Admin\Http\Sections\RidersDrive\Bannerrds',
//        'App\Models\SEO\Modifier' => 'Admin\Http\Sections\SEO\Modifiers',
        'App\Models\RidersDrive\SEO\FriendlyUrlrd' => 'Admin\Http\Sections\RidersDrive\SEO\FriendlyUrlrds',
        'App\Models\RidersDrive\SEO\Catalogrd' => 'Admin\Http\Sections\RidersDrive\SEO\Catalogrds',
        \App\Models\RidersDrive\Galleryrd::class => 'Admin\Http\Sections\RidersDrive\Galleryrds',
//        'App\Models\SEO\Menu' => 'Admin\Http\Sections\SEO\Menus',
        'App\Models\RidersDrive\SEO\CatalogLinkrd' => 'Admin\Http\Sections\RidersDrive\SEO\CatalogLinkrds',
        'App\Models\RidersDrive\SEO\FriendlyLinkrd' => 'Admin\Http\Sections\RidersDrive\SEO\FriendlyLinkrds',
//        Tag::class => 'Admin\Http\Sections\SEO\Tags',
        'App\Models\RidersDrive\Sliderrd' => 'Admin\Http\Sections\RidersDrive\Sliderrds',
        'App\Models\RidersDrive\Pagerd' => 'Admin\Http\Sections\RidersDrive\Pagerds',
        'App\Models\RidersDrive\Menurd' => 'Admin\Http\Sections\RidersDrive\Menurds',
        'App\Models\RidersDrive\Meetingrd' => 'Admin\Http\Sections\RidersDrive\Meetingrds',
        'App\Models\RidersDrive\TypeButtonrd' => 'Admin\Http\Sections\RidersDrive\TypeButtonrds',
        'App\Models\RidersDrive\Geo\CityDomainrd' => 'Admin\Http\Sections\RidersDrive\CityDomainrds',
        'App\Models\RidersDrive\KitCityrd' => 'Admin\Http\Sections\RidersDrive\KitCitierds',
        'App\Models\RidersDrive\Geo\Regionrd' => 'Admin\Http\Sections\RidersDrive\GeoRegionrds',
        'App\Models\RidersDrive\OrderCallrd' => 'Admin\Http\Sections\RidersDrive\OrderCallrds',
        'App\Models\RidersDrive\OrderNewrd' => 'Admin\Http\Sections\RidersDrive\OrderNewrds',
        'App\Models\RidersDrive\Supnoterd' => 'Admin\Http\Sections\RidersDrive\Supnoterds',
        'App\Models\RidersDrive\EmailTemplaterd' => 'Admin\Http\Sections\RidersDrive\EmailTemplaterds',

        'App\Models\RidersDrive\Search\SearchCatalogrd' => 'Admin\Http\Sections\RidersDrive\Search\Catalogrds',
        'App\Models\RidersDrive\SearchLogrd' => 'Admin\Http\Sections\RidersDrive\Search\Logrds',
        'App\Models\RidersDrive\Search\Synonymrd' => 'Admin\Http\Sections\RidersDrive\Search\Synonymrds',
        'App\Models\RidersDrive\Search\Indexrd' => 'Admin\Http\Sections\RidersDrive\Search\Indexrd',
        'App\Models\RidersDrive\Search\Hitrd' => 'Admin\Http\Sections\RidersDrive\Search\Hitrds',
        \App\Models\RidersDrive\Reviewrd::class => 'Admin\Http\Sections\RidersDrive\Reviewrds',
        \App\Models\RidersDrive\Instructionrd::class => 'Admin\Http\Sections\RidersDrive\Instructionrds',
        \App\Models\RidersDrive\CatalogAdmin::class => 'Admin\Http\Sections\RidersDrive\Catalogrds',
        'App\Models\RidersDrive\CatalogLogistrd' => 'Admin\Http\Sections\RidersDrive\CatalogsLogistrds',
        'App\Models\RidersDrive\Supplierrd' => 'Admin\Http\Sections\Supplierrds',
        'App\Models\RidersDrive\Brandrd' => 'Admin\Http\Sections\RidersDrive\Brandrds',
        'App\Models\School\Pagerd' => 'Admin\Http\Sections\RidersDrive\SchoolPagerds',
        'App\Models\School\Rubricrd' => 'Admin\Http\Sections\RidersDrive\SchoolRubricrds',
        'App\Models\RidersDrive\Edurd' => 'Admin\Http\Sections\RidersDrive\Edurds',

        'App\Models\Changelog' => 'Admin\Http\Sections\Changelogs',
        'App\Models\Questionnaire' => 'Admin\Http\Sections\Questionnairies',
        'App\Models\Dictionary' => 'Admin\Http\Sections\Dictionaries',
        'App\Models\Meeting' => 'Admin\Http\Sections\Meetings',
        'App\Models\PracticalTask' => 'Admin\Http\Sections\PracticalTasks',
        Audit::class => 'Admin\Http\Sections\Audits',
        'App\Role' => 'Admin\Http\Sections\Roles',
        'App\User' => 'Admin\Http\Sections\Users',
        'App\Models\Department' => 'Admin\Http\Sections\Departments',
        'App\Models\Log' => 'Admin\Http\Sections\Logs',
        'App\Models\CatalogCity' => 'Admin\Http\Sections\CatalogCity',
        'App\Models\GoodsCity' => 'Admin\Http\Sections\GoodsCity',
        'App\Models\Geo\City' => 'Admin\Http\Sections\GeoCities',
        'App\Models\Geo\CityDomain' => 'Admin\Http\Sections\CityDomains',
        'App\Models\KitCache' => 'Admin\Http\Sections\KitCaches',
        'App\Models\LocalRate' => 'Admin\Http\Sections\LocalRates',
        'App\Models\KitCity' => 'Admin\Http\Sections\KitCities',
        'App\Models\Geo\Region' => 'Admin\Http\Sections\GeoRegions',
        'App\Models\Media' => 'Admin\Http\Sections\Medias',
        'App\Models\Menu' => 'Admin\Http\Sections\Menus',
        'App\Models\Logo' => 'Admin\Http\Sections\Logos',
        'App\Models\ContentContact' => 'Admin\Http\Sections\ContentContacts',
        'App\Models\Ip' => 'Admin\Http\Sections\Ips',
        'App\Models\TypeButton' => 'Admin\Http\Sections\TypeButtons',

        \App\Models\Module::class => 'Admin\Http\Sections\ADMIN\Modules',
        \App\Models\Modset::class => 'Admin\Http\Sections\ADMIN\ModSets',
        \App\Models\GoodsSet::class => 'Admin\Http\Sections\ADMIN\GoodsSets',
        \App\Models\ModuleSet::class => 'Admin\Http\Sections\ADMIN\ModuleSets',
        \App\Models\Modtype::class => 'Admin\Http\Sections\ADMIN\Modtypes',
        \App\Models\Series::class => 'Admin\Http\Sections\ADMIN\Series',
        \App\Models\Price::class => 'Admin\Http\Sections\ADMIN\Prices',
//        \App\Models\User::class => 'App\Http\Sections\Users',
        \App\Models\FranchUser::class => 'Admin\Http\Sections\ADMIN\FranchUsers',
        \App\Models\Alfamart24User::class => 'Admin\Http\Sections\ADMIN\AlfamartUsers',
        \App\Models\AlfamartHelper::class => 'Admin\Http\Sections\ADMIN\AlfamartHelpers',
//        \App\Models\Role::class => 'Admin\Http\Sections\ADMIN\Roles',
        \App\Models\CatalogAdmin::class => 'Admin\Http\Sections\ADMIN\Catalogs',
//        \App\Models\CatalogAdmin2::class => 'Admin\Http\Sections\ADMIN\Catalogs2', // TMP
        \App\Models\CatalogCharacteristic::class => 'Admin\Http\Sections\ADMIN\CatalogsCharacteristics',
        \App\Models\CharacteristicType::class => 'Admin\Http\Sections\ADMIN\CharacteristicsTypes',
        \App\Models\CharacteristicTypeList::class => 'Admin\Http\Sections\ADMIN\CharacteristicsTypesLists',
        \App\Models\Characteristic::class => 'Admin\Http\Sections\ADMIN\Characteristics',
        \App\Models\PackingType::class => 'Admin\Http\Sections\ADMIN\PackingTypes',
        \App\Models\Note::class => 'Admin\Http\Sections\ADMIN\Notes',
        \App\Models\CatalogPrice::class => 'Admin\Http\Sections\ADMIN\CatalogPrices',
        \App\Models\AdminComment::class => 'Admin\Http\Sections\ADMIN\AdminComments',
        \App\Models\Goods::class => 'Admin\Http\Sections\ADMIN\Goods',
        \App\Models\Review::class => 'Admin\Http\Sections\ADMIN\Reviews',
        \App\Models\GoodsTmp::class => 'Admin\Http\Sections\ADMIN\GoodsTmps',
        \App\Models\Gallery::class => 'Admin\Http\Sections\ADMIN\Gallerys',
        \App\Models\GoodsCharacteristic::class => 'Admin\Http\Sections\ADMIN\GoodsCharacteristics',
//        \App\Models\Media::class => 'Admin\Http\Sections\ADMIN\Medias',
        \App\Models\Instruction::class => 'Admin\Http\Sections\ADMIN\Instructions',
        \App\Models\Variant::class => 'Admin\Http\Sections\ADMIN\Variants',
        \App\Models\GoodsGroup::class => 'Admin\Http\Sections\ADMIN\GoodsGroups',

        \App\Room::class => 'Admin\Http\Sections\Rooms',
        \App\Models\CRM\Script::class => 'Admin\Http\Sections\CRM\Scripts',
        'App\Models\CatalogRelatedLink' => 'Admin\Http\Sections\ADMIN\CatalogRelatedLinks',

    ];

    /**
     * @param \SleepingOwl\Admin\Admin $admin
     *
     * @return void
     */
    public function boot(\SleepingOwl\Admin\Admin $admin)
    {
        if ($this->app->runningInConsole() && env('APP_ENV') != 'testing') return;

        $this->loadViewsFrom( base_path( "admin/resources/views" ), 'admin' );
        $this->registerPolicies( 'Admin\\Policies\\' );

        $this->app->call( [ $this, 'registerRoutes' ] );
        $this->app->call( [ $this, 'registerNavigation' ] );

        parent::boot($admin);

        $this->app->call( [ $this, 'registerViews' ] );
        $this->app->call( [ $this, 'registerMediaPackages' ] );
    }

    /**
     * @param NavigationInterface $navigation
     */
    public function registerNavigation(NavigationInterface $navigation)
    {
        if (request()->is('school/*')) {
            return;
        }
        if (request()->is('schoolrd/*')) {
            return;
        }
        require base_path('admin/navigation.php');

        \AdminNavigation::getPages()->findById('hole')->setAccessLogic(function () {
            if (\Auth::user()->isSuperAdmin()) {
                return true;
            }
            $userPageIds = $this->getUserRights();
            $this->setUserAccess($userPageIds);
            return false;
        });
    }

    public function getUserRights()
    {
        $roles = \Auth::user()->roles()->get();
        foreach ($roles as $role) {
            if (array_key_exists($role->name, config('alfamart.access'))) {
                $access = config('alfamart.access')[$role->name];

                $include[] = array_key_exists('include', $access) ? $access['include'] : null;
                $exclude[] = array_key_exists('exclude', $access) ? $access['exclude'] : null;
            }
            $userPageIds = [];
            $include = array_filter($include);
            if (!empty($include)) {
                $userPageIds['include'] = array_merge(...array_values(array_filter($include)));
            }
            $exclude = array_filter($exclude);
            if (!empty($exclude)) {
                $userPageIds['exclude'] = array_unique(array_merge(...array_values($exclude)));
            }
        }
        if (\Auth::id() == 179) $userPageIds['exclude'] = array_diff($userPageIds['exclude'], ['candidates', 'users']);
        if (\Auth::id() == 298) {
            $userPageIds['exclude'] = array_diff($userPageIds['exclude'], ['sales_settings','lead_deletes','users']);
            $userPageIds['exclude'] = array_merge($userPageIds['exclude'], ['expired_leads','leads_simple','blacklist','check_types']);
        }

        return $userPageIds;
    }

    public function setUserAccess($userPageIds)
    {
        if (array_key_exists('include', $userPageIds)) {
            $ids = $userPageIds['include'];

            if (empty($ids)) {
                return true;
            }

            $pages = \AdminNavigation::getPages();
            $this->setPageAccess($pages, false);

            foreach ($ids as $id) {
                $page = \AdminNavigation::getPages()->findById($id);
                if (!$page) continue;

                if ($page->getParent()) {
                    $page->getParent()->setAccessLogic(function () {
                        return true;
                    });
                    $this->setPageAccess($page->getParent()->getPages(), false);
                }

                $page->setAccessLogic(function () {
                    return true;
                });
            }
        }

        if (array_key_exists('exclude', $userPageIds)) {
            foreach ($userPageIds['exclude'] as $item) {
                $page = \AdminNavigation::getPages()->findById($item);
                if($page) {
                    $page->setAccessLogic(function () {
                        return false;
                    });
                }
            }
        }
    }

    public function setPageAccess($pages, $access = true){
        foreach ($pages as $page){
            $page->setAccessLogic(function () use ($access){
                return $access;
            });
        }
    }

    /**
     * @param WidgetsRegistryInterface $widgetsRegistry
     */
    public function registerViews(WidgetsRegistryInterface $widgetsRegistry)
    {
        foreach ($this->widgets as $widget) {
            $widgetsRegistry->registerWidget($widget);
        }
    }

    /**
     * @param Router $router
     */
    public function registerRoutes(Router $router)
    {
        $router->group([
            'prefix' => config('sleeping_owl.url_prefix'),
            'middleware' => config('sleeping_owl.middleware')
        ], function ($router) {
            // require base_path('admin/Http/routes.php');  // TODO: удалить
            require base_path('admin/routes/api.php');
            require base_path('admin/routes/app.php');
            require base_path('admin/routes/doc.php');
            require base_path('admin/routes/web.php');
            require base_path('admin/routes/channels.php');
            require base_path('admin/routes/test.php');
            require base_path('admin/routes/email.php');
            require base_path('admin/routes/seo.php');
        } );
    }

    /**
     * @param MetaInterface $meta
     */
    public function registerMediaPackages(MetaInterface $meta)
    {
        $packages = $meta->assets()->packageManager();

        require base_path('admin/assets.php');
    }

    public function setDemoBadge($page_id)
    {
        $page = \AdminNavigation::getPages()->findById($page_id);
        if (!$page) return;
        $page->addBadge(function () {
            return 'Demo';
        }, call_user_func(function () {
            return ['class' => 'badge-warning', 'style' => 'padding: 2px; font-size: 60%; margin: -10px 0;'];
        }));
    }

}
