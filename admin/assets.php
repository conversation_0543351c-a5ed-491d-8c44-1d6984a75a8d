<?php

/**
 * @var KodiCMS\Assets\Contracts\MetaInterface $meta
 * @var KodiCMS\Assets\Contracts\PackageManagerInterface $packages
 *
 * @see http://sleepingowladmin.ru/docs/assets
 */

PackageManager::add('jquerydatepicker')
//    ->js('jquery-ui', '//ajax.googleapis.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js','admin-modules-load',true)
    ->js('datepicker', asset('/js/libs/datepicker/datepicker.js'),'admin-modules-load',true);
//    ->js('datepicker-ru', asset('/js/libs/datepicker/datepicker-ru.js'),'datepicker',true);
PackageManager::add('jquerydatepicker-test')
    ->js('jquery-ui', '//ajax.googleapis.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js','admin-modules-load',true)
    ->js('datepicker', asset('/js/libs/datepicker/datepicker-test.js'),'jquery-ui',true)
    ->js('datepicker-ru', asset('/js/libs/datepicker/datepicker-ru.js'),'datepicker',true);

PackageManager::add('jvectormap')
    ->css('jvectormap.css', asset('/js/libs/jvectormap/jquery-jvectormap-2.0.3.css'))
    ->js('jvectormap', asset('/js/libs/jvectormap/jquery-jvectormap-2.0.3.min.js'),'admin-modules-load',true)
    ->js('jvectormap-ru-mill', asset('/js/libs/jvectormap/jquery-jvectormap-ru-mill.js'),'jvectormap',true)
    ->js('jvectormap-common', asset("/js/libs/jvectormap/common.js?v=123"),'jvectormap-ru-mill',true);

PackageManager::add('stopPageRefresh')
    ->js('jquery.form',    asset('customjs/jquery.form.min.js'),'admin-modules-load',true)
    ->js('stopPageRefresh.js', '/js/stopPageRefresh.js?q=1', ['jquery.form'],true);

PackageManager::add('elfinder')
    ->css('jquery-ui.css', '//ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/themes/smoothness/jquery-ui.css')
    ->css('elfinder.min.css',asset('/packages/barryvdh/elfinder/css/elfinder.min.css'))
    ->css('elfinder.theme.css',asset('/packages/barryvdh/elfinder/css/theme.css'))
    ->js('jquery-ui.min.js','//ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/jquery-ui.min.js','admin-modules-load',true)
    ->js('elfinder',asset('/packages/barryvdh/elfinder/js/elfinder.min.js'),'jquery-ui.min.js',true)
    ->js('elfinder.ru.js',asset('/packages/barryvdh/elfinder/js/i18n/elfinder.ru.js'),'elfinder', true)
    ->js('elfinder-common',asset('/js/libs/elfinder/common.js'),'elfinder.ru.js', true);

Meta::addJs('crm.js', mix("/js/crm.js"), ['admin-default']);
Meta::addCss('crm.css', mix("/css/crm.css"), ['admin-default']);

Meta::addJs('jquery.cookie.min.js', asset("/js/lib/jquery.cookie.min.js"), ['admin-default']);
Meta::addJs('app', mix("js/app.js"), ['admin-default']);
Meta::addJs('components', mix("js/components.js"), ['admin-default']);

Meta::addCss('suggestions.css', asset("/css/suggestions.css"), ['admin-default']);
Meta::addJs('jquery.autocomplete.min.js', asset("/js/lib/jquery.autocomplete.min.js"), ['admin-default']);
Meta::addJs('common.js', asset("/js/common.js?v=2"), ['admin-default']);
