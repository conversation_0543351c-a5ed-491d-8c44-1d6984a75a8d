<?php

namespace Admin\Http\Sections\Search;

use AdminForm;
use AdminFormElement;
use App\Models\Search\SearchCatalog;
use SleepingOwl\Admin\Section;
use AdminDisplay;
use AdminColumn;

class Synonyms extends Section
{
    protected $checkAccess = false;
    protected $title = 'Синонимы';
    protected $alias;

    public function onDisplay($payload = [])
    {

        $display = AdminDisplay::datatablesAsync()
            ->with('catalog')
            ->setHtmlAttribute('class', 'table-primary')
            ->setColumns([
                AdminColumn::text('catalog.title', 'Каталог'),
                AdminColumn::text('word', 'Слово'),
                AdminColumn::custom('Синонимы', function ($model){
                    return count(json_decode($model->synonyms));
                }),
            ]);

        if ($payload) { $display->setScopes($payload['scopes']); }

        $display->setDisplaySearch(true);
        return $display;
    }

    public function onEdit($id)
    {
        $form = AdminForm::card()->addBody([
            AdminFormElement::select('catalog_id', 'Каталог')
                ->setModelForOptions(SearchCatalog::class),
            AdminFormElement::text('word', 'Слово')->required(),
            AdminFormElement::hasManyLocal('synonyms', [
                AdminFormElement::text('word', 'Синоним'),
            ]),
        ]);

//        $form->getButtons()->replaceButtons(
//            [
//                'save_and_close' => new SaveAndClose(),
//                'save' => new Save(),
//                'cancel' => new Cancel(),
//                'delete' => null,
//            ]
//        );

        return $form;
    }


    public function onCreate()
    {
        $form = AdminForm::card()->addBody([
            AdminFormElement::select('catalog_id', 'Каталог')
                ->setModelForOptions(SearchCatalog::class),
        ]);

        return $form;
    }

    public function onDelete($id)
    {
        // todo: remove if unused
    }
}
