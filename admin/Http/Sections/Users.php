<?php

namespace Admin\Http\Sections;

use AdminColumn;
use AdminDisplay;
use AdminDisplayFilter;
use AdminForm;
use AdminFormElement;
use App\Models\Log;
use App\Role;
use App\Models\Department;
use App\Models\Franchise;
use App\User;
use SleepingOwl\Admin\Contracts\Display\DisplayInterface;
use SleepingOwl\Admin\Contracts\Form\FormInterface;
use SleepingOwl\Admin\Section;

/**
 * Class Users
 *
 * @property \App\User $model
 *
 * @see http://sleepingowladmin.ru/docs/model_configuration_section
 */
class Users extends Section
{
    /**
     * @see http://sleepingowladmin.ru/docs/model_configuration#ограничение-прав-доступа
     *
     * @var bool
     */
    protected $checkAccess = true;

    /**
     * @var string
     */
    protected $title = 'Сотрудники CRM';

    /**
     * @return DisplayInterface
     */

    public function onDisplay()
    {
        $display = AdminDisplay::datatablesAsync()
            ->setApply(function ($query) {
                $query->orderBy('order', 'asc');
                if(!\Auth::user()->isSuperAdmin() && !\Auth::user()->hasRole('personal') && auth()->id() != 130) {
                    $query->excludeOtherFranchises();
                }
            })
            ->with('roles')
            ->setHtmlAttribute('class', 'table-primary')
            ->setColumns([
//                AdminColumn::text('id', 'id')->setWidth('10px'),
                AdminColumn::custom('ФИО',function ($model){
//                    $initials = mb_substr($model->name,0,1,'UTF-8') . '. '.mb_substr($model->midName,0,1,'UTF-8') . '.';
//                    return "<a href='{$this->alias}/{$model->id}/edit'>{$model->lastName} {$model->name} {$model->midName}</a>";
                    return "{$model->lastName} {$model->name} {$model->midName}";
                }),
//                \AdminColumnEditable::select('class', 'Категория',[''=>'-','A'=>'A','B'=>'B','C'=>'C','D'=>'D']),
                \AdminColumnEditable::text('class', 'Категория'),
                \AdminColumnEditable::text('ext', 'Ext'),
                AdminColumn::text('email', 'E-mail')->setWidth('150px'),
                AdminColumn::text('phone', 'Телефон')->setWidth('200px'),
                AdminColumn::lists('roles.display_name', 'Должность'),
                AdminColumn::text('franchise.name', 'Франшиза'),
                AdminColumn::text('department.name', 'Отдел'),
                AdminColumn::custom('Статус', function ($model) {
                    return !is_null($model->status) ? User::STATUS[$model->status] : '-';
                }),
            ])->paginate(20);

        $display->setFilters(
            AdminDisplayFilter::custom('active', 'Активные',function ($query) {
                $query->where('status', '<>', 'fired');
            })
        );

        return $display;
    }

    /**
     * @param int $id
     *
     * @return FormInterface
     */
    public function onEdit($id)
    {
        $form = AdminForm::card();
        $tab = [];
        if (auth()->user()->isSuperAdmin() || auth()->user()->hasRole('personal') || auth()->id() == 130 || auth()->id() == 179 || auth()->id() == 298) {
            $tab['Основная информация'] = new \SleepingOwl\Admin\Form\FormElements([
                AdminFormElement::columns()
                    ->addColumn([
                        AdminFormElement::columns()
                            ->addColumn([
                                AdminFormElement::text('lastName', 'Фамилия'),
                                AdminFormElement::text('email', 'E-mail')->required()->addValidationRule('email'),
                                AdminFormElement::select('status', 'Статус', User::STATUS)->setDefaultValue('new'),
                                AdminFormElement::text('about', 'Подзаголовок'),
                            ])
                            ->addColumn([
                                AdminFormElement::text('name', 'Имя')->required('Пожалуйста, введите имя'),
                                AdminFormElement::password('password', 'Пароль'),
                                AdminFormElement::multiselect('roles', 'Должность')->setModelForOptions(new Role())->setDisplay('display_name')->setReadonly(!auth()->user()->isSuperAdmin() && !auth()->user()->hasRole('personal') && auth()->id() != 130 && auth()->id() != 179 && auth()->id() != 298)
                                    ->setLoadOptionsQueryPreparer(function ($element, $query) { return auth()->user()->isSuperAdmin() ? $query : $query->where('name','<>', 'superadmin'); })
                            ])
                            ->addColumn([
                                AdminFormElement::text('midName', 'Отчество'),
                                AdminFormElement::select('franchise_id', 'Франшиза')->setModelForOptions(new Franchise())->setDisplay('name'),
                                AdminFormElement::select('department_id', 'Отдел')->setModelForOptions(new Department())->setDisplay('name'),
                                AdminFormElement::text('social->vk', 'VK')
                            ])
                    ])
                    ->addColumn([
                        AdminFormElement::image('photo', 'Фото сотрудника')
                            ->setSaveCallback(function ($file, $path, $filename, $settings) use ($id) {
                                $url = $file->storeAs('/Users/'.$id, $file->getClientOriginalName(), 'public');
                                $path = storage_path("public") .'/'. $url;

                                return ['path' => $path, 'value' => asset('storage').'/'.$url];
                            })
                    ],3)
            ]);
            $tab['Общие данные'] = new \SleepingOwl\Admin\Form\FormElements([
                AdminFormElement::columns()
                    ->addColumn([
                        AdminFormElement::text('trainee_dogovor', '№ договора стажировки'),
                        AdminFormElement::text('dogovor_n', '№ договора'),
                        AdminFormElement::text('phone', 'Телефон сотрудника'),
                        AdminFormElement::text('phone_rod', 'Телефон родственника'),
                        AdminFormElement::text('inn', 'Серия и номер свидетельства ИНН'),

                    ])
                    ->addColumn([
                        AdminFormElement::text('work_book', '№ трудовой книжки'),
                        AdminFormElement::date('dogovor_data', 'Дата трудового договора'),
                        AdminFormElement::date('employment_data', 'Дата приема на работу'),
                        AdminFormElement::text('address', 'Место жительства фактическое'),
                        AdminFormElement::text('snils', 'Номер страхового свидетельства'),
                    ])
                    ->addColumn([
                        AdminFormElement::date('trainee_data', 'Дата начала стажировки'),
                        AdminFormElement::date('fired_at', 'Дата увольнения'),
                    ])
            ]);
            $tab['Паспортные данные'] = new \SleepingOwl\Admin\Form\FormElements([
                AdminFormElement::columns()
                    ->addColumn([
                        AdminFormElement::text('pas_rozhd', 'Место рождения'),
                        AdminFormElement::text('pas_n', 'Серия и номер паспорта'),
                    ],3)
                    ->addColumn([
                        AdminFormElement::date('birthday', 'Дата рождения'),
                        AdminFormElement::date('pas_data', 'Дата выдачи паспорта'),
                    ],3)
                    ->addColumn([
                        AdminFormElement::select('pas_pol', 'Пол', ['Ж'=>'Ж','М'=>'М'])->nullable(),
                    ],1),
                AdminFormElement::columns()
                    ->addColumn([
                        AdminFormElement::text('pas_address', 'Прописка по паспорту'),
                    ],3)
                    ->addColumn([
                        AdminFormElement::text('pas_vidan', 'Кем выдан паспорт'),
                    ],3),
            ]);
            $tab['Аттестация'] = new \SleepingOwl\Admin\Form\FormElements([
                AdminFormElement::columns()
                    ->addColumn([
                        AdminFormElement::date('cert_date', 'Дата сдачи аттестации')
//                        AdminFormElement::multiselect('certification', 'Категории')->setModelForOptions(\App\Models\CatalogCertification::class)
//                            ->setLoadOptionsQueryPreparer(function ($element, $query) {
//                                return $query->whereNotNull('parent_id')->doesntHave('goods');
//                            })->setSortable(false)
                    ],3)
            ]);
            $tab['Документы'] = new \SleepingOwl\Admin\Form\FormElements([
                AdminFormElement::html('<iframe width="100%" height="600" src="/elfinder?user_id='.$id.'"></iframe>')
            ]);
        }
        $tab['Активность'] = new \SleepingOwl\Admin\Form\FormElements([
    \AdminSection::getModel(Log::class)->fireDisplay(['scopes' => ['withUser', $id]])
]);
        $tabs = AdminDisplay::tabbed($tab);
        $form->addElement($tabs);
        if (auth()->user()->isSuperAdmin() || auth()->user()->hasRole('personal') || auth()->id() == 130 || auth()->id() == 179 || auth()->id() == 298) {
            $form->addFooter($id ? AdminFormElement::view("admin::comment", ['id' => $id, 'model_type' => \App\User::class, 'type' => 0]) : '');
        } else {
            $form->getButtons()->setButtons([]);
        }

        return $form;
    }

    /**
     * @return FormInterface
     */
    public function onCreate()
    {
        $form = AdminForm::card()->addBody([
            AdminFormElement::text('name', 'Имя')->required('Пожалуйста, введите имя'),
            AdminFormElement::text('email', 'E-mail')->required()->addValidationRule('email'),
            AdminFormElement::password('password', 'Пароль')->required()->addValidationRule('min:6'),
            AdminFormElement::select('franchise_id', 'Франшиза')->setModelForOptions(new Franchise
            ())->setDisplay('name'),
            AdminFormElement::select('department_id', 'Отдел')->setModelForOptions(new Department
            ())->setDisplay('name'),
            AdminFormElement::multiselect('roles', 'Роли')->setModelForOptions(new Role())->setDisplay('display_name'),
            AdminFormElement::select('status', 'Статус', User::STATUS)->setDefaultValue('new')
        ]);
        return $form;
    }

    /**
     * @return void
     */
    public function onDelete($id)
    {
        // todo: remove if unused
    }

    /**
     * @return void
     */
    public function onRestore($id)
    {
        // todo: remove if unused
    }
}
