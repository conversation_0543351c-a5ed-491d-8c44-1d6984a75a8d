<?php

namespace Admin\Http\Sections\RidersDrive;

use Admin\Display\Form\Buttons\Create;
use AdminDisplayFilter;
use App\Models\Bank;
use App\Models\Cart;
use App\Models\Catalog;
use App\Models\CheckType;
use App\Models\Client;
use App\Models\Config\AverageCatalog;
use App\Models\Config\AverageClass;
use App\Models\Config\CatalogClass;
use App\Models\Config\CatalogSales;
use App\Models\Config\CatalogScore;
use App\Models\Config\ConversionCatalog;
use App\Models\Config\ConversionClass;
use App\Models\Config\LeadCatalog;
use App\Models\Config\LeadClass;
use App\Models\Geo\Region;
use App\Models\Money;
use App\Models\Old\OldGeocity;
use App\Models\FormSurcharge;
use App\Models\Order;
use App\Models\RidersDrive\OrderCallrd as OrderCall;
use App\Models\OrderNew;
use App\Models\OrderStatus;
use App\Models\OrderType;
use App\Models\RoboPayment;
use App\Models\SberPayment;
use App\Models\Service;

use App\Models\Task;
use App\Models\TransportCompany\Intercity;
use App\Services\ShortLinks;
use App\User;
use App\View\Components\Helper;
use Auth;
use Carbon\Carbon;
use SleepingOwl\Admin\Section;
use SleepingOwl\Admin\Form\Buttons\Delete;
use SleepingOwl\Admin\Form\Buttons\Save;
use SleepingOwl\Admin\Form\Buttons\SaveAndClose;

use AdminDisplay;
use AdminColumn;
use AdminForm;
use AdminFormElement;
use AdminColumnFilter;
use AdminSection;

class OrderCallrds extends Section
{
    protected $checkAccess = false;
    protected $title = 'Все лиды';
    protected $alias;

    public function onDisplay($payload = [])
    {
        $key = config('franchise.key');
        $display = AdminDisplay::datatablesAsync()->setName('LeadsRD');
        if ($payload) {
            $display->setScopes($payload['scopes']);
        }
        $display->setApply(function ($query) use ($key, $payload) {
            $query->where('deleted_at', null)->whereNull('black_list');
            if (!array_key_exists('scopes', $payload)) {
                $query->whereNull('parent_id');
            }
            $query->orderByRaw("status ='NEW' desc, case when status ='NEW' then concat('-',id) end desc");
        });
        $display->setOrder([[0, 'desc']]);
        $display->with(['task.manager','task.comments.author', 'geo_cities.region.cities', 'old_cities.region', 'order_status', 'call_type', 'comments.author', 'cart.items.catalog.parent', 'cart.goods.price', /*'cart.goods.goods_cities',*/ 'cart.catalog.parent',
            /*'cart.catalog.catalog_cities',*/ 'cart.catalog.suppliers.supplier_city', 'cart.catalog.siblings', 'client.user', 'client.city', 'client.bonuses', 'robofirst', 'sberfirst', 'yafirst', 'yookassafirst', 'glue', 'similar','glue_similar','reason','audits']);
        $user_options = config('franchise.users')->filter(fn($v) => $v->roles->whereIn('name', ['sales', 'remotesales', 'topsales', 'seniorsales','subdostavka'])->count())
            ->mapWithKeys(fn ($item) => [$item->id => $item->fi])->all();
        $seo_list = [null => 'Пусто', 'context' => 'Контекст', 'seo' => 'SEO', 'other' => 'Другое'];
        $category_list = [ 'D' => 'D', 'D1' => 'D1', 'D2' => 'D2', 'D3' => 'D3', 'D4' => 'D4', '0' => 'Другие'];
        $probability = [
            0 => "0% - Отказ от сделки",
            30 => "30% - Слабая вероятность",
            50 => "60% - Средняя вероятность",
            70 => "90% - Высокая вероятность",
//            100 => "100% - Покупает 100%"
        ];
        $probabilities = [
            0 => "0%",
            30 => "30%",
            50 => "60%",
            70 => "90%",
            100 => "100%"
        ];

        $display->setHtmlAttribute('class', 'text-left');

        $goods_id = [];
        if (request()->has('catalog_id')) {
            $cat_ids = Catalog::query()->descendantsAndSelf(request()->get('catalog_id'))->pluck('id')->all();
            $goods_id = \App\Models\Goods::query()->whereIn('catalog_id', $cat_ids)->pluck('id')->all();
        }

        $columns = collect([
            AdminColumn::link('id', '№')->setLinkAttributes(['class' => 'text-secondary', 'target' => '_blank'])//->setHtmlAttribute('class', 'h5')
                ->append(
                    AdminColumn::datetime('created_at', 'Дата')->setFormat("d.m.y H:i")->setHtmlAttribute('class', 'text-nowrap text-muted')
                        ->append(AdminColumn::custom('',function ($model) use ($payload){
//                            if ($model->status == 'NEW') return '';
//                            $first_status_change = $model->audits()->where('created_at','>=',$model->created_at)->whereJsonContains('old_values', ['status' => 'NEW'])->first();
                            $first_status_change = $model->audits->where('created_at', '>=', $model->created_at)
                                ->filter(fn($v) => optional($v->old_values)['status'] == "NEW")->first();
                            $start = $model->created_at;
                            $end = $first_status_change ? $first_status_change->created_at : now();
                            $tz = $model->geo_cities->timezone ?? Carbon::now()->utcOffset() * 60;
                            $tz = Carbon::now()->utcOffset()*60 - $tz;
                            if ($start->diffInDays($end) > 1) return '<b class="badge bg-danger text-nowrap"><i class="far fa-clock"></i> ?</b>';

                            $dates = $start->toPeriod($end, 1, 'minutes')->toArray();
                            $dates = array_filter($dates, function ($date) {
                                $start_day = $date->copy()->setHour(10)->setMinute(0);
                                $end_day = $date->copy()->setHour(19)->setMinute(0);
                                return $date->gte($start_day) && $date->lte($end_day);
                            });
                            $dates = array_filter($dates, function ($date) use ($tz) {
                                $client_start = \Carbon\Carbon::parse($date)->setHour(10)->setMinute(0)->addSeconds($tz);
                                $client_end = \Carbon\Carbon::parse($date)->setHour(24)->setMinute(59)->addSeconds($tz);
                                if ($date->isSaturday() || $date->isSunday()) $client_start->addHours(2);
                                return $date->gte($client_start) && $date->lte($client_end);
                            });
                            $minutes = count($dates);
                            $interval = \Carbon\CarbonInterval::minutes($minutes)->cascade();
                            $color = $minutes > 30 ? 'badge bg-danger text-nowrap' : ($minutes > 15 ? 'badge bg-warning text-nowrap' : '');
                            return '<b title="'.(-$tz/3600).'" class="'.$color.'"><i class="far fa-clock"></i> '.num_diff_time2($interval).'</b>';
                        }))
                ),
            AdminColumn::custom('Важность', $this->cImportance())->setHtmlAttribute('class', 'text-nowrap')
                ->append(AdminColumn::custom('', fn($model) => ($model->geo_cities?->region_id == 22) ? '<i class="badge badge-success">ekb</i>' : '')),
            AdminColumn::custom('Статус', $this->cStatus())->setHtmlAttribute('class', 'text-nowrap')
                ->append(AdminColumn::custom('Склейка', function ($model) use ($probabilities) {
                    $similar_count = $model->similar->where('id', '<>', $model->id)->count();
                    $count = $model->glue_similar->where('id', '<>', $model->id)->count();
                    $out = [];
                    if ($count) $out[] = '<i class="fa fa-clone" aria-hidden="true"></i> Склейка';
                    if ($similar_count) $out[] = '<i class="far fa-copy" aria-hidden="true"></i> Похожие';
                    if ($model->probability) $out[] = 'Вероятность: ' . optional($probabilities)[$model->probability];
                    return implode('<br>', $out);
                })->setHtmlAttribute('class', 'text-nowrap text-muted')),
            AdminColumn::custom('Время у клиента', $this->cTime())->setOrderCallback($this->oTime())->setWidth(50),
            AdminColumn::custom('', fn() => '')->setHtmlAttributes(['class' => 'display:none']), //            AdminColumn::datetime('status_date', '')->setFormat("d.m H:i")->setWidth(1),
            AdminColumn::custom('Дела', $this->cTasks())->setWidth(50),
            AdminColumn::custom('Клиент', $this->cClient())
                ->append(AdminColumn::custom('',fn($model) => $model->client_type ? '<span class="badge bg-'.match ($model->client_type) {'A1','A2'=>'pink','B1','B2'=>'info','C1','C2'=>'primary',default=>'gray'}.' text-nowrap">'.(OrderCall::CLIENT_TYPES[$model->client_type] ?? $model->client_type) .'</span>' : '')),
            AdminColumn::custom('', fn() => '')->setHtmlAttributes(['class' => 'display:none']),
            AdminColumn::custom('Контакты', $this->cPhones()),
            AdminColumn::custom('Open', $this->cOpen()),
            AdminColumn::custom('Тип', $this->cType()),
            AdminColumn::custom('Категория', $this->model->getCartPopup(false, $goods_id)),
            AdminColumn::custom('Сумма', $this->cSumm())->setHtmlAttributes(['class' => 'text-right']),
            AdminColumn::custom('Город', $this->cCity()),
            \AdminColumnEditable::select('user_id', 'Менедж.', $user_options)->setWidth(150)
                ->setModifier(function ($value) {
                    if (is_null($value->getModelValue()))
                        return '<div class="btn btn-sm btn-outline-secondary text-nowrap">Выбрать менеджера</div>';
                })
                ->setTitle('Выберите ответственного:')->setOrderCallback(function ($c, $q, $s) {
                    $q->orderBy('user_id', $s);
                })
                ->append(AdminColumn::custom('Комментарии', $this->model->cComment())),
            AdminColumn::custom('', $this->cClass()),
            AdminColumn::custom('', fn() => ''),
            AdminColumn::custom('', fn() => ''),
            AdminColumn::custom('', fn() => ''),
            AdminColumn::custom('', fn() => ''),
        ]);

        unset($seo_list[null]);

        $c_attr = collect([
            'f_callbacks' =>
                [
                    null, $this->fTasks(), $this->fPhones(), $this->fType(), $this->fCatalog(),
                    $this->fSumm(), $this->fCity(), $this->fRegion(), $this->fReason(), $this->fStatus(), null,
                    /*$this->fDelivery()*/ $this->fClassCategory(),$this->fSex(), null, $this->fGoods(),$this->fProbability(),$this->fClient(),$this->fClientype(),
//                    $this->fComment(), $this->fSeason(), $this->fScores(), $this->fNames(),
                ],
        ]);
        $columns = $columns->each(function ($item, $key) use ($c_attr) {
            if (optional($c_attr['f_callbacks'])[$key]) $item->setFilterCallback($c_attr['f_callbacks'][$key]);
            $title = $item->getHeader()->getTitle();
            $item->getHeader()->setTitle('<span class="position-relative">' . $title . renderComponent(new Helper("display:{$this->alias}.{$title}", true)) . '</span>');
            return $item;
        })->all();


        $display->setColumns($columns);


        $display->setRowClassCallback(function ($model) {
            $group = OrderCall::getStatusGroup($model->status);
            if (!$group) return '';
            return 'row-' . $group['class'];
        });

        if ($payload) return $display;

        $catalogs_opts = \Cache::remember('lead.filter.catalogs', now()->addHour(), fn() => \App\Models\Catalog::withDepth()->where('show', true)->get()->pluck('full_title','id')->all());
        $brands_opts   = \Cache::remember('lead.filter.brands',   now()->addHour(), fn() => \App\Models\Brand::query()->pluck('name','id')->all());
        $cities_opts   = \Cache::remember('lead.filter.cities',   now()->addHour(), fn() => \App\Models\Geo\City::query()->orderBy('name')->pluck('name','id')->all());
        $regions_opts  = \Cache::remember('lead.filter.regions',  now()->addHour(), fn() => \App\Models\Geo\Region::query()->orderBy('name')->pluck('name','id')->all());
        $statuses_opts = \Cache::remember('lead.filter.statuses', now()->addHour(), fn() => \App\Models\OrderStatus::query()->orderBy('order')->pluck('name', 'slug')->all());
        $types_opts    = \Cache::remember('lead.filter.types',    now()->addHour(), fn() => \App\Models\OrderType::query()->whereNotIn('alias', ['сondition', 'subscrible', 'moneyonphone'])->pluck('name', 'alias')->all());

        $display->setColumnFilters([
            AdminColumnFilter::daterangex()->setPlaceholder('Дата')->setColumnName('created_at'),
            AdminColumnFilter::select()->setOptions(['Важные лиды', 'Замечания'])->setPlaceholder('Важность')->setHtmlAttribute('data-id', 'importance'),
//            AdminColumnFilter::checkbox(null, 'Важные лиды'),//            AdminColumnFilter::daterangex()->setPlaceholder('Д. статуса')->setColumnName('status_date')->setHtmlAttribute('data-id', 'sdate'),
            AdminColumnFilter::text()->setPlaceholder('#/Телефон/Имя/Email'),
            AdminColumnFilter::select()->setOptions($types_opts)->setPlaceholder('Тип'),
//            AdminColumnFilter::select(Catalog::SEASONS)->setPlaceholder('Сезонность'),
            AdminColumnFilter::select()->setOptions($catalogs_opts)->setPlaceholder('Категория'),
            AdminColumnFilter::select()->setOptions($brands_opts)->setPlaceholder('Бренд'),
//            AdminColumnFilter::select(['Не оплачено', 'Оплачено'])->setPlaceholder('Робокасса')->setSortable(false),
//            AdminColumnFilter::text()->setPlaceholder('Город/Область'),
            AdminColumnFilter::select()->setOptions($cities_opts)->setPlaceholder('Город'),
            AdminColumnFilter::select()->setOptions($regions_opts)->setPlaceholder('Область'),
            AdminColumnFilter::select()->setOptions(\App\Models\RejectReason::query()->where('show',1)->pluck('title','id')->all())->setPlaceholder('Причина отказа'),
            AdminColumnFilter::select()->setOptions($statuses_opts)->setSortable(false)->setPlaceholder('Статус')->setHtmlAttribute('data-id', 'status'),
            AdminColumnFilter::select()->setOptions($user_options)->setColumnName('user_id')->setPlaceholder('Менеджер')->setHtmlAttribute('data-id', 'user'),
//            AdminColumnFilter::select(['1' => 'КИТ', '0' => 'Почта'])->setPlaceholder('Доставка'),
//            AdminColumnFilter::select($seo_list)->setSortable(false)->setPlaceholder('Рекламный канал'),
            AdminColumnFilter::select()->setOptions($category_list)->setSortable(false)->setPlaceholder('Класс категории'),
            AdminColumnFilter::select()->setOptions(['1' => 'М', '0' => 'Ж'])->setSortable(false)->setPlaceholder('Пол'),
            AdminColumnFilter::select()->setOptions(['1' => 'Качественный', '0' => 'Не качественный'])->setColumnName('class')->setSortable(false)->setPlaceholder('Качество лида'),
            AdminColumnFilter::text()->setPlaceholder('Артикул товара'),
            AdminColumnFilter::select()->setOptions($probability)->setPlaceholder('Вероятность, (%)')->setSortable(false),
            AdminColumnFilter::select()->setOptions(Client::TYPES)->setPlaceholder('Тип клиента')->setSortable(false),
            AdminColumnFilter::select()->setOptions(OrderCall::CLIENT_TYPES)->setPlaceholder('Классификация клиента')->setSortable(false),
            AdminColumnFilter::checkbox(null, 'СВО')->setColumnName('is_svo')
        ]);

        $display->setFilters(
            AdminDisplayFilter::scope('default', config('alfamart.franchise')[$key]['title']),
            AdminDisplayFilter::scope('myleads', 'МОИ ЛИДЫ'),
            AdminDisplayFilter::scope('tasks', 'ПРОСРОЧЕННЫЕ ДЕЛА'),
            AdminDisplayFilter::scope('isur', 'ЮР. ЛИЦА'),
            AdminDisplayFilter::scope('isdg', 'ДИЗАЙНЕРЫ'),
            AdminDisplayFilter::scope('isst', 'СТРОИТЕЛИ'),
            AdminDisplayFilter::scope('isam', 'ALFAMART24'),
            AdminDisplayFilter::scope('isrd', 'RIDERSDRIVE'),
            AdminDisplayFilter::scope('sales', function ($id) {
                return $id == 1 ? 'ВАЖНЫЕ' : 'НЕ МЕНЕЕ ВАЖНЫЕ';
            })
        );

        view()->composer(\AdminTemplate::getViewPath('_partials.header'), function ($view) {
            $lead_id = $order_id = $supplier_id = $taskable_id = $taskable_type = false;
            $view->getFactory()->startPush('navbar.buttons', view('admin::datatables.tasks_toolbar', compact('lead_id','order_id','supplier_id','taskable_id','taskable_type')));
        });

        view()->composer(\AdminTemplate::getViewPath('_layout.inner'), function ($view) use ($key, $user_options) {
            $view->getFactory()->startPush('content.header', $this->getHeaderHtml($key));
            $view->getFactory()->startPush('panel.filters.before', $this->getPanelButtons($user_options));
        });
        $display->getActions()->setView(view('infoblock.orders', ['html' => $this->getCounts(), 'is_leads' => true]))->setPlacement('before.card');

        $display->getColumns()->disableControls();

        return $display;
    }

    public function onEdit($id)
    {
//        if ($id) $this->Log('view', $id);

        $model = $this->model::with('audits.user', 'audits.auditable', 'cart.order.geo_cities', 'geo_cities', 'lead')->find($id);
        $display = AdminDisplay::tabbed();
        if (!$model) {
            $display->appendTab($this->tGeneral($id, $model), 'Основные')->setIcon(renderComponent(new Helper('edit:order_calls.Основные')));
        } else {
            \Meta::loadPackage(['stopPageRefresh']);
            $cart = $id ? $model->cart : false;
            $cart_ids = $cart ? $cart->pluck('id')->toArray() : false;
            $paid_amount = ($cart) ? $cart->map(function ($value) {
                return $value->quantity * $value->price;
            })->sum() : false;
            $client = $model->getClient();

            $display->appendTab($this->tGeneral($id, $model, $cart, $paid_amount), 'Основные')->setIcon(renderComponent(new Helper('edit:order_calls.Основные'))." <i class=\"{$model->call_type->icon}\" aria-hidden=\"true\" title='{$model->call_type->name}'></i>");
            $display->appendTab($this->tabTasks($id), 'Дела')->setBadge(function () use ($id, $model) {
                return Task::withLead($id, optional($model->order)->id)->where('status_id','<>','complete')->count();
            })->setHtmlAttribute('style','background-color: #4099FC')->setIcon(renderComponent(new Helper('edit:order_calls.Дела',null,'white')));

            if ($client) {
                \View::share('custom_title', $client->fio);
                if ($cart && count($cart_ids)) $display->appendTab($this->tabDuty($id, $cart_ids), 'Заказ-наряды')->setIcon(renderComponent(new Helper('edit:order_calls.Заказ-наряды')))->setBadge(fn() => Service::withLead($cart_ids)->where('status','new')->count());
                $display->appendTab($this->tClient($client), 'Клиент')->setIcon(renderComponent(new Helper('edit:order_calls.Клиент')));
                $display->appendTab($this->tabDocs($id, optional($client)->type ?: 0), 'Документы')->setIcon(renderComponent(new Helper('edit:order_calls.Документы')));
//                $email = $this->tabEmail($model, $cart, $paid_amount);
//                $display->appendTab($email, 'Почта');
                $display->appendTab($this->tabEmailTemplates($model, $cart, $paid_amount), 'Шаблоны')->setIcon(renderComponent(new Helper('edit:order_calls.Шаблоны')));
                $display->appendTab($this->tabMoney($model), 'Деньги')->setIcon(renderComponent(new Helper('edit:order_calls.Деньги')))
                    ->setBadge(function () use ($model) {
                        $leadId = $model->id;
                        $orderId = optional($model->lead)->id;
                        return Money::withLead($leadId, $orderId)->count();
                    });


                if (!empty($client->phone) && !empty(auth()->user()->ext)) {
                    $date_from = now()->addMonths(-1)->timestamp;
                    $date_to = now()->timestamp;
                    $client_phone = preg_replace(["/^(8)/", "/[^0-9]/"], ["7", ""], $client->phone);
                    $display->appendTab(AdminFormElement::html('<mango-record ext="' . auth()->user()->ext . '" :number="' . $client_phone . '" :date-from="' . $date_from . '" :date-to="' . $date_to . '" />'), 'Телефония')->setIcon(renderComponent(new Helper('edit:order_calls.Телефония')));
                }
            }

//            $emailLogs = AdminSection::getModel(EmailLog::class)->fireDisplay(['scopes' => ['withEmail', $model->email]]);
//            $display->appendTab($emailLogs, "Логи писем");
            if (!empty($model->phone)) {
                if ($similar_count = \App\Models\OrderCall::withLeadExcludeMonth($model->id, $model->phone)->count()) {
                    $similar = AdminSection::getModel(\App\Models\OrderCall::class)->fireDisplay(['scopes' => ['withLeadExcludeMonth', $id, $model->phone]]);
                    $similar->setView('display.table_without_create');
                    $display->appendTab($similar, "Похожие лиды")->setBadge($similar_count)->setHtmlAttribute('style','background-color: #FFCBDB')->setIcon(renderComponent(new Helper('edit:order_calls.Похожие лиды')));
                }
//                if ($glue_count = OrderCall::where('parent_id', $id)->count()) {
//                if ($model->parent_id) {
//                    $glue_id = $model->parent_id;
//                } else {
//                    $glue_id = $id;
//                }
//                if ($glue_count = OrderCall::where('parent_id', $glue_id)->count()) {
                if ($glue_count = \App\Models\OrderCall::withLeadPerMonth($id, $model->phone)->count()) {
                    $glue = AdminSection::getModel(\App\Models\OrderCall::class)->fireDisplay(['scopes' => ['withLeadPerMonth', $id, $model->phone]]);
                    $glue->setView('display.table_without_create');
                    $display->appendTab($glue, "Склейка")->setBadge($glue_count)->setHtmlAttribute('style','background-color: #FFCBDB')->setIcon(renderComponent(new Helper('edit:order_calls.Склейка')));
                }
            }
            $this->title = $id;
        }
//        $display->appendTab($this->tCalc(), 'Рассрочка')->setIcon("<i class=\"fa fa-university\" aria-hidden=\"true\"></i>");

        $audits = $id ? $model->audits : false;
        if ($audits) {
            $count = $audits->count();
            $statuses = OrderStatus::all()->map(function ($v) {
                return ['slug' => $v->slug, 'name' => $v->name, 'color' => $v->color ?: 'gray'];
            });
            $audits = $audits->groupBy(function ($v) {
                return $v->created_at->format("d.m.Y");
            })->map(function ($v) {
                return $v->groupBy(function ($v) {
                    return $v->created_at->format("H:i:s");
                });
            });
            $audit = AdminFormElement::view('admin::audit', compact('audits', 'statuses'));
            $display->appendTab($audit, 'История изменений')->setBadge($count)->setIcon(renderComponent(new Helper('edit:order_calls.История изменений')));
        }
//        if ($id) {
//
//            $tasks = AdminSection::getModel(Task::class)->fireDisplay(['scopes' => ['withLead', $model->id]]);
//            $tasks->setParameter('ordercall_id', $id);
//            //$tackCount = 0;
//            $display->appendTab($tasks, 'Дела');//->setBadge($count);
//        }

        return $display;
    }

    public function onCreate()
    {
//        $this->Log('create');
        return $this->onEdit(null);
    }

    public function onDelete($id)
    {
//        $this->Log('delete', $id);
        // todo: remove if unused
    }

    public function isDeletable(\Illuminate\Database\Eloquent\Model $model)
    {
        return $model->name && $model->comments->count();
        return $model->name && $model->comments->count() && (auth()->user()->hasRole(['topsales','seniorsales']) || auth()->user()->isAdmin() || auth()->id() == 179);
//        return $model->name && $model->comments->count(); //auth()->user()->hasRole('topsales') || auth()->user()->isAdmin();
    }

    /**
     * @return void
     */
    public function onRestore($id)
    {
        // todo: remove if unused
    }

    public function getEditTitle()
    {
        return 'Редактирование лида №' . $this->title;
    }

    public function getCatalogClass($prev = false)
    {
        $date = now();
        if ($prev) $date->addMonth(-1);
        $year = $date->year;
        $month = $date->month;
        return Order::with('lead.cart.catalog.parent')
            ->whereNotIn('status', ['RETURN', 'NEEDRETURN', 'PRETENSE', 'INPROCESSCREDIT'])
            ->whereYear('date', $year)->whereMonth('date', $month)->get()
            ->map(function ($v) {
                return $v->lead->cart;
            })->flatten()
            ->filter(function ($v) {
                return $v->catalog;
            })
            ->groupBy(function ($v) {
                $class = '';
                if (!empty($v->catalog->class)) $class = $v->catalog->class . $v->catalog->subclass;
                if (!$class && $v->catalog->parent) $class = $v->catalog->parent->class . $v->catalog->parent->subclass;
                return $class;
//                return optional($v->catalog)->class ?: optional($v->catalog->parent)->class;
            })
            ->filter(function ($v, $k) {
                return !empty($k) && in_array($k, ['D','D1','D2','D3','D4']);
            })
            ->map(function ($v) {
                $sum = $v->sum(function ($v) {
                    return $v->quantity * $v->price;
                });
                return $sum; //number_format($sum, 0, '.', ' ') . ' р.';
            });
    }

    public function getLeadsCatalogClass($leads) {
        $lead_cat = [];
        $tmp = $leads->map(function ($v) use (&$lead_cat) {
            return $v->cart->map(function ($v) use (&$lead_cat) {
                if (!$v->catalog) return '';
//                $class = optional($v->catalog)->class ?: optional(optional($v->catalog)->parent)->class;
                $class = '';
                if (!empty($v->catalog->class)) $class = $v->catalog->class . $v->catalog->subclass;
                if (!$class && $v->catalog->parent) $class = $v->catalog->parent->class . $v->catalog->parent->subclass;

                if (empty($class)) {
                    $class = 'Z';
                    if (!in_array($v->order_call_id,$lead_cat ?: [])) $lead_cat[] = $v->order_call_id;
                }
                return $class;
            })->unique()->toArray();
        });
        return [
//            'Z' => !empty($lead_cat) ? array_map(function ($v) { return "<a class='btn btn-default btn-xs' href='/order_calls/{$v}/edit' target='_blank'>{$v}</a>"; }, $lead_cat) : null,
//            'A'=> $tmp->filter(function ($v) { return in_array('A', $v); })->count(),
//            'B'=> $tmp->filter(function ($v) { return in_array('B', $v); })->count(),
//            'C'=> $tmp->filter(function ($v) { return in_array('C', $v); })->count(),
            'D'=> $tmp->filter(fn($v) => array_intersect($v, ['D','D1','D2','D3','D4']))->count(),
            'D1'=> $tmp->filter(fn($v) => in_array('D1', $v))->count(),
            'D2'=> $tmp->filter(fn($v) => in_array('D2', $v))->count(),
            'D3'=> $tmp->filter(fn($v) => in_array('D3', $v))->count(),
            'D4'=> $tmp->filter(fn($v) => in_array('D4', $v))->count(),
//            ''=> $tmp->filter(function ($v) { return in_array('Z', $v); })->count(),
        ];
    }

    public function getConversionCatalogClass($leads, $orders) {
        return $leads->map(function ($v) use ($orders){
            $item = $v->cart->sortBy('is_module')->first();
            if (!$item /*|| !optional($item)->item_id*/) return false;

            $catalog = $item->catalog;
            if (!$catalog && $item->item_id) $catalog = optional($item->goods)->catalog;
            if (!$catalog) return false;

//            if (optional($item)->item_id && $catalog->parent_id) $catalog = $catalog->parent;
//            $class = optional($catalog)->class ?: optional(optional($catalog)->parent)->class;
            $orders = $orders->where('lead_id', $v->id);
            $carts = $orders->sum(function ($v) {
                return $v->lead->cart->sum(fn($v) => $v->quantity * $v->price);
            });

            return [
//                'class' => $class,
                'catalog' => $catalog->id,
                '_lft' => $catalog->_lft,
                'id' => $v->id,
                'order' => $orders->count(),
                'carts' => $carts
            ];
        })->filter();
    }

    public function getOrdersCountClass($orders) {
        return $orders->whereNotIn('status',['RETURN','PRETENSE'])
            ->groupBy(function ($v) {
                return $v->lead->cart->map(function ($v) {
                    if (!$v->catalog) return '';
                    $class = '';
                    if (!empty($v->catalog->class)) $class = $v->catalog->class . $v->catalog->subclass;
                    if (!$class && $v->catalog->parent) $class = $v->catalog->parent->class . $v->catalog->parent->subclass;
                    return $class;
//                    return optional($v->catalog)->class ?: optional($v->catalog->parent)->class;
                })->unique()->first();
            })->filter(function ($v, $k) {
                return !empty($k) && in_array($k, ['D','D1','D2','D3','D4']);
            })->map(function ($v, $k) {
                return count($v);
            })->all();
    }

    public function cImportance()
    {
        return static function ($model) {

            $out = [];
            if ($model->importance >= 9) {
                $smile = '<img alt="🔥" width="15" src="data:image/png;base64,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">';
//            } elseif ($model->importance <= 5) {
//                $smile = '<img alt="☁" width="15" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAABCUExURUdwTJK62Zy/27XJ2aLC26XC2qDB25e82aPE3oq22bnL2rTc+qzY+s3o+6XU+sPj+7vf+9ru/ZrO+b7R36TG4pLB6DFW2y8AAAALdFJOUwDMeuxRnCz7FPnDJAWUfwAAAvhJREFUWMPtl9l6gyAUhAMCgqIYhfd/1Z7DelyyNO1lJsnXXoTfmQGR3G5fffXVZ9Kgv0MUlwNKCvUXjBi6raiTH6OUhPHTdEdNiOKfRRTgJlGS3LYN6jMOxSRXH5DOHELSSgih3gqqhhMH2gKS1Dchhw418Df8yTMHUQ4al533m908iL2sH4LdT8FQQOq2cXELvEfve6l/awgcJdC2OdSCsq9IetguMUhqmBHke/7LZFMBRdTiMsl69qxxfkiGhHvDJA4aAtBTS4eKppLMJdLSoo2zZ/o5aDpSSEPJELwtWnqcTcstRqGIhCEF5WgAEo/2Dli4W22FuDkWjZiHIM3jFuSIjzudrpOhByAlAbO46SRXiz4Yuu5IDR7WLQ67ptSiEwhJM8y/OO7DOnLysCtK8UMMAQjE5C6fjH4qKQEuOMmQjaR59j6EvjfkphOdX/KIjHAEQuwQQwBCrYGSpMf9wTVlSmvHXXJWfK2hkhQY2oOoip0dZ54LCWRM7kn0AIpfP9LSPkbt5IJKsAhaTb7ruLdLoqRXm+2yBst05Vh2bsFQIVvCioraULfHxD0oryBLg0UQzyD4xkJZBFHNXBhaK0g20EhZVGNtObeT+yHR1gziERRHnChjKblwTgWBoeIIZq25XzIyG6m5LvyQaKkj1edshVTGHyl02klDsJDyvSvBEkHtITRUWYg7P2Co7N6QzdoTqkKIHXvhJ9SVDXUjKQ4YGw//bxCCOfox7bmkWe/ngjp8aqpac+KEZAc55LGkMilq3P3ZTdZacwHAIMUcDiZK9r1vLHuAFDdzuysYY0Bhp6OS5qxHFoHNBLKWuaqGhFYgff1cYwDzqY+Iqc3A/rzS2QrmxalGiVhWBtRAK+7zfq3bBgR7efjjeO2o1gv44XCFEOqEv+ZAV3DteSfY32FmBDM9sOJMvXWAR1JY29ILiZM6NJcT9fDYhvMHOaAUH/AhKEiHb56y61IwfZZ538D1EYfDWmBMcvEPP9n+43ffV199lfUDVWJ6E0p4y40AAAAASUVORK5CYII=">';
            } else {
//                $smile = '<img alt="☀" width="15" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAAMFBMVEVHcEz/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDNIzNElAAAAD3RSTlMAYCCA7xBAz3DfjzCfr7/eJ7hYAAABgklEQVR42u2Y627DIAyFDZiQ5ub3f9tJaTcr9cIxIoqmqd+/lHCgwYZjCDKyCI/UTZCdQL08ZOdBvciL/OeE6CN05fLHlFL0B2RM0/66YeZnB5wiOgDPZIhsh8aZw3ZOSV4En86LZJoWaVAKci6U5IcIl0WMEGiMYSmyU5YQwaB2uhM9GVY5sA5GKDg+4LCJYRvMwqAlzZP8ypQ9oaJBloucULIGL86SmeUUnjU/EJmlAmdykotUKV6lSQATuRgEMpCHTSBbw4T6p7SKg5UgUVxE31aDCYRYxMVChvB94DwHKeKimL6jNo56IGKI3vqyPnCj0LHvW2NsETo8VxqbhC77a5d97KuWvzsg+1PkhqTt30b6N7b+rbZ/87/3OMIHJCm3HNnYRHhBtgagfh4bLa0RgNWqWz+1WdjP18yoNX7Yz1t7bK0o9vM4XkOTn8c1An6hdUDg53GNgAstLfxQkYVLv1D5kWMtwwIsjjVwQX6AKw2tET53I//gbuSWyzr/9eEXUsdxIFCaGjcAAAAASUVORK5CYII=">';
                $smile = '';
            }
            // dd($smile);
            $out[] = $model->status != 'TRANSFER' ? $model->importance ? $smile : '' : '';

            $tasks_count = $model->task->where('type','obrabotkalida')->count();
            if ($tasks_count) {
                $out[] = ' <i title="Замечание" class="text-red fa fa-lg fa-circle-exclamation"></i>';
            }
            return implode('',$out);
        };
    }

    public function cFat()
    {
        return static function ($model) {

            $cart = $model->cart;
            $paid_amount = $cart->map(function ($value) {
                return $value->quantity * $value->price;
            })->sum();

            $score =
                getScoreByType($model) +
                getScoreByPrice($paid_amount) +
                getScoreByTypePay($model);
            //$title = "[" . getScoreByType($model) . "," . getScoreByPrice($paid_amount) . "," . getScoreByTypePay($model) . "]";

            if ($score >= 9) {
                $smile = '<img alt="🔥" width="15" src="data:image/png;base64,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">';
            } elseif ($score <= 5) {
                $smile = '<img alt="☁" width="15" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAABCUExURUdwTJK62Zy/27XJ2aLC26XC2qDB25e82aPE3oq22bnL2rTc+qzY+s3o+6XU+sPj+7vf+9ru/ZrO+b7R36TG4pLB6DFW2y8AAAALdFJOUwDMeuxRnCz7FPnDJAWUfwAAAvhJREFUWMPtl9l6gyAUhAMCgqIYhfd/1Z7DelyyNO1lJsnXXoTfmQGR3G5fffXVZ9Kgv0MUlwNKCvUXjBi6raiTH6OUhPHTdEdNiOKfRRTgJlGS3LYN6jMOxSRXH5DOHELSSgih3gqqhhMH2gKS1Dchhw418Df8yTMHUQ4al533m908iL2sH4LdT8FQQOq2cXELvEfve6l/awgcJdC2OdSCsq9IetguMUhqmBHke/7LZFMBRdTiMsl69qxxfkiGhHvDJA4aAtBTS4eKppLMJdLSoo2zZ/o5aDpSSEPJELwtWnqcTcstRqGIhCEF5WgAEo/2Dli4W22FuDkWjZiHIM3jFuSIjzudrpOhByAlAbO46SRXiz4Yuu5IDR7WLQ67ptSiEwhJM8y/OO7DOnLysCtK8UMMAQjE5C6fjH4qKQEuOMmQjaR59j6EvjfkphOdX/KIjHAEQuwQQwBCrYGSpMf9wTVlSmvHXXJWfK2hkhQY2oOoip0dZ54LCWRM7kn0AIpfP9LSPkbt5IJKsAhaTb7ruLdLoqRXm+2yBst05Vh2bsFQIVvCioraULfHxD0oryBLg0UQzyD4xkJZBFHNXBhaK0g20EhZVGNtObeT+yHR1gziERRHnChjKblwTgWBoeIIZq25XzIyG6m5LvyQaKkj1edshVTGHyl02klDsJDyvSvBEkHtITRUWYg7P2Co7N6QzdoTqkKIHXvhJ9SVDXUjKQ4YGw//bxCCOfox7bmkWe/ngjp8aqpac+KEZAc55LGkMilq3P3ZTdZacwHAIMUcDiZK9r1vLHuAFDdzuysYY0Bhp6OS5qxHFoHNBLKWuaqGhFYgff1cYwDzqY+Iqc3A/rzS2QrmxalGiVhWBtRAK+7zfq3bBgR7efjjeO2o1gv44XCFEOqEv+ZAV3DteSfY32FmBDM9sOJMvXWAR1JY29ILiZM6NJcT9fDYhvMHOaAUH/AhKEiHb56y61IwfZZ538D1EYfDWmBMcvEPP9n+43ffV199lfUDVWJ6E0p4y40AAAAASUVORK5CYII=">';
            } else {
                $smile = '<img alt="☀" width="15" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAAMFBMVEVHcEz/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDP/rDNIzNElAAAAD3RSTlMAYCCA7xBAz3DfjzCfr7/eJ7hYAAABgklEQVR42u2Y627DIAyFDZiQ5ub3f9tJaTcr9cIxIoqmqd+/lHCgwYZjCDKyCI/UTZCdQL08ZOdBvciL/OeE6CN05fLHlFL0B2RM0/66YeZnB5wiOgDPZIhsh8aZw3ZOSV4En86LZJoWaVAKci6U5IcIl0WMEGiMYSmyU5YQwaB2uhM9GVY5sA5GKDg+4LCJYRvMwqAlzZP8ypQ9oaJBloucULIGL86SmeUUnjU/EJmlAmdykotUKV6lSQATuRgEMpCHTSBbw4T6p7SKg5UgUVxE31aDCYRYxMVChvB94DwHKeKimL6jNo56IGKI3vqyPnCj0LHvW2NsETo8VxqbhC77a5d97KuWvzsg+1PkhqTt30b6N7b+rbZ/87/3OMIHJCm3HNnYRHhBtgagfh4bLa0RgNWqWz+1WdjP18yoNX7Yz1t7bK0o9vM4XkOTn8c1An6hdUDg53GNgAstLfxQkYVLv1D5kWMtwwIsjjVwQX6AKw2tET53I//gbuSWyzr/9eEXUsdxIFCaGjcAAAAASUVORK5CYII=">';
            }
            return $model->status != 'TRANSFER' ? $smile : '';
        };
    }

    // todo custom columns
    public function cTime()
    {
        return function ($model) {
            if ($model->city == '') return '-';
            $city = $model->geo_cities;
            if (!$city) return '';
            return Carbon::now('UTC')->addSecond($city->timezone)->format("H:i");
        };
    }

    public function oTime()
    {
        return function ($c, $q, $s) {
            $sort = strtolower($s) == 'desc' ? 'sortByDesc' : 'sortBy';
            $cities = OrderCall::with('geo_cities')
                ->where('city', '<>', '')->get()
                ->$sort(function ($v) {
                    $city = $v->geo_cities;
                    if (!$city) return '';
                    $time = Carbon::now('UTC')->addSecond($city->timezone)->timestamp;
                    return $time;
                })->pluck('id')->implode(',');
            $q->orderByRaw('FIELD(id,' . $cities . ')');
        };
    }

    public function cClient()
    {
        return function ($model) {
            $client = $model->client;
            $content = $client ? $this->getClientCardPopup($client, 'client_popup') : '';
            $name = $model->name; //$client ? $client->fio : $model->name;
            if (empty(trim($name))) $name = '<i class="text-muted">имя не указано</i>';
            $in_process = $model->in_process ? ' <b class="fa fa-check text-success"></b>' : '';
            $is_ur = $client?->type ? ' <span class="badge bg-info">'.Client::TYPES[$client->type].'</span>' : '';
            return  $client
                ? '<a tabindex="0" role="button" data-html="true" data-toggle="popover" data-trigger="hover focus" data-content="' . htmlentities($content) . '" title="" href="/order_callrds/' . $model->id . '/edit" target="_blank"><i class="fa fa-user-tie"></i> '.$name.'</a> '. $in_process . $is_ur
                : '<a href="/order_callrds/' . $model->id . '/edit" target="_blank">'.$name.'</a> '. $in_process . $is_ur;
        };
    }

    public function cTasks()
    {
        return function ($model) {
            $tasks = $model->task->where('status_id', '<>', 'complete');
            $content = '';
            $expired = false;
            foreach ($tasks as $task) {
                $date = Carbon::parse($task->date);
                $title = $task->title;
                $manager = optional($task->manager)->fi;
                $content = $content . "<a href='/tasks/$task->id/edit' target='_blank'>$title</a> {$date->format('d.m.y')} ($manager)<br>";
                $expired = $expired ?: $date->lt(Carbon::now());
            }
            $action_color = $expired ? 'red' : 'green';
            $action = count($tasks) ? '<a tabindex="0" data-html="true" data-toggle="popover" data-trigger="hover focus"
            class="text-'.$action_color.'"
            data-content="'.htmlspecialchars($content).'" data-original-title="" data-title="Дела"
            title=""><i class="fa fa-lg fa-bell"></i></a>' : '';

            $last = $model->comments->last();
            $missed = $last && strpos($last->message, 'пропущен') !== false ? '<i class="fa fa-reply-all" title="Пропущенный"></i>' : '';

            $is_repair = $model->is_repair ? '<i class="fa fa-wrench"></i>' : '';
            $is_svo = $model->is_svo ? '<i class="fas fa-peace"></i>' : '';

            return "{$missed} {$action} {$is_repair} {$is_svo}";



            $count = $model->task->where('status', 0)->count();
            $tasks = $count ? '<small class="badge bg-blue">' . $count . '</small>' : '';
            $status_date = Carbon::parse($model->status_date);
            $action_color = (now()->gt($status_date)) ? 'red' : 'green';
            $status = $model->status == 'REJECT' || $model->status == 'AFTER' || $model->status == 'TRANSFER';
            $action = (!$status && $model->status_date) ? '<a tabindex="0" data-html="true" class="text-' . $action_color . '"
data-toggle="popover" data-trigger="hover focus" data-content="' . $model->action . '" data-original-title="" data-title="' . $status_date->format("d.m.Y H:i") . '"
title=""><i class="fa fa-bell"></i></a>' : '';
            $is_repair = $model->is_repair ? '<i class="fa fa-wrench"></i>' : '';
            $last = $model->comments->last();
            $missed = $last && strpos($last->message, 'пропущен') !== false ? '<i class="fa fa-reply-all" title="Пропущенный"></i>' : '';
            return "{$missed} {$action} {$is_repair} {$tasks}";
        };
    }

    public function fTasks()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;

            switch ($search) {
                case 0:
                    $query->where('importance', '>=',9)->whereNotIn('status', ['TRANSFER','REJECT']);
                    break;
                case 1:
                    $ids = \App\Models\Task::query()->where('type', 'obrabotkalida')->whereNotNull('ordercall_id')->pluck('ordercall_id')->all();
                    $query->whereIn('id',$ids);
                    break;
            }
//            $query->where('status_date', '>=', $search[0])
//                ->where('status_date', '<=', $search[1])
//                ->whereNotIn('status', ['REJECT', 'AFTER', 'TRANSFER']);
        };
    }

    public function cPhones()
    {
        return function ($model) {
            $text = $model->phone ? $model->phone . '&nbsp' : '';
            if ($model->email || $model->whatsapp || $model->viber) $text .= "<span style='color: #00a65a'>@</span>";
            return "<a href='/{$this->alias}/{$model->id}/edit' target='_blank'>{$text}</a>";
        };
    }

    public function cOpen()
    {
        return function ($model) {
            $url = $model->first_url;
            $color = \Str::contains($url, 'REKLAMA') ? 'text-green' : '';
            return $url ? "<a class='{$color}' href='{$url}' target='_blank'><i class='fa fa-link' aria-hidden='true' title='Первая ссылка'></i></a>" : '';
        };
    }

    public function fPhones()
    {
        return function ($column, $query, $search) {
            if (!$search) return;
            $query->where(function ($q) use ($search) {
                $client_fields = ['name', 'phone', 'address', 'phone2', 'viber', 'whatsapp', 'vk', 'instagram', 'email'];
                $client_ids = Client::query();
                foreach ($client_fields as $field) $client_ids->orWhere($field, 'like', "%$search%");
                $client_ids = $client_ids->pluck('id')->toArray();

                $q->where('id', '=', "$search")->orWhere('client_id', $search);
                $search_fields = ['name', 'phone', 'address', 'phone2', 'phone3', 'phone4', 'viber', 'whatsapp', 'telegram', 'email'];
                foreach ($search_fields as $field) $q->orWhere($field, 'like', "%$search%");
                if (count($client_ids)) $q->orWhereIn('client_id', $client_ids);
            });
        };
    }

    public function fNames()
    {
        return function ($column, $query, $search) {
            $query->where('name', 'like', "%$search%");
        };
    }

    public function fCatalog()
    {
        return function ($column, $query, $search) {
            if (!$search) return;
            $with_childs = Catalog::find($search)->with_childs()->pluck('id');
            $carts = Cart::whereIn('catalog_id', $with_childs)->whereNotNull('order_call_id')->pluck('order_call_id');
            return $query->whereIn('id', $carts);

        };
    }

    public function cType()
    {
        return function ($model) {
            if ($model->call_type) {
                return ($model->call_type->icon != '') ? "<i class=\"{$model->call_type->icon}\" aria-hidden=\"true\" title='{$model->call_type->name}'></i>" : '-';
            }
            return '-';
        };
    }

    public function fType()
    {
        return function ($column, $query, $search) {
            if (!$search) return;
            return $query->where('type', $search);
        };
    }

    public function cSumm()
    {
        return function ($model) {
            $cart = $model->cart;
            $paid_amount = $cart->map(function ($value) {
                return $value->quantity * $value->price;
            })->sum();
            $robokassa = '';

            $add_fields = is_array($model->add_fields) ? $model->add_fields : json_decode($model->add_fields, true);
            if ($model->yafirst && ($model->yafirst->status != 'CAPTURED' || !optional(optional($add_fields)['order'])['paid_amount'])) {
                try {
                    $yapay = getYapayStatus($model->id);
                } catch (\Exception $exception) {}
            }

            if ($model->glue->count()) {
                $children = $model->glue; //OrderCall::select('add_fields')->where('parent_id', $model->id)->get();
                $add_fields = is_array($model->add_fields) ? $model->add_fields : json_decode($model->add_fields, true);
                if (isset($add_fields['order'])) {
                    $add_order = $add_fields['order'];
                    if ($children->count() && optional($add_order)['order_type']) {
                        $color = optional($add_order)['paid_amount'] > 0 ? 'success' : 'danger';
                        if (in_array($add_order['order_type'], [1, 5, 9, 10])) {
                            $robokassa = optional($add_order)['pay_type']==2 ? '<span class="badge bg-'.$color.' text-nowrap">СБЕР</span><br>' : '<span class="badge bg-'.$color.' text-nowrap">РОБОКАССА</span><br>';
                        } elseif (in_array($add_order['order_type'], [15, 16])) {
                            $pay_type = $add_fields['order']['order_type'] ?? 15;
                            $pay_type = $pay_type == 15 ? 'ЯПЭЙ' : 'СПЛИТ';
                            $robokassa = '<span class="badge bg-'.$color.' text-nowrap">'.$pay_type.'</span><br>';
                        } elseif (in_array($add_order['order_type'], [17])) {
                            $robokassa = '<span class="badge bg-'.$color.' text-nowrap">ЮКАССА</span><br>';
                        }
                    }
                }
                return $robokassa . number_format($paid_amount, 0, ',', ' ') . "&nbsp;₽<br>";// . $this->cScores($model);
            } else {
                $cart = $model->cart;
                $paid_amount = $cart->map(function ($value) {
                    return $value->quantity * $value->price;
                })->sum();
                $add_fields = is_array($model->add_fields) ? $model->add_fields : json_decode($model->add_fields, true);
                $robokassa = '';
                if (($model->type == 'cart' || $model->type == 'stock_good') && $add_fields && isset($add_fields['order'])) {
                    $add_order = $add_fields['order'];

                    $order_type_label = (!empty($add_order['paid_amount']) && $add_order['paid_amount'] > 0 ||
                        ($model->robofirst && optional($model->robofirst)->status == 100) ||
                        ($model->yafirst && optional($model->yafirst)->status == 'CAPTURED') ||
                        ($model->yookassafirst && optional($model->yookassafirst)->status == 'succeeded') ||
                        ($model->sberfirst && optional($model->sberfirst)->status == 2)) ? 'success' : 'danger';
                    $robo_perc = false;
                    $bank = $model->robofirst ? 'Робокасса' : 'Сбер';
                    $pay_type = $add_fields['order']['order_type'] ?? 15;
                    switch ($pay_type) {
                        case 15:
                        case 16:
                            $pay_type = $pay_type == 15 ? 'ЯПЭЙ' : 'СПЛИТ';
                            break;
                        case 17:
                            $pay_type = 'ЮКАССА';
                            break;
                    }
                    $bank = $model->yafirst ? $pay_type : $bank;
                    $bank = $model->yookassafirst ? $pay_type : $bank;
                    switch (optional($add_order)['order_type']) {
                        case -1:
                            $order_type_label = 'primary';
                            $robo_perc = 'Первый шаг';
                            if ($model->pay_type == 0) $robo_perc .= ' РАССРОЧКА';
                            break;
                        case 5:
                            $order_type_label = 'danger';
//                            $robo_perc = 'РАССРОЧКА';
                            $robo_perc = 'Сбер рассрочка';
                            break;
                        case 1: $robo_perc = "$bank 40%";break;
                        case 2: $robo_perc = "$bank 100%";break;
                        case 9: $robo_perc = "$bank 20%";break;
                        case 10: $robo_perc = "$bank 50%";break;
                        case 15: $robo_perc = "ЯПЭЙ";break;
                        case 16: $robo_perc = "СПЛИТ";break;
                        case 17: $robo_perc = "ЮКАССА";break;
                    }
                    $robokassa = $robo_perc ? '<span class="badge bg-' . $order_type_label . ' text-nowrap">' . $robo_perc . '</span><br>' : '';
                    $robokassa .= '<div class="text-muted small">'.RoboPayment::STATE_CODES[optional($model->robofirst)->status].'</div>';
                } elseif ($model->robofirst && !empty($paid_amount)) {
                    $order_type_label = ($model->robofirst && optional($model->robofirst)->status == 100) ? 'success' : 'danger';
                    $perc = round(100 * $model->robofirst['out_sum'] / $paid_amount);
                    $robokassa = '<span class="badge bg-'.$order_type_label.' text-nowrap">РОБОКАССА ' . $perc . '%</span><br>';
                    $robokassa .= '<div class="text-muted small">'.RoboPayment::STATE_CODES[optional($model->robofirst)->status].'</div>';
                } elseif ($model->sberfirst && !empty($paid_amount)) {
                    $order_type_label = optional($model->sberfirst)->status == 2 ? 'success' : 'danger';
                    $perc = round(100 * $model->sberfirst['out_sum'] / $paid_amount);
                    $robokassa = '<span class="badge bg-'.$order_type_label.' text-nowrap">Сбер ' . $perc . '%</span><br>';
                    $robokassa .= '<div class="text-muted small">'.SberPayment::STATE_CODES[optional($model->sberfirst)->status].'</div>';
                } elseif ($model->yafirst && !empty($paid_amount)) {
                    $order_type_label = optional($model->yafirst)->status == 'CAPTURED' ? 'success' : 'danger';
                    $pay_type = $add_fields['order']['order_type'] ?? 15;
                    $pay_type = $pay_type == 15 ? 'ЯПЭЙ' : 'СПЛИТ';
                    $robokassa = '<span class="badge bg-'.$order_type_label.' text-nowrap">'.$pay_type.'</span><br>';
                    $robokassa .= '<div class="text-muted small">'.optional($model->yafirst)->status.'</div>';
                } elseif ($model->yookassafirst && !empty($paid_amount)) {
                    $order_type_label = optional($model->yookassafirst)->status == 'succeeded' ? 'success' : 'danger';
                    $robokassa = '<span class="badge bg-'.$order_type_label.' text-nowrap">ЮКАССА</span><br>';
                    $robokassa .= '<div class="text-muted small">'.optional($model->yookassafirst)->status.'</div>';
                }

                return $robokassa . number_format($paid_amount, 0, ',', "&nbsp;") . "&nbsp;₽<br>";// . $this->cScores($model);
            }
        };
    }

    public function fSumm()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;
            $carts = Cart::where('brand_id', $search)->whereNotNull('order_call_id')->pluck('order_call_id')->all();
            return $query->whereIn('id', $carts);
        };

        /*return function ($column, $query, $search) {
            if (is_null($search)) return;
            if ($search == 0) $search = null;
            $query->whereIn('add_fields->order->order_type', [1, 2])
                ->when($search, function ($q) {
                    $robo = RoboPayment::whereColumn('lead_id', 'inv_id')->pluck('lead_id')->toArray();
                    $q->where('add_fields->order->paid_amount', '>', 0)->orWhereIn('id', $robo);
                }, function ($q) {
                    $q->where('add_fields->order->paid_amount', 0);
                });

        };*/
    }

    public function cScores($model)
    {
//        return function ($model) {
        if (!$cart = $model->cart) return '';
        $debug = [];
        $scores = $cart->filter(function ($v) {
            if ($v->is_module) return false;
            if (!$v->catalog) return false;
            if (!$v->catalog->catalog_cities) return false;
            return true;
        })->sum(function ($v) use ($model, &$debug) {
            if (!$item = $v->goods) return 0;
            if (!$item->price->count()) return 0;
            $purchase = ($v->is_module) ? $item->price : $item->price->first()->purchase;
            $city = $model->geo_cities;
            $add = 0;
            if ($city && $v->catalog->catalog_cities && !$v->is_module) {
                $cc = $v->catalog->catalog_cities->where('city_id', $city->id)->where('active', 1)->first();
                if ($cc) {
                    $add = $cc->add_price;
                    $gc = $item->goods_cities->where('city_id', $city->id)->first();
                    if ($gc && $gc->add_price > 0) {
                        $add = $gc->add_price;
                    }
                }
            }
//	                $debug[] = "($v->quantity * ($v->price - ($add + $purchase)) / 70) = ".($v->quantity * ($v->price - ($add + $purchase)) / 70);
            return round($v->quantity * ($v->price - ($add + $purchase)) / 70);
        });
//            debugbar()->log("$model->id ($scores): ", $debug);
        return "<span class='text-muted'>{$scores}&nbsp;Б</span>";
//        };
    }

    public function curScores()
    {
        $leads = OrderCall::with(['cart.catalog.catalog_cities', 'cart.items.price', 'geo_cities'])->where('status', 'TRANSFER')
            ->where('user_id', auth()->id())->whereMonth('created_at', now()->month)->whereYear('created_at', now()->year)->get();
        $scores = $leads->sum(function ($model) {
            if (!$cart = $model->cart) return '';
            return $cart->sum(function ($v) use ($model) {
                if (!$v->catalog) return 0;
                if (!$item = $v->items) return 0;
                if (!$item->price->count()) return 0;
                $purchase = ($v->is_module) ? $item->price : $item->price->first()->purchase;
                $city = $model->geo_cities;
                $add = 0;
                if ($city && $v->catalog->catalog_cities && !$model->is_module) {
                    $cc = $v->catalog->catalog_cities->where('city_id', $city->id)->first();
                    $add = $cc ? $cc->add_price : 0;
                }
                return round($v->quantity * ($v->price - ($add + $purchase)) / 70);
            });
        });

        return $scores;
    }

    public function fScores()
    {
        return function ($column, $query, $search) {
            return $query;
        };
    }

    public function fReason()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;
            $query->where('reject_reason', $search);
        };
    }

    public function fComment()
    {
        return function ($column, $query, $search) {
            return $query;
        };
    }

    public function cStatus()
    {
        return function ($model) {
            $comment = htmlentities(optional($model->order_status)->comment);

            if ($model->status == 'TRANSFER') {
                $order = $model->lead;
                return !empty($order) ? '<a target="_blank" href="/order_news/' . $order->id . '/edit" tabindex="0" data-toggle="popover" data-trigger="hover focus" data-content="' . $comment . '">' . $model->order_status->name . '</a>' : '[ЗАКАЗ УДАЛЕН]';
            }
            if ($model->status == 'REJECT' && !is_null($model->reject_reason)) {
                $reason = $model->reason->title;
                return '<a tabindex="0" data-toggle="popover" data-trigger="hover focus" data-content="' . $reason . '">' . $model->order_status->name . '</a>';
            }
            $new = $model->status == 'NEW' ? '<small class="align-text-bottom mr-2"><i class="fa fa-circle fa-xs text-secondary"></i></small>' : '';
            return ($model->order_status) ? '<a tabindex="0" role="button" data-html="true" data-toggle="popover" data-trigger="hover focus" data-content="' . $comment . '">' . $new . $model->order_status->name . '</a>' : '-';
        };
    }

    public function fStatus()
    {
        return function ($column, $query, $search) {
            if (!empty($search)) $query->whereHas('order_status', function ($q) use ($search) {
                $q->where('slug', $search);
            });
            return $query;
        };
    }

    public function cCity()
    {
        return function ($model) {
            if ($model->city == '') return '-';
//            $city = \App\Models\Geo\City::whereName($model->city)->first();
            $city = $model->geo_cities;

            $sup = $model->cart->flatMap(function ($v) {
                $catalog = $v->catalog;
                if (!$catalog) return;
                $suppliers = $catalog->suppliers;
                return $suppliers;
            })->unique('id');
            $is_city = $sup->filter(function ($v) use ($model) {
                $city = optional($v->supplier_city)->name;
                return $city == $model->city;
            })->map(function ($v) {
                $name = $v->title ?: $v->name;
                return "<a href='/suppliers/{$v->id}/edit' target='_blank'>{$name}</a>";
//                return "<a class='badge bg-success' href='/suppliers/{$v->id}/edit' target='_blank'>{$name}</a>";
            });
            $label = $is_city->isNotEmpty() ? 'danger' : 'primary';
            $suppliers = $is_city->isNotEmpty() ? '<hr style="margin:5px"><b>Поставщики:</b><br>' . $is_city->implode('<br>') : '';

            $region_btn = $this->cRegion($model, $sup);
            if ($city) {
                $region = $city->region;
            } elseif ($model->old_cities) {
                $region = $model->old_cities->region;
            }
            $region_txt = !empty($region) ? $region->name . ', ' : '';

            if (!$city) return '<a target="_blank" href="http://maps.yandex.ru/?l=map&text=' . urlencode($region_txt . $model->city) . '">' . $model->city . '</a>' . $region_btn;
//            if (!$city) return '<a target="_blank" href="http://maps.yandex.ru/?l=map&text='. urlencode($region_txt . $model->city) .'" class="badge bg-'.$label.'" style="width:100%;font-size:.65em;">' . $model->city . '</a>' . $region_btn;
            $info_tdp = $city->info_tdp ? "<span class='badge bg-success'>КИТ</span>" : "<span class='badge bg-warning'>Почта</span>";
            $info_population = $city->info_population ? "<br>Население: {$city->info_population}" : '';
            $add_text = ' транспортная компания';
            $info = "ПСВ: {$info_tdp}<br>{$info_population}{$suppliers}";

            $city = $model->city;
            if (!empty($region) && !is_null($region->conversion) && config('franchise.key') == 'crm') {
                $key = 'AB';
                if (!\Cache::has("report.catalog_by_class.{$key}")) {
                    $cats = \Cache::remember("report.catalog_by_class.{$key}", now()->addDay(), function () use ($key){
                        $c = ['A','B'];
                        return Catalog::select('id', 'class')->with('children:id,parent_id')->whereIn('class', $c)->get()
                            ->map(function ($v) {
                                return array_merge([$v->id], $v->children->pluck('id')->toArray());
                            })->flatten()->unique()->toArray();
                    });
                } else {
                    $cats = \Cache::get("report.catalog_by_class.{$key}");
                }
                if (in_array($model->catalog_id, $cats)) {
                    $city .= '&nbsp;<i class="fa fa-circle fa-xs text-'.Region::LABELS[$region->conversion].'"></i>';
                    $city = '<span class="text-nowrap">'.$city.'</span>';
                }
            }

            return '<a target="_blank" href="http://maps.yandex.ru/?l=map&text=' . urlencode($region_txt . $model->city . $add_text) . '" tabindex="0" data-html="true" data-placement="left" data-toggle="popover" data-trigger="hover focus" data-content="' . htmlentities($info) . '">' . $city . '</a>' . $region_btn;
//            return '<a target="_blank" href="http://maps.yandex.ru/?l=map&text='.urlencode($region_txt . $model->city . $add_text).'" tabindex="0" data-html="true" data-placement="left" data-toggle="popover" class="badge bg-'.$label.'" style="width:100%;font-size:.65em;" data-content="'.htmlentities($info). '">' . $model->city . '</a>' . $region_btn;
        };
    }

    public function fCity()
    {
        return function ($column, $query, $search) {
            if (!$search) return;
            $city = \App\Models\Geo\City::find($search);
            $query->whereCity(optional($city)->name);
        };
    }

    public function fRegion()
    {
        return function ($column, $query, $search) {
            if (!$search) return;
            $cities = \App\Models\Geo\City::where('region_id', $search)->pluck('name')->all();
            $query->whereIn('city', $cities);
//            $regs = \App\Models\Geo\Region::where('name', 'like', "%$search%")->pluck('id')->toArray();
//            $cities = \App\Models\Geo\City::whereIn('region_id', $regs)->pluck('name')->toArray();
//            $query->where(function($q) use ($search, $cities) {
//                return $q->where('city', 'like', "%$search%")->orWhereIn('city', $cities);
//            });
//            $query->where('city', 'like', "%$search%")->orWhereNull('city');
        };
    }

    public function cRegion($model, $sup)
    {
//        return function ($model) {
        if ($model->city == '') return '-';
        $list = false;
//            $sup = $model->cart->flatMap(function ($v) use (&$list){
//                if ($brand = $v->brand) {
//                    if ($brand->list) $list = true;
//                    return $brand->suppliers;
//                } else {
//                    if (!$v->items) return;
//                    $brand = $v->is_module ? $v->items->brand()->first() : $v->items->brand;
//                    if ($brand->list) $list = true;
//                    return $brand->suppliers;
//                }
//            })->unique('id');

        $region = $cities = false;
        if ($city = $model->geo_cities) {
            $region = $city->region;
        } elseif ($city = $model->old_cities) {
            $region = $city->region;
        }
        $cities = optional($region)->cities;

        if ($cities) {
            $cities = $cities->pluck('name')->all();
            $sup_in_region = $sup->filter(function ($v) use ($model, $cities) {
                $city = optional($v->supplier_city)->name;
                return in_array($city, $cities);
            });
            if ($sup_in_region->isNotEmpty()) {
                unset($sup);
                $sup = $sup_in_region->values();
            }
        }

        $sup = $sup->map(function ($v) use ($model) {
            $name = $v->title ?: $v->name;
            $city = optional($v->supplier_city)->name;
            $label = $model->city == $city ? 'success' : 'danger';
            return "<a class='badge bg-{$label}' href='/suppliers/{$v->id}/edit' target='_blank'>{$name} ({$city})</a>";
        });

        if (isset($sup_in_region) && $sup_in_region->isNotEmpty()) $label = 'danger';
        elseif ($sup->isNotEmpty()) $label = ($list) ? 'warning' : 'primary';
        else $label = 'primary';

        $suppliers = $sup->isNotEmpty() ? '<b>Поставщики:</b><br>' . $sup->implode('<br>') : '';

        return $region ? '<div tabindex="0" class="text-muted" data-html="true" data-toggle="popover" data-trigger="hover focus" data-placement="left" data-content="' . htmlentities($suppliers) . '">' . $region->name . '</div>' : '-';
//            return $region ? '<div tabindex="0" style="width:100%;font-size:.7em;cursor:pointer" class="text-'.$label.'" data-html="true" data-toggle="popover" data-placement="left" data-content="'.htmlentities($suppliers). '">' . $region->name . '</div>' : '-';
//        };
    }

    public function cClass()
    {
        return function ($model) {
            $content = '';
            if ($model->checks && !empty($model->checks['class_reason'])) {
                $reason = $model->checks['class_reason'];
                $content = 'data-toggle="popover" data-trigger="hover focus" data-placement="left" data-content="' . htmlentities($reason) . '"';
            }
            return !isset($model->class) ? '-' :
                (($model->class == '1') ?
                    "<i class=\"fa fa-thumbs-up text-success\" aria-hidden=\"true\" {$content}></i>" :
                    "<i class=\"fa fa-thumbs-down text-danger\" aria-hidden=\"true\" {$content}></i>");
        };
    }

    public function fSeason()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;
            $catalogs = Catalog::where('season', 'like', "%\"{$search}\"%")->pluck('id');
            $carts = Cart::whereIn('catalog_id', $catalogs)->whereNotNull('order_call_id')->pluck('order_call_id');
            return $query->whereIn('id', $carts);
        };
    }

    public function fDelivery()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;
            $cities = \App\Models\Geo\City::where('info_tdp', $search)->pluck('name')->toArray();
            return $query->whereIn('city', $cities);
        };
    }

    public function fSeo()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;
            return $query->where('seo_status', $search);
        };
    }

    public function fSex()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;
            return $query->where('sex', $search);
        };
    }

    public function fGoods()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;

            $goods_leads = Cart::query()->whereNull('is_module')->whereNotNull('order_call_id')
                ->where('item',\App\Models\Goods::class)->where('item_id', $search)->pluck('order_call_id')->unique()->all();
            if (!$goods_leads) return;
            return $query->whereIn('id', $goods_leads);
        };
    }

    public function fProbability()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;

            return $query->where('probability', $search);
        };
    }

    public function fClient()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;

            return $query->whereHas('client', fn($q) => $q->where('type', $search));
        };
    }

    public function fClientype()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;

            return $query->where('client_type', $search);
        };
    }

    public function fClassCategory()
    {
        return function ($column, $query, $search) {
            if (is_null($search)) return;
            if ($search === '0') {
//    	    	$cats = Catalog::select('id','class')->with('children:id,parent_id')->whereNotNull('class')->get()
//		            ->map(function ($v) { return array_merge([$v->id], $v->children->pluck('id')->toArray()); })->flatten()->unique()->toArray();
//	            $ids = Cart::whereIn('catalog_id',$cats)->pluck('order_call_id')->unique()->toArray();
                return $query->whereDoesntHave('cart');//->whereNotIn('id', $ids);
            }
            $cats = \Cache::remember('catalog_by_class.' . $search, now()->addDay(), function () use ($search) {
                $class = substr($search, 0, 1);
                $subclass = substr($search, 1, 1);
                return Catalog::select('id', 'class')->with('children:id,parent_id')->when(!empty($subclass), fn($q)=>$q->where('subclass',$subclass))->where('class', $class)->get()
                    ->map(function ($v) {
                        return array_merge([$v->id], $v->children->pluck('id')->toArray());
                    })->flatten()->unique()->toArray();
            });
            return $query->whereIn('catalog_id', $cats);

            $ids = Cart::query()->whereIn('catalog_id', $cats)->whereNotNull('order_call_id')->select('order_call_id')->distinct()->pluck('order_call_id')->all();
            $ids_goods = Cart::query()->whereNotNull('order_call_id')->whereNull('is_module')->select('order_call_id')->distinct()->whereHas('goods', function ($q) use ($cats) {
                return $q->whereIn('catalog_id', $cats);
            })->pluck('order_call_id')->all();
//            $ids = Cart::with('modules.catalog')->whereNotNull('order_call_id')->select('order_call_id')->distinct()->whereHas('modules.catalog', function ($q) use ($cats) { return $q->whereIn('id', $cats); })->pluck('order_call_id')->all();
            $ids = array_values(array_unique(array_merge($ids, $ids_goods)));

            return $query->whereIn('id', $ids);
        };
    }

    public function cCompetitors($competitors)
    {
        return function ($model) use ($competitors) {
            $cart = $model->cart;
            if (!$cart) return;
            $cat_ids = $model->cart->where('catalog_id', '<>', null)
                ->map(function ($v) {
                    if (!optional($v->catalog)->ancestors->count()) return $v->catalog_id;
                    $ids = $v->catalog->ancestors->pluck('id')->toArray();
                    $ids[] = $v->catalog_id;
                    return $ids;
                })->flatten()->toArray();
            $competitors = $competitors->filter(function ($v) use ($model, $cat_ids) {
                return ($v->city ? in_array($model->city, $v->city->region->cities->pluck('name')->toArray()) : true) &&
                    ($v->catalogs->count() ? $v->catalogs->whereIn('id', $cat_ids)->count() : true);
            });
            if (!$competitors->count()) return '';
            $content = $competitors->map(function ($v) {
                return "<a href='{$v->url}' target='_blank' class='badge bg-warning'>{$v->title}</a>";
            })->implode('<br>');

            return '<a tabindex="0" data-toggle="popover" data-trigger="hover focus" data-placement="left"
                class="text-danger" role="button" data-html="true" data-title="Конкуренты"
                data-content="' . htmlentities($content) . '"><i class="fa fa-users"></i></a>';
        };
    }

    public function cDivider()
    {
        return function ($model) {
            return '<div class="divider" style="display: none"></div>';
        };
    }

    public function cManager($user_options)
    {
        return function ($model) use ($user_options) {
//            dd(\AdminColumnEditable::select('user_id', 'Менедж.', $user_options));
            return
//                \AdminColumnEditable::select('user_id', 'Менедж.', $user_options)
//                ->setTitle('Выберите ответственного:')->setOrderCallback(function ($c, $q, $s) {$q->orderBy('user_id', $s);})->render() .
                $this->cComment($model);
//            \AdminColumnEditable::select('user_id', 'Менедж.', $user_options)
//                ->setTitle('Выберите ответственного:')
//                ->setOrderCallback(function ($c, $q, $s) {
//                    $q->orderBy('user_id', $s);
//                })
        };
    }

    //Счет на оплату
//    public function tabBill($model, $cart)
//    {
//        $bill = AdminForm::card()->addStyle('bill.css', '/css/bill.css', ['admin-default']);
//        $bill->addBody([
//            AdminFormElement::view('doc.order.bill')
//                ->setData([
//                    'invoice' => $model->id,
//                    'order' => $model,
//                    'total_sum' =>  $model->getAmountToPay(),
//                    'total_sum_str' => ucfirst(digit_text($model->getAmountToPay(), 'ru', true)),
//                    'date' => Date::parse($model->date)->format('d F Y'),
//                    'cart' => $cart
//                ])
//        ]);
//        return $bill;
//    }

    public function tabTasks($id, $order_id = null)
    {
        $tasks = AdminSection::getModel(Task::class)->fireDisplay(['scopes' => ['withLead', $id]]);
        $tasks->setParameter('ordercall_id', $id);
        if ($order_id) $tasks->setParameter('order_id', $id);
        return $tasks;
    }

    // Письмо клиенту
    public function tabEmail($model, $cart, $paid_amount)
    {
        $email = AdminForm::card();
        $email->addBody([
            AdminFormElement::columns()
                ->addColumn([
                    AdminFormElement::html('<a href="/send_invoice/' . $model->id . '" class="btn btn-warning float-right">Отправить письмо</a>')
//                    AdminFormElement::datetime('sendet_in', 'Дата отправки письма')
//                        ->setFormat(config('sleeping_owl.datetimeFormat'))
//                        ->setReadonly(true)
                ]),
//                ->addColumn([
//                    AdminFormElement::checkbox('resend', 'Переотправить')
//                        //  ->setValueSkipped(true)
//                        ->setHelpText('Для повторной отправки письма при статусе "Отправлен на оплату"')
//                ]),

//            AdminFormElement::wysiwyg('e-mail', 'e-mail')->setVisibilityCondition(function ($model){
//                return false;
//            }),
//            AdminFormElement::view('admin::leads.email')
//                ->setData([
//
//                ])
            AdminFormElement::view('doc.order.mail')
                ->setData([
                    'order' => $model,
                    'cart' => $cart,
                    'robolink' => getRobocassaLink($model->id, $paid_amount,'order'),
                    'user' => $this->getUser($model->user_id)
                ])
        ]);
        $email->getButtons()->setButtons([]);
        return $email;
    }

    // Письмо клиенту
    public function tabEmailTemplates($model, $cart, $paid_amount)
    {
        $tpls = \App\Models\EmailTemplate::query()->where('show',true)->orderBy('order')->get();
        $tpls_rd = \App\Models\RidersDrive\EmailTemplaterd::query()->where('show',true)->orderBy('order')->get();
        $email = AdminForm::form();
        $email->addElement(
            AdminFormElement::view('doc.order.templates', ['order' => $model])
        );
        $email->addElement(
            AdminFormElement::columns()
                ->addColumn([
                    AdminFormElement::wysiwyg('email_msg', '')->setHeight(500)
                ])
                ->addColumn([
                    AdminFormElement::html('<label class="control-label"></label>'),
                    AdminFormElement::view('rd.doc.order.templates-list', compact('tpls','tpls_rd'))
                ], 3)
        );
        $email->getButtons()->setButtons([]);
        return $email;
    }

    public function getUser($user_id)
    {
        if (null === $user_id) {
            return null;
        }
        $user = User::find($user_id);
        $role = $user->roles()->get()->first();
        //$role = $user->roles()->get()->find(9); TODO: учесть несколько ролей
        return collect([
            'id' => $user->id,
            'fio' => $user->name . ' ' . $user->lastName,
            'email' => $user->email,
            'role' => mb_strtolower($role->display_name)
        ]);
    }

    public function getClientCard($client)
    {
        if (!$client) return "<div class=\"alert alert-danger alert-dismissible\">
                <h4><i class=\"icon fa fa-ban\"></i> Клиент не найден</h4>
                Необходимо указать телефон.
              </div>";
        return AdminFormElement::html(view('admin::info.client', ['client' => $client]));
    }

    public function getClientCardPopup($client, $view = 'order_client')
    {
        if ($client) {
            return
                AdminFormElement::html(
                    view('admin::info.' . $view, ['client' => $client])
                );
        }
        return '<div class=\"alert alert-danger alert-dismissible\">
                <h4><i class=\"icon fa fa-ban\"></i> Клиент не найден</h4>
                Необходимо привязать клиента к заказу.
              </div>';
    }

    public function getOrder($lead)
    {
        return (!empty($lead)) ?
            AdminFormElement::html('<div class="form-group"><a class="badge bg-success" style="font-size: 1em;" href="/order_news/'
                . $lead->id . '/edit" target="_blank">Перейти в заказ '.renderComponent(new Helper('edit:order_calls.Перейти в заказ')).'</a></div>&nbsp;') : '';
    }

    public function getCounts()
    {
        $calc = session()->has('leads_stats_calc_sum');
        $counts = \Cache::remember('rd.getCounts:'. $calc.':'.request()->has('default'), now()->addMinutes(30), function () use ($calc) {
            $counts = OrderCall::with(['order_status'])
                ->when($calc, function ($q) {
                    return $q->with('cart');
                })
                ->when(request()->has('default'), function ($q) {
                    return $q->default();
                })
                ->whereNotIn('status', ['TRANSFER', 'REJECT', ''])
                ->whereNull('parent_id')
                ->whereNull('black_list')
                ->whereNull('deleted_at')->get()
                ->groupBy('status')->sortBy(function ($v, $k) {
                    return ($v->first()->order_status) ? ($v->first()->order_status->order) : 999;
                })->map(function ($v) {
                    if (!$v->first()->order_status) return;
                    $status = $v->first()->order_status->name ?: '?';
                    $color = $v->first()->order_status->color ?: 'gray';
                    $sum = !session()->has('leads_stats_calc_sum') ? '' : $v->map(function ($v) {
                        return $v->cart ? $v->cart->map(function ($v) {
                            return $v->price * $v->quantity;
                        })->sum() : 0;
                    })->sum();
                    if ($sum) $sum = "({$sum}р.)";
                    $onclick = "$('select[data-id=status]').val('{$v->first()->order_status->slug}').trigger('change').trigger('select2:select')";
                    return "<div role='button' class=\"list-group-item list-group-item-action\" onclick=\"{$onclick}\">{$status} <b class=\"float-right\">{$v->count()} {$sum}</b></div>";
                });
            return $counts;
        });

        return $counts->map(function ($v, $k) {
            $group = OrderCall::getStatusGroup($k);
            if (!$group) return false;
            return [
                'items' => $v,
                'title' => $group['title'],
                'class' => $group['class'],
                'order' => $group['order'],
            ];
        })->filter()->sortBy('order')->groupBy('title')->map(function ($v,$k) {
            return [
                'items' => $v->pluck('items')->implode(''),
                'class' => optional($v->first())['class'],
                'helper' => 'display:order_calls.' . $k
            ];
        })->all();
    }

    public function getTopSalesCounts($user_options)
    {
        $calc = session()->has('leads_stats_calc_sum');
        $counts = \Cache::remember('rd.getTopSalesCounts:'. $calc.':'.request()->has('default'), now()->addMinutes(30), function () use ($calc, $user_options) {
            $counts = OrderCall::with('order_status', 'user')
                ->when($calc, function ($q) {
                    return $q->with('cart');
                })
                ->when(request()->has('default'), function ($q) {
                    return $q->default();
                },
                    function ($q) {
                        return $q->excludeDefault();
                    })
                ->whereNotIn('status', ['NEW', 'TRANSFER', 'REJECT', ''])
                ->whereNotIn('type', ['subscrible', 'moneyonphone', 'сondition'])
                ->whereNull('deleted_at')
                ->whereDate('status_date', Carbon::today()->toDateString())
                ->whereIn('user_id', array_keys($user_options))
                ->get()
                ->sortBy('order_status.order')
                ->groupBy(function ($v) {
                    return "{$v->user->fi}|{$v->user->id}";
                })
                ->map(function ($value) use ($calc) {
                    if ($calc) {
                        $paid_amount = $value->map(function ($v) {
                            return $v->cart->map(function ($value) {
                                return $value->quantity * $value->price;
                            })->sum();
                        })->sum();
                        $paid_amount = number_format($paid_amount, 2, ',', ' ');
                        $paid_amount = "<hr style=\"margin: 5px 0\">Итого: <b class=\"float-right\">{$paid_amount} руб.</b>";
                    } else $paid_amount = '';

                    return $value->groupBy('status')->map(function ($v) use ($paid_amount, $calc) {
                            $status = $v->first()->order_status->name;
                            $color = $v->first()->order_status->color ?: 'gray';
                            $sum = !$calc ? '' : number_format($sum = $v->map(function ($v) {
                                return $v->cart->map(function ($value) {
                                    return $value->quantity * $value->price;
                                })->sum();
                            })->sum(), 2, ',', ' ');
                            if ($sum) $sum = "{$sum} р.";
                            return "<div class=\"badge text-white\" style=\"width:100%;background-color:{$color}\"><span class=\"float-left\">{$status}:{$v->count()}</span>&nbsp;&nbsp;<span class=\"float-right\">{$sum}</span></div>";
                        })->implode('<br>') . $paid_amount;
                })->map(function ($v, $k) {
                    $stat = explode('|', $k);
                    $content = htmlentities($v);
                    $date_from = Carbon::yesterday()->format("d.m.Y");
                    $date_to = Carbon::today()->format("d.m.Y"); // val('{$date_from}::{$date_to}')
                    return "<a tabindex='0' onclick=\"$('select[data-id=user]').val('{$stat[1]}').trigger('change');$('input[data-id=sdate]').data('daterangepicker').setStartDate('{$date_from}');$('input[data-id=sdate]').data('daterangepicker').setEndDate('{$date_to}');$(this).blur();$('.applyBtn').click();\"
data-html='true' data-toggle='popover' data-trigger='hover' data-placement='top' data-content='{$content}' class='btn btn-default btn-block mb-2'>{$stat[0]}</a>";
                })->implode(' ');

            return $counts;
        });

        return $counts;
    }

    public function getSalesCounts()
    {
        $calc = session()->has('leads_stats_calc_sum');
        $counts = \Cache::remember('rd.getSalesCounts:'. $calc.':'.request()->has('default'), now()->addMinutes(30), function () use ($calc) {
            $counts = OrderCall::with('order_status', 'user')
                ->when($calc, function ($q) {
                    return $q->with('cart');
                })
                ->when(request()->has('default'), function ($q) {
                    return $q->default();
                },
                    function ($q) {
                        return $q->excludeDefault();
                    })
                ->whereIn('status', ['NEW', 'INFO3', 'INFO4', 'INFO1', 'INFO2', 'INFO5', 'SENDPAY',
                    'NOANSWER', 'SELECTGOODS', 'CALC', 'SENDINSPECT', 'CONTACT2', 'CONTACT3'])
                ->whereNotIn('type', ['subscrible', 'moneyonphone', 'сondition'])
                ->whereNull('deleted_at')
                ->whereDate('status_date', Carbon::today()->toDateString())
                ->where('user_id', auth()->id())->get()
                ->sortBy('order_status.order')->values()
                ->groupBy(function ($v) {
                    $status = ['NEW', 'INFO3', 'INFO4', 'INFO1', 'INFO2', 'INFO5', 'SENDPAY'];
                    $status2 = ['NOANSWER', 'SELECTGOODS', 'CALC', 'SENDINSPECT', 'CONTACT2', 'CONTACT3'];
                    return in_array($v->status, $status2) ? 'НЕ МЕНЕЕ ВАЖНЫЕ' :
                        (in_array($v->status, $status) ? 'ВАЖНЫЕ' : null);
                })
                ->map(function ($value) use ($calc) {
                    if ($calc) {
                        $paid_amount = $value->map(function ($v) {
                            return $v->cart->map(function ($value) {
                                return $value->quantity * $value->price;
                            })->sum();
                        })->sum();
                        $paid_amount = number_format($paid_amount, 2, ',', ' ');
                        $paid_amount = "<hr style=\"margin: 5px 0\">Итого: <b class=\"float-right\">{$paid_amount} руб.</b>";
                    } else $paid_amount = '';

                    return $value->groupBy('status')->map(function ($v) use ($paid_amount, $calc) {
                            $status = $v->first()->order_status->name;
                            $color = $v->first()->order_status->color ?: 'gray';
                            $sum = !$calc ? '' : number_format($v->map(function ($v) {
                                return $v->cart->map(function ($value) {
                                    return $value->quantity * $value->price;
                                })->sum();
                            })->sum(), 2, ',', ' ');
                            if ($sum) $sum = "{$sum} р.";
                            return "<div class=\"badge text-white\" style=\"width:100%;background-color:{$color}\"><span class=\"float-left\">{$status}:{$v->count()}</span>&nbsp;&nbsp;<span class=\"float-right\">{$sum}</span></div>";
                        })->implode('<br>') . $paid_amount;
                })->map(function ($v, $k) {
                    $content = htmlentities($v);
                    $key = $k == 'ВАЖНЫЕ' ? 1 : 2;
                    $label = request()->get('sales') == $key ? 'success' : 'default';
                    $url = !request()->has('sales') ? "?sales={$key}" : '';
                    if (request()->has('default')) $url .= $url ? '&default=1' : '?default=1';
                    return "<a tabindex='0' href='/order_calls{$url}' data-html='true' data-toggle='popover' data-trigger='hover' data-placement='top' data-content='{$content}' class='btn btn-{$label}  btn-block mb-2'>{$k}</a>";
                })->implode(' ');
            return $counts;
        });

        return $counts;
    }

    public function tabDocs($lead_id, $client_type = 0)
    {
        $errors = null;

        // TODO: добавить валидацию

//        $validator = Validator::make($data, [
//            'email' => 'required|email'
//        ],[
//            'email' => 'e-mail (в карточе клиента)'
//        ]);
//        if ($validator->fails()) {
//            $errors =  $validator->messages()->all();
//        }

        $panel = AdminForm::card();
        $panel->addBody(
            [
                AdminFormElement::view('admin::rd.leads.doc', [
                    'lead_id' => $lead_id,
                    'client_type' => $client_type,
                    'errors' => $errors
                ])
            ]
        );
        $panel->getButtons()->setButtons([]);
        return $panel;
    }

    public function tabDuty($id, $cart_ids)
    {
        $form = AdminForm::card();
        $measure = AdminSection::getModel(Service::class)->fireDisplay(['scopes' => ['withLead', $cart_ids]]);
        $measure->setParameter('order_call_id', $id);
        $form->addBody([$measure,
//            AdminFormElement::view("admin::comment", ['id' => $id, 'model_type' => Order::class, 'type' => 4]),
        ]);
        $form->getButtons()->setButtons ([]);
        return $form;
    }

    public function tabMoney($model)
    {
        $leadId = $model->id;
        $orderId = optional($model->lead)->id;

        $form = AdminForm::card();
        $tasks = AdminSection::getModel(Money::class)->fireDisplay(['scopes' => ['withLead', $leadId, $orderId]]);
        $tasks->setParameter('lead_id', $leadId);
        $tasks->setParameter('order_id', $orderId);
        $form->addBody([$tasks]);
        $form->getButtons()->setButtons([]);
        //$tasks->getColumns()->disableControls();
        return $form;
    }

    public function getCartLink($id, $model)
    {
        $domen = 'www';
        if (!empty($model)) {
            $city = $model->geo_cities; //\App\Models\Geo\City::where('name', $model->city)->first();
            if ($city) {
                $domen = $city->subdomain;
            }
        }
        return 'https://' . $domen . '.ridersdrive.ru/cart/order/' . base64_encode(json_encode(['id' => $id]));
    }

    public function getRobocassaLink($id, $sum, $type, $cart = false)
    {
        if ($sum < 1) return '';

        $payment = new \App\Modules\Robokassa\Payment(
            config('services.robokassa.login'), config('services.robokassa.paymentPassword'),
            config('services.robokassa.validationPassword'), false
        );

        setlocale(LC_ALL, null);
        $payment
            ->setInvoiceId($id)
            ->setCurrencyLabel('BankCard');
        $payment->addCustomParameters(['lead_id' => $id]);

        $desc = 'ridersdrive.ru, preorder# ' . $id;
        switch ($type) {
            case 1:
                $sum *= .4;
                break;
            case 9:
                $sum *= .2;
                break;
            case 10:
                $sum *= .5;
                break;
            case 12: $sum *= .7; break;
            default:
                //$payment->setSum($sum - ($sum * 0.03))
                $desc = 'ridersdrive.ru, order# ' . $id;
        }
//        if ($sum >= config('alfamart.robokassa.sum')) $sum += config('alfamart.robokassa.tax') * $sum;
        $payment->setSum($sum)->setDescription($desc);

        if ($cart->count()) {
            $payment->setReceipt($cart, $type);
        }

        return $payment->getPaymentUrl();
    }

    public function tGeneral($id, $model, $cart = null, $paid_amount = null, $items = null)
    {
        $type = OrderType::whereNotNull('alias')->orderBy('order')->get()->pluck('name', 'alias')->toArray();
        $status = new OrderStatus();
        $status->setKeyName('slug');
        $cities = OldGeocity::get()->pluck('name')->merge(\App\Models\Geo\City::get()->pluck('name'))->unique()->sort()->values()->all();

        $form = AdminForm::card();
        $form->addBody($this->add_fields($model));
        $cart_check_brand = $model ? $model->cart->map(function ($v) {
            return (int)!is_null($v->brand_id);
        })->max() : null;
        $cart_check_count = $model ? $model->cart->count() : 1;

        $link = new ShortLinks();
        $ext = auth()->user()->ext;
        $line_number = 'rd';
        /*if ($id && $city = $model->geo_cities) {
            $city_phone = config('call_number.'.$city->region_id);
//            $city_phone = preg_replace(["/^(8)/", "/[^0-9]/"], ["7", ""], $city->phone);
            if (!empty($city_phone) && strlen($model->phone) == 11) $line_number = $city_phone;
        }*/

        $call1 = ($ext && !empty($model->phone) && strlen($model->phone) == 11) ? "<div class=\"btn btn-link\" onclick=\"mangocall('{$ext}','{$model->phone}', '{$line_number}')\"><i class=\"fa fa-phone\"></i> позвонить</div>" : '';
        $call2 = ($ext && !empty($model->phone2) && strlen($model->phone2) == 11) ? "<div class=\"btn btn-link\" onclick=\"mangocall('{$ext}','{$model->phone2}','{$line_number}')\"><i class=\"fa fa-phone\"></i> позвонить</div>" : '';
        $call3 = ($ext && !empty($model->phone3) && strlen($model->phone3) == 11) ? "<div class=\"btn btn-link\" onclick=\"mangocall('{$ext}','{$model->phone3}','{$line_number}')\"><i class=\"fa fa-phone\"></i> позвонить</div>" : '';
        $call4 = ($ext && !empty($model->phone4) && strlen($model->phone4) == 11) ? "<div class=\"btn btn-link\" onclick=\"mangocall('{$ext}','{$model->phone4}','{$line_number}')\"><i class=\"fa fa-phone\"></i> позвонить</div>" : '';
        $probability =
            [
                0 => "0% - Отказ от сделки",
                30 => "30% - Слабая вероятность",
                50 => "60% - Средняя вероятность",
                70 => "90% - Высокая вероятность",
//                100 => "100% - Покупает 100%"
            ];

        $check_lists = CheckType::query()->where('type', 2)->orderBy('order')->get()->all();
        $checks = [];
        $class_reasons = [
            'Клиент не знает что мы интернет магазин и думает что товар есть в наличии в его городе здесь и сейчас',
            'Доставка не вложена в товар, хотя должна быть вложена',
            'Клиент не понимает что мы работаем через банки и думает что рассрочка от магазина',
            'До клиента не удалось дозвониться/дописаться в течении 2-х дней',
            'На сайте не было цены товара (и клиент не ожидал что будет такая цена)',
            'Не оставлял заявку',
            'Нет такого товара',
        ];
        if ($model && optional($model->checks)['class_reason']) {
            if (!in_array($model->checks['class_reason'], $class_reasons)) {
                $class_reasons[] = $model->checks['class_reason'];
            }
        }
        $checks[] = $this->customRules(AdminFormElement::select('checks->class_reason', 'Причина оценки качества лида '.renderComponent(new Helper('edit:order_calls.Причина оценки')), array_combine($class_reasons,$class_reasons)), $model);
        $checks[] = $this->customRules($id ? AdminFormElement::select('probability', 'Вероятность, (%) '.renderComponent(new Helper('edit:order_calls.Вероятность')))
            ->addValidationRule('required_unless:status,NEW,REJECT,NOANSWER', 'Поле Вероятность обязательно для заполнения при текущем статусе')
            ->setSortable(false)
            ->setOptions($probability)
            : AdminFormElement::select('probability', 'Вероятность, (%)')
                ->setOptions($probability),$model);

        if (count($check_lists)) {
//            $checks[] = AdminFormElement::html('<div class="card"><h5 class="card-header">Чеклист обработки лида</h5><div class="card-body">');
            foreach ($check_lists as $n => $check) {
                if ($check->input_type == 1) {
                    $el = $this->customRules(AdminFormElement::select('checks->' . $check->id, $check->subject, $check->options) ,$model);
                } else {
                    $el = $this->customRules(AdminFormElement::text('checks->' . $check->id, $check->subject) ,$model);
                }
                if ($check->about) $el->setLabel($el->getLabel().' '.view('components.helper',['desc'=>$check->about]));
                $checks[] = $el;
            }
//            $checks[] = AdminFormElement::html('</div></div>');
        }

        $cart_q = '';
        if ($model && $model->type == 'cart') {
            $cart_type = optional($model->transfer_fields)['cart_type'];
            if (is_null($cart_type)) {
                $add_fields = is_array($model->add_fields) ? $model->add_fields : json_decode($model->add_fields, true);
                $add_order = optional($add_fields)['order'];
                $order_type = (!empty($add_order['paid_amount']) && optional($add_order)['paid_amount'] > 0 || $model->robofirst) ? 2 : 1;
                $cart_type = optional($add_order)['order_type'] == -1 ? 0 : $order_type;
            }
            $cart_q = AdminFormElement::columns()->addColumn([
                AdminFormElement::html('<div class="card card-danger"><div class="card-header"><h3 class="mb-0"><b>Почему сделал покупку?<sup>*</sup></b> '.renderComponent(new Helper('edit:order_calls.Почему сделал покупку',false,'white')).'</h3></div><div class="card-body">'),
                AdminFormElement::textarea('transfer_fields->cart_q1', /*'- '.OrderCall::CART_Q1[$cart_type] . '<br>- ' .OrderCall::CART_Q2[$cart_type]*/)->addValidationRule('rd_lead_cart_q')->setRows(2),
//                AdminFormElement::textarea('transfer_fields->cart_q2', OrderCall::CART_Q2[$cart_type])->addValidationRule('lead_cart_q')->setRows(2),
                AdminFormElement::hidden('transfer_fields->cart_type')->setDefaultValue($cart_type),
                AdminFormElement::html('</div></div>')
            ],8);
        }

        $class_text = "<h4>Критерии оценки качества лида</h4>
<p>1. <b>Клиент понимает что мы интернет-магазин</b> и товар везется под заказ.</p>
<p>2. <b>Доставка вложена в товар, или клиент понимает ее стоимость</b></p>
<p>3. <b>Клиент понимает что мы работаем через банки.</b> И условия на сайте не противоречат тому что мы озвучиваем клиенту (Если это рассрочка или кредит)</p>
<p>4. <b>До клиента удалось дозвониться в течении дня обращения</b></p>";
        $class_help = '<a tabindex="0" data-html="true" data-toggle="popover" data-trigger="hover focus" style="margin-right:15px; font-size:1.2em;color:blue;word-break: normal;" data-content="' . htmlspecialchars($class_text) . '"><i class="fa fa-question-circle"></i></a>';

//        <x-helper name="sex" />
        $form->addBody([
            AdminFormElement::columns()
                ->addColumn([
                    AdminFormElement::columns()
                        ->addColumn([
                            ($model && $model->first_url) ? AdminFormElement::html(" <a class='btn btn-sm btn-outline-secondary text-nowrap mb-3' href='{$model->first_url}' target='_blank'>Самая первая ссылка входа </a>") : '',
                            ($model) ? AdminFormElement::html(" <a class='btn btn-sm btn-outline-secondary text-nowrap mb-3' href='{$model->url}' target='_blank'>Ссылка со страницы оформления заявки ".renderComponent(new Helper('edit:order_calls.ссылка'))."</a>") : '',
                            ($model && (auth()->user()->isAdmin() || auth()->user()->hasRole(['topsales','seniorsales','subdostavka']))) ?
                                AdminFormElement::select('type', 'Тип обращения '.renderComponent(new Helper('edit:order_calls.Тип обращения')), $type)->required() :
                                AdminFormElement::select('type', 'Тип обращения '.renderComponent(new Helper('edit:order_calls.Тип обращения')), $type)->setReadonly(true),
                                AdminFormElement::select('client_type', 'Тип клиента '.renderComponent(new Helper('edit:order_calls_rd.Тип клиента')), OrderCall::CLIENT_TYPES)
                                    ->addValidationRule('required_unless:status,NEW,NOANSWER', 'Поле Тип клиента обязательно для заполнения при текущем статусе'),
                        ])
//                        ->addColumn([
//                            ($model) ? AdminFormElement::html(" <a class='btn btn-outline-secondary btn-sm text-nowrap' href='{$model->url}' target='_blank'>Ссылка ".renderComponent(new Helper('edit:order_calls.ссылка'))."</a>") : '',
//                        ],2)
                        ->addColumn([
                            AdminFormElement::html('<div class="form-group form-element-select"><label for="type" class="control-label">Оценка качества лида: '.renderComponent(new Helper('lead_class')).'</label><div class="form-inline mb-3">'),
                            AdminFormElement::radio('class', '',
                                ['1' => '<i class="fa fa-thumbs-up" aria-hidden="true"></i>', '0' => '<i class="fa fa-thumbs-down" aria-hidden="true"></i>'])
                                ->setHtmlAttribute('style', 'display: none; margin-right:1em;')
                                ->addValidationRule('required_unless:status,NEW,NOANSWER', 'Поле Оценка качества обязательно для заполнения при текущем статусе')
//                                ->required()
                                ->setSortable(false),
                            AdminFormElement::html('</div></div>'),
                        ]),
                    AdminFormElement::columns()
                        ->addColumn([
                            $this->customRules(AdminFormElement::text('name', 'ФИО '.renderComponent(new Helper('edit:order_calls.ФИО'))),$model)
                        ],8)
                        ->addColumn([
                            $this->customRules(AdminFormElement::select('sex', 'Пол '.renderComponent(new Helper('edit:order_calls.Пол')))->setOptions([0=>'Ж',1=>'M']),$model)
                        ], 4),

                    AdminFormElement::columns()
                        ->addColumn([$this->customRules(AdminFormElement::text('phone', 'Телефон '.renderComponent(new Helper('edit:order_calls.Телефон')))->setHelpText($call1),$model)])
                        ->addColumn([$this->customRules(AdminFormElement::text('email', 'E-mail '.renderComponent(new Helper('edit:order_calls.E-mail')))->addValidationRule("rd_check_contact"),$model)])
                        ->addColumn([$model?->client ? AdminFormElement::select('client.type', 'Тип клиента', Client::TYPES)->setDefaultValue(0) : '']),
                    AdminFormElement::columns()
                        ->addColumn([$this->customRules(AdminFormElement::checkbox('only_phone', 'Нет других средств связи '.renderComponent(new Helper('edit:order_calls.Нет других средств связи')))->setDefaultValue(0),$model)], 8),
                    $id && $model->phone && Auth::user()->hasRole(['admin', 'superadmin']) ? AdminFormElement::html('<iframe src="/livewire/leadrd/' . $id . '?p=1" frameborder="0" width="100%" height="30"></iframe>') : '',
                    AdminFormElement::columns()
                        ->addColumn([$this->customRules(AdminFormElement::text('phone2', 'Телефон 2 '.renderComponent(new Helper('edit:order_calls.Телефон 2')))->setHelpText($call2),$model)])
                        ->addColumn([$this->customRules(AdminFormElement::text('phone3', 'Телефон 3 '.renderComponent(new Helper('edit:order_calls.Телефон 3')))->setHelpText($call3),$model)])
                        ->addColumn([$this->customRules(AdminFormElement::text('phone4', 'Телефон 4 '.renderComponent(new Helper('edit:order_calls.Телефон 4')))->setHelpText($call4),$model)]),
                    $id && $model->phone2 && Auth::user()->hasRole(['admin', 'superadmin']) ? AdminFormElement::html('<iframe src="/livewire/leadrd/' . $id . '?p=2" frameborder="0" width="100%" height="30"></iframe>') : '',
                    AdminFormElement::columns()
                        ->addColumn([$this->customRules(AdminFormElement::text('whatsapp', 'WhatsApp '.renderComponent(new Helper('edit:order_calls.WhatsApp')))->addValidationRule("rd_"),$model)])
                        ->addColumn([$this->customRules(AdminFormElement::text('viber', 'Viber '.renderComponent(new Helper('edit:order_calls.Viber')))->addValidationRule("rd_check_contact"),$model)])
                        ->addColumn([$this->customRules(AdminFormElement::text('telegram', 'Telegram '.renderComponent(new Helper('edit:order_calls.Telegram')))->addValidationRule("rd_check_contact"),$model)]),

                    $id ? AdminFormElement::columns()
                        ->addColumn([AdminFormElement::select('status', 'Статус '.renderComponent(new Helper('edit:order_calls.Статус')), $status)->setSortable(false)
                            ->addValidationRule("rd_lead_has_comment:$id,".$model->status)
                            ->addValidationRule("rd_lead_check_catalog:$id,".$model->status)
                            ->addValidationRule("rd_lead_check_selection:$id,".$model->status)
//                            ->addValidationRule("lead_has_tasks:$id,".$model->status)
                            ->setLoadOptionsQueryPreparer(function ($element, $query) {
                                return $query->where('show', true)->whereNotNull('slug')->orderBy('order');
                            })
//                            ->setHelpText(renderComponent(new Helper('lead_status', true)))
                            ->setForeignKey('slug')->setDisplay('name')->setDefaultValue('NEW')->required()])
                        ->addColumn([
                            $this->customRules(AdminFormElement::select('reject_reason', 'Причина отказа '.renderComponent(new Helper('lead_reason')), \App\Models\RejectReason::class)
                                ->setLoadOptionsQueryPreparer(fn($e,$q) => $q->where('show',1))
//                                ->addValidationRule('required_if:status,REJECT', 'Поле Причина отказа обязательно для заполнения при текущем статусе')
                                ->setSortable(false),$model)
                        ]) : AdminFormElement::hidden('status')->setDefaultValue('NEW'),
//                    $this->customRules(AdminFormElement::text('terms', 'Разъяснение')
////                        ->addValidationRule("required_if:reject_reason,0,1,4,5,6,7,9,11", 'Поле Разъяснение обязательно для заполнения при выбранной причине отказа')
//                        ,$model),
                    AdminFormElement::columns()
                        ->addColumn([$this->customRules(AdminFormElement::select('user_id', 'Ответственный '.renderComponent(new Helper('edit:order_calls.Ответственный')), new User)
                            ->setLoadOptionsQueryPreparer(function ($element, $query) {
                                $ids = config('franchise.users')->filter(fn($v) => $v->roles->whereIn('name', ['sales', 'remotesales', 'topsales', 'seniorsales','subdostavka'])->count())->pluck('id')->all();
                                return $query->whereIn('id', $ids);
                            })->setDisplay('fi')->setDefaultValue(Auth::id()),$model)])
                        ->addColumn([$this->customRules(AdminFormElement::select('pay_type', 'Способ оплаты '.renderComponent(new Helper('edit:order_calls.Способ оплаты')), OrderCall::PAY_TYPES)->setSortable(false)->addValidationRule("required_if:reject_reason,0,1,2,3,4,5,6,7,9,10,11", 'Поле Способ оплаты обязательно для заполнения при выбранной причине отказа'),$model),]),
                    AdminFormElement::hidden('cart_check')->setDefaultValue($cart_check_brand ? 1 : null)->addValidationRule("required_if:status,TRANSFER", 'Проверьте заполненность брендов в корзине')->setValueSkipped(true),
                    AdminFormElement::hidden('cart_check_count')->setDefaultValue($cart_check_count ? 1 : null)->addValidationRule("rd_cart_check_count")->setValueSkipped(true),
                    AdminFormElement::hidden('url')->setDefaultValue('#ridersdrive'),
                    AdminFormElement::columns()->addColumn($this->addModal($model)),

                    $this->customRules(AdminFormElement::select('city', 'Город '.renderComponent(new Helper('edit:order_calls.Город')))->setEnum($cities)
                        ->setHelpText('<div class="badge bg-info mr-4 fa-2x">Время у клиента: ' . (optional($model)->geo_cities ? Carbon::now('UTC')->addSecond($model->geo_cities->timezone)->format("H:i") : '-').'</div>'.$this->hCity($model)),$model),
                    $this->customRules(AdminFormElement::text('address', 'Адрес '.renderComponent(new Helper('edit:order_calls.Адрес')))->addValidationRule("required_if:status,INFO1,TRANSFER", 'Поле Адрес обязательно для заполнения при выбранном статусе'),$model)
                ])
                ->addColumn([
                    AdminFormElement::columns()->addColumn($checks),
                    AdminFormElement::html('<hr>'),
                    ($id) ? $this->customRules(AdminFormElement::textarea('comment', 'Комментарий к письму '.renderComponent(new Helper('edit:order_calls.Комментарий к письму')))->setRows(5), $model) : '',
                ]),
                $cart_q
        ]);

        $form->addFooter([
            AdminFormElement::columns()
                ->addColumn([
                    AdminFormElement::html('<p><b>Комментарий: </b> 1) Запрос клиента; 2) Готовность к покупке; 3) След. шаг;  '.renderComponent(new Helper('edit:order_calls.Комментарий')).'</p>'),
                    (!$id) ? AdminFormElement::textarea('comment', 'Комментарий')->setRows(10) :
                        AdminFormElement::view("admin::comment", ['id' => $id, 'model_type' => \App\Models\OrderCall::class, 'type' => null, 'height' => 700]),

                    AdminFormElement::columns()
                        ->addColumn([
                            AdminFormElement::text('company', 'Наименование компании')->setValueSkipped(true),
                            AdminFormElement::text('bank', 'Банк')->setValueSkipped(true),
                            AdminFormElement::text('inn', 'ИНН')->setValueSkipped(true),
                            AdminFormElement::text('kpp', 'КПП')->setValueSkipped(true),
                            AdminFormElement::text('bik', 'БИК')->setValueSkipped(true),
                        ])
                        ->addColumn([
                            AdminFormElement::text('uaddress', 'Юридический адрес')->setValueSkipped(true),
                            AdminFormElement::text('faddress', 'Фактический адрес')->setValueSkipped(true),
                            AdminFormElement::text('raschet', 'Расчетный счет')->setValueSkipped(true),
                            AdminFormElement::text('kschet', 'Корреспонденский счет')->setValueSkipped(true),
                        ])->setHtmlAttributes(['id' => 'ur_fields', 'style' => 'display:none']),
                ]),
            AdminFormElement::columns()
                ->addColumn([
                    ($id && $paid_amount) ? AdminFormElement::text('robolink10', 'Ссылка на Робокассу 20%')->setDefaultValue($link->get(getRobocassaLink($id, $paid_amount * .2)))->setValueSkipped(true) : '',
                ])
                ->addColumn([
                    ($id && $paid_amount && !in_array($model->status, ['TRANSFER', 'REJECT'])) ? AdminFormElement::text('cart_link', 'Ссылка на корзину')->setDefaultValue($link->get($this->getCartLink($id, $model)))->setValueSkipped(true) : '',
                ])
//            ($id && $paid_amount) ? AdminFormElement::text('robolink', 'Робокасса 100%')->setDefaultValue($this->getRobocassaLink($id, $paid_amount, 2))->setValueSkipped(true) : '',
//            ($id && $paid_amount) ? AdminFormElement::text('robolink40', 'Робокасса 40%')->setDefaultValue($this->getRobocassaLink($id, $paid_amount, 1))->setValueSkipped(true) : ''
        ]);

        if ($cart) {
            $items = AdminSection::getModel(Cart::class)->fireDisplay(['scopes' => ['withLeadRd', $id]]);
            $items->setParameter('order_call_id', $id);
            $items->setParameter('order_cart_id', optional($model->lead)->id);
            $items->setView('display.table_without_create');

            $cart_block = [];
            $cart_block[] = "<h3>Корзина (<b class='text-danger'>{$paid_amount} руб.</b>) ".renderComponent(new Helper('edit:order_calls.Корзина'))."</h3>";
            $cart_block[] = $items;
//            $this->getRelated($model, $cart, $cart_block);
//            $this->getModules($model, $cart, $cart_block);
            $form->addBody($cart_block);
        }

        $form->getButtons()->setButtons([
            'save_and_close' => new SaveAndClose(),
            'save' => new Save(),
            'delete' => new Delete(),
            'create' => new Create(),
        ]);
        return $form;
    }

    public function getModules($model, $cart, &$form)
    {
        $cart_item = $cart->where('is_module', null)->first();
        if (!$cart_item) return;
        $item = $cart_item->goods;
        if (!$item) return;
        $city = $model->geo_cities;
        if (!$city) return;
        $mods = $item->getModules($city);
        if (!$mods) return;
        $m_count = count($mods);
        if ($m_count == 0) return;
        $m = [];
        foreach ($mods as $mod) {
            $key = $mod['type_name'] ?: 'Прочее';
            $m[$key][] = $mod;
        }
        uasort($m, function ($a, $b) {
            if (is_null(current($a)['type_order'])) return;
            return current($a)['type_order'] <=> current($b)['type_order'];
        });
        $form[] = AdminFormElement::view('doc.leads.cart_modules', ['id' => $item->id, 'complectation' => $m, 'lead_id' => $model->id]);
    }

    public function getRelated($model, $cart, &$form)
    {
        $cart_item = $cart->where('is_module', null)->where('catalog_id', '<>', null)->first();
        if (!$cart_item) return;
        $item = $cart_item->goods;
        if (!$item) return;
        $city = $model->geo_cities;
        $catalog = $cart_item->catalog;
        if (!$catalog) return;
        $ancestors = $catalog->ancestors->values();
        $ancestors = $ancestors->push($catalog);
        $catalog = $ancestors->last();
        $catalogs = collect();
        foreach ($catalog->ancestors->reverse() as $item) {
            if (!$item->related->count()) continue;
            $catalogs = $item->related;
        }
        if (!$catalogs->count()) return;
        $domain = $city ? $city->subdomain : 'www';
        $related = $catalogs->map(function ($v) use ($domain) {
            return '<a href="https://' . $domain . '.ridersdrive.ru/catalog/' . $v->url . '" target="_blank" class="btn bg-purple btn-flat">' . $v->title . '</a> ';
        });
        if ($related->count()) {
            $form[] = "<h3>Сопутствующие категории / модули</h3>";
            $form[] = $related->implode('') . '<hr>';
        }
    }

    public function addModal($model)
    {
        if (!optional($model)->cart) return [];
        $catalogs = $model->cart->pluck('catalog_id')->all();
        $shaf = [89, 1571, 112, 1381, 253, 103];
        $doors = [1768, 1047, 21, 1046, 1044, 1045, 1631, 1796, 1049, 916, 1454, 1453, 133];
        $ids = array_merge($shaf, $doors);
        $spec = array_intersect($catalogs, Catalog::whereIn('parent_id', $ids)->pluck('id')->merge($ids)->all());
        $edits = [
            'delivery' => optional($model->transfer_fields)['delivery_edit'] ?? false,
            'date_delivery' => optional($model->transfer_fields)['date_delivery_edit'] ?? false,
            'measure' => optional($model->transfer_fields)['measure_need'] ?? false,
            'delivery_door' => optional($model->transfer_fields)['delivery_door_need'] ?? false,
            'terminal' => optional($model->transfer_fields)['terminal'] ?? false,
        ];
        $tk = optional($model->transfer_fields)['delivery_tk'];

        $item = $model->cart->where('is_module', null)->first();
        $date_delivery = '';
        $add_price = null;
        $add_active = 0;
        if ($item) {
            $link = optional(explode('/', $item->link))[2];
            if ($link) {
                $url = "https://{$link}/api/delivery/{$item->item_id}";
                $url2 = "https://{$link}/shop/item/{$item->item_id}";
                try {
                    $result = json_decode(file_get_contents($url, false, stream_context_create(["ssl" => ["verify_peer" => false, "verify_peer_name" => false]])), 1);
                    $result2 = json_decode(file_get_contents($url2, false, stream_context_create(["ssl" => ["verify_peer" => false, "verify_peer_name" => false]])), 1);
                } catch (\Exception $exception) {
                    $result = $result2 = null;
                }
                $time = optional($result)['time'];
                $add_price = optional($result2)['add_price'] ?: null;
                if (!empty($add_price)) {
                    $add_active = optional($result2)['add_active'] ?: 0;
                }
                if (!empty($time)) $date_delivery = now()->addWeekdays($time)->timezone(config('sleeping_owl.timezone'))->format(config('sleeping_owl.dateFormat'));

//                $formatter = new \IntlDateFormatter('ru_RU', \IntlDateFormatter::FULL, \IntlDateFormatter::FULL);
//                $formatter->setPattern('d MMMM');
//                $this->data['delivery_date'] = $d_date['time'] > 0 ? 'до ' . $formatter->format(Carbon::now()->addWeekdays($d_date['time'])->timestamp) : 'Согласовывается с менежером';

            }
        }
        if (empty($date_delivery)) $edits['date_delivery'] = true;
        if (empty($add_price)) $edits['delivery'] = true;

        $delivery_attr = str_replace(">", '\\\\>', '#transfer_fields->delivery_take, #transfer_fields->delivery_address, #transfer_fields->delivery_terminal, #transfer_fields->delivery_tk, #transfer_fields->delivery_price');

        return [
            AdminFormElement::html('<div class="form-group"><div type="button" class="btn btn-secondary" data-toggle="modal" data-target="#transfer-modal">Для переноса в заказ '.renderComponent(new Helper('edit:order_calls.Для переноса в заказ')).'</div></div>'),
            AdminFormElement::html('<div id="transfer-modal" class="modal fade" role="dialog"><div class="modal-dialog modal-xl"><div class="modal-content"><div class="modal-body">'),
//                ->addColumn([AdminFormElement::select('transfer_fields->form_surcharge', 'Форма доплаты',new FormSurcharge)->setDisplay('name')])
            AdminFormElement::columns()
                ->addColumn([
                    AdminFormElement::html('<h3>Доставка <small class="font-weight-light">(кто оплачивает)</small>'.
                        renderComponent(new Helper('lead_transfer')).
                        '</h3><hr>'),
                    AdminFormElement::columns([[
                        AdminFormElement::date('transfer_fields->date_delivery', 'Желаемая дата доставки '.renderComponent(new Helper('edit:order_calls.transfer.date_delivery')))->setDefaultValue($date_delivery)->setHtmlAttributes(['data-date-default-view-date' => $date_delivery])
                            ->setHtmlAttributes(!$edits['date_delivery'] ? ['readonly' => true] : [])
                            ->addValidationRule("required_if:status,TRANSFER", 'Поле Желаемая дата доставки обязательно для заполнения при текущем статусе')
                    ], [
                        AdminFormElement::html('<h4> </h4>'),
                        AdminFormElement::checkbox('transfer_fields->date_delivery_edit', 'Внести вручную')
                            ->setDefaultValue($edits['date_delivery'])
                            ->setHtmlAttributes(['onchange' => "document.getElementById('transfer_fields->date_delivery').readOnly = !this.checked;"])
                    ]]),
                    AdminFormElement::columns()
                        ->addColumn([
                            AdminFormElement::select('transfer_fields->delivery_take', 'Забор от поставщика '.renderComponent(new Helper('edit:order_calls.transfer.delivery_address')), Order::DELIVERY_MEMBERS)->setSelect2(true)->setDefaultValue($add_active ? 'A' : 'C')->setHtmlAttributes(!$edits['delivery'] ? ['readonly' => true] : [])->setHtmlAttribute('style', 'width:100%!important'),
                            AdminFormElement::select('transfer_fields->delivery_address', 'Доставка до адреса '.renderComponent(new Helper('edit:order_calls.transfer.delivery_terminal')), Order::DELIVERY_MEMBERS)->setSelect2(true)->setDefaultValue('C')->setHtmlAttributes(!$edits['delivery'] ? ['readonly' => true] : [])->setHtmlAttribute('style', 'width:100%!important'),
                            AdminFormElement::select('transfer_fields->delivery_terminal', 'Перевозка до терминала ТК '.renderComponent(new Helper('edit:order_calls.transfer.delivery_take')), Order::DELIVERY_MEMBERS)->setSelect2(true)->setDefaultValue($add_active ? 'A' : 'C')->setHtmlAttributes(!$edits['delivery'] ? ['readonly' => true] : [])->setHtmlAttribute('style', 'width:100%!important'),
                        ])
                        ->addColumn([
                            AdminFormElement::html('<h4> </h4>'),
                            AdminFormElement::checkbox('transfer_fields->delivery_edit', 'Внести вручную')
                                ->setDefaultValue($edits['delivery'])
                                ->setHtmlAttributes(['onchange' => "if(this.checked) $(`$delivery_attr`).removeAttr('readonly');else $(`$delivery_attr`).attr('readonly','readonly');"]),
                            AdminFormElement::select('transfer_fields->delivery_tk', 'Выбор ТК '.renderComponent(new Helper('edit:order_calls.transfer.delivery_tk')), Intercity::class)->setDefaultValue($add_active ? 1 : $tk)->setDisplay('name')->nullable()->setSelect2(true)->setHtmlAttributes(!$edits['delivery'] ? ['readonly' => true] : [])->setHtmlAttribute('style', 'width:100%!important'),
                            AdminFormElement::number('transfer_fields->delivery_price', 'Стоимость перевозки '.renderComponent(new Helper('edit:order_calls.transfer.delivery_price')))->setStep(1)->setDefaultValue($add_price)->setHtmlAttribute('data-add_active', $add_active)->setHtmlAttributes(!$edits['delivery'] ? ['readonly' => true] : [])
                                ->addValidationRule("required_if:status,TRANSFER", 'Поле Стоимость перевозки обязательно для заполнения при текущем статусе')
                        ]),

                    AdminFormElement::columns([[
                        AdminFormElement::checkbox('transfer_fields->delivery_door_need', 'Доставка до дома '.renderComponent(new Helper('edit:order_calls.transfer.delivery_door_need')))->setHtmlAttributes(['data-toggle' => "collapse", 'data-target' => "#delivery_door"]),
                        AdminFormElement::html('<div class="collapse' . ($edits['delivery_door'] ? ' show' : '') . '" id="delivery_door">'),
                        AdminFormElement::number('transfer_fields->delivery_door', 'Сумма доставки'),
                        AdminFormElement::html('</div>'),
                    ], [
                        AdminFormElement::checkbox('transfer_fields->measure_need', 'Нужен монтаж '.renderComponent(new Helper('edit:order_calls.transfer.measure_need')))->setHtmlAttributes(['data-toggle' => "collapse", 'data-target' => "#measure_need"]),
                        AdminFormElement::html('<div class="collapse' . ($edits['measure'] ? ' show' : '') . '" id="measure_need">'),
                        AdminFormElement::number('transfer_fields->measure', 'Сумма монтажа'),
                        AdminFormElement::html('</div>'),
                    ], [
                        AdminFormElement::checkbox('transfer_fields->delivery_floor', 'Подъем на этаж '.renderComponent(new Helper('edit:order_calls.transfer.delivery_floor'))),
                    ]]),
                    config('franchise.key') == 'ekb' ? AdminFormElement::columns([
                        [
                            AdminFormElement::html('<label class="control-label "> </label>'),
                            AdminFormElement::checkbox('transfer_fields->terminal', 'Терминал')->setHtmlAttributes(['data-toggle' => "collapse", 'data-target' => "#terminal"])
                        ],
                        [
                            AdminFormElement::html('<div class="collapse' . ($edits['terminal'] ? ' show' : '') . '" id="terminal">'),
                            AdminFormElement::number('transfer_fields->terminal_sum', 'Сумма'),
                            AdminFormElement::html('</div>')
                        ]
                    ]) : ''
                ], 7)
                ->addColumn([
                    AdminFormElement::html('<h3>Рассрочка</h2><hr>'),
                    AdminFormElement::select('transfer_fields->inst_bank_id', 'Банк '.renderComponent(new Helper('edit:order_calls.transfer.inst_bank_id')), new Bank)->setDisplay('name')->addValidationRule("required_with:transfer_fields->inst_screen"),
                    AdminFormElement::number('transfer_fields->inst_amount', 'Сумма от банка '.renderComponent(new Helper('edit:order_calls.transfer.inst_amount')))->setStep(1)->addValidationRule("required_with:transfer_fields->inst_bank_id", 'Поле Сумма от банка обязательно для заполнения при наличии банка'),
                    AdminFormElement::number('transfer_fields->inst_pay', 'Первоначальный взнос '.renderComponent(new Helper('edit:order_calls.transfer.inst_pay')))->setStep(1)->addValidationRule("required_with:transfer_fields->inst_bank_id", 'Поле Первоначальный взнос обязательно для заполнения при наличии банка'),
                    AdminFormElement::select('transfer_fields->inst_send', 'Кред. договор '.renderComponent(new Helper('edit:order_calls.transfer.inst_send')), Order::CREDIT_CONTRACT_STATUS),
                    AdminFormElement::checkbox('transfer_fields->inst_screen', 'Скрин выдачи кредита в фин.сервисе '.renderComponent(new Helper('edit:order_calls.transfer.inst_screen')))->addValidationRule("required_with:transfer_fields->inst_bank_id"),
                    AdminFormElement::checkbox('transfer_fields->kd_check', 'Про отправку КД уведомил '.renderComponent(new Helper('edit:order_calls.transfer.kd_check'))),
                    AdminFormElement::checkbox('transfer_fields->kd_load', 'КД, паспорт загружены '.renderComponent(new Helper('edit:order_calls.transfer.kd_load'))),
                    $spec ? AdminFormElement::checkbox('transfer_fields->spec_signed', 'Спецификация подписана '.renderComponent(new Helper('edit:order_calls.transfer.spec_signed'))) : '',
                ]),
            AdminFormElement::columns()
                ->addColumn([
                    AdminFormElement::select('transfer_fields->inform_type', 'Как информировать клиента '.renderComponent(new Helper('edit:order_calls.transfer.inform_type')), Order::INFORM_TYPES)
                        ->addValidationRule("required_if:status,TRANSFER", 'Поле Как информировать клиента обязательно для заполнения при текущем статусе'),
//                    AdminFormElement::text('transfer_fields->amount_to_pay', 'Сумма к оплате'),
//                    ((boolean)$paid_amount) ? AdminFormElement::html('<span style="color: red">Итого корзины: ' . (float)$paid_amount . ' Руб.</span>') : ''
                ]),
            AdminFormElement::textarea('transfer_fields->comment', 'Комментарий к заказу '.renderComponent(new Helper('edit:order_calls.transfer.comment')))->setRows(3)
                ->addValidationRule("required_if:status,TRANSFER", 'Поле Комментарий к заказу обязательно для заполнения при текущем статусе'),
            AdminFormElement::html('</div><div class="modal-footer"><span class="btn btn-success" onclick="fill_comment()">Добавить к письму</span> <button role="button" class="btn btn-default" data-dismiss="modal">Закрыть</button></div></div></div></div>')
        ];
    }

    public function tClient($client)
    {
        $form = AdminForm::card();
        $form->addBody([
            $this->getClientCard($client),
        ]);
        $form->getButtons()->setButtons([]);
        return $form;
    }

    public function tTransfer($paid_amount)
    {
        $order_form = AdminForm::card();
        $order_form->addBody([
            AdminFormElement::columns()
                ->addColumn([AdminFormElement::select('transfer_fields->form_surcharge', 'Форма доплаты', new FormSurcharge)->setDisplay('name')])
                ->addColumn([AdminFormElement::date('transfer_fields->date_delivery', 'Желаемая дата доставки')]),
            AdminFormElement::html('<h2>Доставка (кто оплачивает)</h2><hr>'),
            AdminFormElement::columns()
                ->addColumn([AdminFormElement::select('transfer_fields->delivery_take', 'Забор от поставщика', Order::DELIVERY_MEMBERS)])
                ->addColumn([AdminFormElement::select('transfer_fields->delivery_terminal', 'Перевозка до терминала ТК', Order::DELIVERY_MEMBERS)]),
            AdminFormElement::columns()
                ->addColumn([AdminFormElement::select('transfer_fields->delivery_address', 'Доставка до адреса', Order::DELIVERY_MEMBERS)])
                ->addColumn([AdminFormElement::select('transfer_fields->delivery_floor', 'Подъем на этаж', Order::DELIVERY_MEMBERS)]),
            AdminFormElement::html('<h2>Рассрочка</h2><hr>'),
            AdminFormElement::columns()
                ->addColumn([AdminFormElement::select('transfer_fields->inst_bank_id', 'Банк', new Bank)->setDisplay('name')])
                ->addColumn([AdminFormElement::text('transfer_fields->inst_amount', 'Сумма от банка')])
                ->addColumn([AdminFormElement::text('transfer_fields->inst_pay', 'Первоначальный взнос')]),
            AdminFormElement::columns()
                ->addColumn([
                    AdminFormElement::text('transfer_fields->amount_to_pay', 'Сумма к оплате'),
                    ((boolean)$paid_amount) ?
                        AdminFormElement::html('<span style="color: red">Итого корзины: ' . (float)$paid_amount . ' Руб.</span>') : ''
                ]),
        ]);
        $order_form->getButtons()->setButtons([
            'save_and_close' => new SaveAndClose(),
            'save' => new Save()
        ]);

        return $order_form;
    }

    public function tCalc()
    {
        $panel = AdminForm::elements();
        $panel->addElement(AdminFormElement::view('admin::leads.calc', []));
        return $panel;
    }

    public function hCity($model)
    {
        if (!$model) return '';
        $region = optional($model->old_cities)->region;
        $region = $region ? $region->name : '';
        $city_d_address = optional($model->geo_cities)->address;
        $help = "<b>Область:</b> {$region}<br><b>Адрес доставки:</b> {$city_d_address}";
        if ($city = $model->geo_cities) {
            $region = $city->region;
        } elseif ($model->old_cities) {
            $region = optional($model->old_cities)->region;
        }
        $region_txt = !empty($region) ? $region->name . ', ' : '';
        $city_map = '<a target="_blank" href="http://maps.yandex.ru/?l=map&text=' . urlencode($region_txt . $model->city . ' транспортная компания') . '" tabindex="0">На карте '.renderComponent(new Helper('edit:order_calls.На карте')).'</a>';
        return $city_map . '<a tabindex="0" data-html="true" class="float-right" data-toggle="popover" data-trigger="hover focus" style="font-size:2em;color:blue;word-break: normal;" data-content="' . htmlspecialchars($help) . '"><i class="fa fa-question-circle"></i></a>';
    }

    function appendStyles($el)
    {
        $html = $el->getText();
        $style = $el->getFontStyle();
        if ($style->getItalic()) $html = "<i>{$html}</i>";
        if ($style->getBold()) $html = "<b>{$html}</b>";
        $html = "<span style=\"font-family: '{$style->getName()}'; color: #{$style->getColor()}; font-size: {$style->getSize()}pt\">{$html}</span>";

        return $html;
    }

    function getScripts()
    {
        try {
            $files = \Storage::disk('help')->files('Скрипты');
            $out = [];
            foreach ($files as $file) {
                \Storage::disk('public')->put($file,
                    \Storage::disk('help')->get($file));
                $f = \PhpOffice\PhpWord\IOFactory::load(storage_path('app/public/' . $file));
                $prev_el = null;
                foreach ($f->getSections()[0]->getElements() as $el) {
                    $html = '';
                    if ($prev_el instanceof \PhpOffice\PhpWord\Element\ListItem &&
                        !$el instanceof \PhpOffice\PhpWord\Element\ListItem) $html .= '</ol>';
                    switch (true) {
                        case $el instanceof \PhpOffice\PhpWord\Element\Text:
                            $html .= $this->appendStyles($el);
                            break;
                        case $el instanceof \PhpOffice\PhpWord\Element\TextRun:
                            $html .= '<p>';
                            foreach ($el->getElements() as $e) {
                                if ($e instanceof \PhpOffice\PhpWord\Element\Text)
                                    $html .= $this->appendStyles($e);
                            }
                            $html .= '</p>';
                            break;
                        case $el instanceof \PhpOffice\PhpWord\Element\TextBreak:
                            $html .= '<br>';
                            break;
                        case $el instanceof \PhpOffice\PhpWord\Element\ListItem:
                            if (!$prev_el instanceof \PhpOffice\PhpWord\Element\ListItem) $html .= '<ol>';
                            $html .= '<li>' . $el->getText() . '</li>';
                            break;
                    }
                    $k = str_replace(['Скрипты/', '.docx', '.doc'], '', $file);
                    $out[$k] = isset($out[$k]) ? $out[$k] . $html : $html;
                    $prev_el = $el;
                }
            }
        } catch (\Exception $exception) {
            $out = [];
        }
        return $out;
    }

    public function add_fields($model)
    {
        $first_status_change = $model ? $model->audits()->where('created_at','>=',$model->created_at)->whereJsonContains('old_values',['status'=>'NEW'])->first() : null;
        $robo = '';
        if ($model) {
            try {
                $robo = $this->getRobokassaStatus($model->id);
            } catch (\Exception $exception) {}

            $add_fields = is_array($model->add_fields) ? $model->add_fields : json_decode($model->add_fields, true);
            if (optional($add_fields)['order'] && $model->yafirst && ($model->yafirst->status != 'CAPTURED' || !optional($add_fields['order'])['paid_amount'])) {
                try {
                    $yapay = getYapayStatus($model->id);
                } catch (\Exception $exception) {}
            }
        }
        $lead = $model ? ($model->lead ?: null) : null;
        $region = $model ? optional($model->old_cities)->region : null;
        $region = $region ? $region->id : 0;
        $cart = $model ? Cart::withLead($model->id)->with('catalog')->get() : false;
        if ($model) {
            $paid_amount = $cart->map(function ($value) {
                return $value->quantity * $value->price;
            })->sum();
            $add_fields = is_array($model->add_fields) ? $model->add_fields : json_decode($model->add_fields, true);
            if (isset($add_fields['order'])) {
                $add_order = $add_fields['order'];
//                $add_client = $add_fields['client'];
                //'<div class="form-group"><label class="control-label">Комментарий к заказу:</label><br>' . $add_order['comment'] . '</div>';
                if ($model->type == 'cart' || $model->type == 'stock_good') {
                    $amount_to_pay_html = '<div class="form-group mr-3"><label class="control-label">К оплате:</label><br><strong>' . optional($add_order)['amount_to_pay'] . ' руб.</strong></div>';
                    $paid_amount_html = '<div class="form-group mr-3"><label class="control-label">Оплачено:</label><br><strong>' . optional($add_order)['paid_amount'] . ' руб.</strong></div>';
                    $credit = $model && $model->pay_type == 0 && optional($add_order)['order_type'] == -1 ? ' РАССРОЧКА' : '';
                    $add = '';
                    if (!empty($add_fields['add'])) {
                        foreach (json_decode($add_fields['add'], 1) as $k => $item) {
                            $add .= !is_array($item) ? ' <code>[' . $k . ':' . $item . ']</code>' : ' <code>[' . $k . ':' . implode(', ', $item) . ']</code>';
                        }
                    }
                    $model_type_html = '<span class="badge bg-primary mr-3">' . $model::ORDER_TYPES[$add_order['order_type']] . $credit . '</span>' . $add;//</div>';
                    $order_type_label = (!empty($add_order['paid_amount']) && $add_order['paid_amount'] > 0 ||
                        ($model->robofirst && optional($model->robofirst)->status == 100) ||
                        ($model->yafirst && optional($model->yafirst)->status == 'CAPTURED') ||
                        ($model->yookassafirst && optional($model->yookassafirst)->status == 'succeeded') ||
                        ($model->sberfirst && optional($model->sberfirst)->status == 2)) ? 'success' : 'danger';
                    $robo_perc = false;
                    $bank = $model->robofirst ? 'Робокасса' : 'Сбер';
                    $bank = $model->yafirst ? 'ЯПЭЙ' : $bank;
                    $bank = $model->yookassafirst ? 'ЮКасса' : $bank;
                    switch ($add_order['order_type']) {
                        case 1: $robo_perc = "$bank 40%";break;
                        case 2: $robo_perc = "$bank 100%";break;
                        case 9: $robo_perc = "$bank 20%";break;
                        case 10: $robo_perc = "$bank 50%";break;
                        case 15: $robo_perc = "ЯПЭЙ";break;
                        case 16: $robo_perc = "СПЛИТ";break;
                        case 17: $robo_perc = "ЮКАССА";break;
                    }
                    if ($model->robofirst) $robo_perc .= '<br><small>'.RoboPayment::STATE_CODES[optional($model->robofirst)->status].'</small>';
                    $robokassa = $robo_perc ? '<span class="badge bg-' . $order_type_label . ' mr-3">' . $robo_perc . '</span>' : '';
                    if (isset($add_order['floor'])) $floor = $add_order['floor'];
                }
            } elseif (is_array($add_fields)) {
                $add_fields_html = '';
                $credit_info = [];
                foreach ($add_fields as $k => $item) {
                    if (in_array($k,['сумма','срок'])) {
                        $credit_info[] = str_replace(['сумма','срок'],['Желаемый ПВ','Желаемый срок'],$k) . ": " . str_replace(['месяцев','месяца'],'мес.',$item);
                        continue;
                    }
                    $add_fields_html .= !is_array($item) ? '<code>[' . $k . ':' . $item . ']</code>'
                        : '<code>[' . $k . ':' . implode(', ', $item) . ']</code>';
                }
                $add_fields_html = $add_fields_html != '' ? '<div class="form-group mr-3">' . $add_fields_html . "</div>" : null;
            }

            if ($model->robofirst && optional($model->robofirst)->status == 100) {
                $paid_amount_html = '<div class="form-group d-block p-2">
                                        <label class="control-label">Оплачено:</label><strong>' . round($model->robofirst['out_sum'], 2) . ' руб.</strong>
                                    </div>';
            }
            if ($model->yafirst && optional($model->yafirst)->status == 'CAPTURED') {
                $paid_amount_html = '<div class="form-group d-block p-2">
                                        <label class="control-label">Оплачено:</label><strong>' . round(json_decode(optional($model->yafirst)->info,1)['order']['orderAmount'], 2) . ' руб.</strong>
                                    </div>';
            }

            if (!empty($paid_amount) && empty($robokassa) && $model->robofirst) {
                $order_type_label = ($model->robofirst && optional($model->robofirst)->status == 100) ? 'success' : 'danger';
                $perc = round(100 * $model->robofirst['out_sum'] / $paid_amount);
                $amount_to_pay_html = '<div class="form-group d-block p-2"><label class="control-label">К оплате:</label><strong>' . $paid_amount . ' руб.</strong></div>';
                $robokassa = ' <div><span class="badge bg-'.$order_type_label.'">РОБОКАССА ' . $perc . '%</span><div class="text-muted small">'.RoboPayment::STATE_CODES[optional($model->robofirst)->status].'</div></div>';
            } elseif ($model->sberfirst && !empty($paid_amount)) {
                $order_type_label = optional($model->sberfirst)->status == 2 ? 'success' : 'danger';
                $perc = round(100 * $model->sberfirst['out_sum'] / $paid_amount);
                $amount_to_pay_html = '<div class="form-group d-block p-2"><label class="control-label">К оплате:</label><strong>' . $paid_amount . ' руб.</strong></div>';
                $robokassa = ' <div><span class="badge bg-'.$order_type_label.'">Сбер ' . $perc . '%</span><div class="text-muted small">'.SberPayment::STATE_CODES[optional($model->sberfirst)->status].'</div></div>';
            } elseif ($model->yafirst && !empty($paid_amount)) {
                $order_type_label = optional($model->yafirst)->status == 'CAPTURED' ? 'success' : 'danger';
                $pay_type = $add_fields['order']['order_type'] ?? 15;
                $pay_type = $pay_type == 15 ? 'ЯПЭЙ' : 'СПЛИТ';
                $amount_to_pay_html = '<div class="form-group d-block p-2"><label class="control-label">К оплате:</label><strong>' . $paid_amount . ' руб.</strong></div>';
                $robokassa = ' <div><a target="_blank" href="https://ridersdrive.ru/yapay/'.$model->yafirst->inv_id.'" class="badge bg-'.$order_type_label.' text-nowrap">'.$pay_type.'<br><span class="text-muted small">'.optional($model->yafirst)->status.'</span></a></div>';
            }
        }
        $create_types = OrderType::where('show', 1)->get()->mapWithKeys(function ($v) {
            return [$v->alias => '<i class="' . htmlentities($v->icon) . '" aria-hidden="true" title="' . htmlentities($v->name) . '"></i>'];
        })->sortBy('order')->toArray();

        $item = false;
        if ($cart && $cart->where('is_module',false)->where('item_id','<>',0)->count()) {
            $item = $cart->where('is_module',false)->where('item_id','<>',0)->first();
            $catalog = $item->catalog ?? optional($item->items)->catalog;
            $item['model'] = $item->catalog_id ? optional($item->catalog)->full_title : optional($catalog)->full_title;
            $item['model'] .= !empty($item->item_id) ? ", арт. {$item->item_id}" : '';
        }
        $lead_info = $model ? (new \App\Http\Controllers\FinancialReportController())->leadStatusReport(null, null, $model->id) : '';
        return [
            AdminFormElement::html('<div class="form-inline">'),
            (!$model) ? AdminFormElement::radio('type', '',
                $create_types)
                ->setHtmlAttribute('style', 'display: none')
                ->setSortable(false)
                ->setDefaultValue('call') : '',
            ($model) ? AdminFormElement::html('<div class="form-group"><div class="badge bg-warning" style="font-size: 1em;color:black!important">
                <i class="fa fa-calendar" aria-hidden="true"></i> Дата создания: '
                . date("d.m.Y H:i", strtotime($model->created_at)) . renderComponent(new Helper('edit:order_calls.Дата создания')) . '</div></div>&nbsp;') : '',
            $this->getOrder($lead),
            ($model && isset($order_type_html)) ? $order_type_html : '',
            ($model && isset($model_type_html)) ? $model_type_html : '',
            ($model && isset($robokassa) ? $robokassa : ''),
            ($model && isset($comment_html)) ? $comment_html : '',
            ($model && isset($amount_to_pay_html)) ? $amount_to_pay_html : '',
            ($model && isset($paid_amount_html)) ? $paid_amount_html : '',
            ($model && isset($add_fields_html) && $model->type != 'cart') ? AdminFormElement::html($add_fields_html) : '',
            (!$model) ? AdminFormElement::checkbox('is_ur_client', 'Юр. лицо&nbsp;')
                ->setValueSkipped(true)
                ->setHtmlAttributes(['class' => 'is_ur_client', 'onclick' => 'showUrFields()']) : '',
//            $model?->catalog_id ? '<span class="badge bg-secondary mr-3">'.($model?->catalog->class ?? '?').'</span>' : '',
            ($model && $model->importance >= 9 && !in_array($model->status, ['TRANSFER', 'REJECT'])) ?
                AdminFormElement::checkbox('importanceTmp', '&nbsp;🔥'.renderComponent(new Helper('edit:order_calls.Огонь')))->setDefaultValue(true) :
                AdminFormElement::checkbox('importanceTmp', '&nbsp;🔥'.renderComponent(new Helper('edit:order_calls.Огонь')))->setDefaultValue(false),
//            AdminFormElement::checkbox('nds', '&nbsp;НДС'),
//            AdminFormElement::checkbox('is_repair', '&nbsp;Ремонт'.renderComponent(new Helper('edit:order_calls.Ремонт'))),
            AdminFormElement::checkbox('is_svo', '&nbsp;СВО'.renderComponent(new Helper('edit:order_calls.СВО'))),
            ($model && isset($floor)) ? "<span class='badge bg-danger'>Этаж: {$floor}</span> " : '',
//            ($model) ? AdminFormElement::html(" <a class='btn btn-outline-secondary btn-sm mr-auto' href='{$model->url}' target='_blank'>Ссылка</a>") : '',
//            ($model) ? AdminFormElement::html($this->getRobokassaStatus($model->id)) : '',
            AdminFormElement::view('admin::leads.calc',
                [
                    'id' => $cart ? $model->id : false,
                    'key' => explode('.', \Request::server('HTTP_HOST'))[0],
                    'region' => $region,
                    'item' => $item,
                    'robo' => $robo,
                    'model' => $model,
                    'first_status_change' => $first_status_change,
//                    'scripts' => $this->getScripts()
                ]),
            AdminFormElement::html('</div>'),
            AdminFormElement::html($lead_info),
            ($model && isset($credit_info) && !empty($credit_info)) ? '<div class="form-group mt-3 mr-3"><h4>'.implode("&nbsp;&nbsp;&nbsp;&nbsp;",$credit_info).'</h4></div>' : '',
        ];
    }

    public function getRobokassaStatus($id)
    {
        $options = stream_context_create(["ssl" => ["verify_peer" => false, "verify_peer_name" => false]]);
        $login = config('services.robokassa.login');
        $pass = config('services.robokassa.validationPassword');
        $url_start = 'https://auth.robokassa.ru/Merchant/WebService/Service.asmx/OpState?';

        $pay = RoboPayment::where('inv_id', $id)->get();
        if (!$pay->count()) {
            $signature = vsprintf('%s:%u:%s', [$login, $id, $pass]);
            $url = $url_start . 'MerchantLogin=' . $login . '&InvoiceID=' . $id . '&Signature=' . md5($signature);
            $result = simplexml_load_string(file_get_contents($url, false, $options));
            if ($result->Result->Code == 0) {
                $rb = new RoboPayment([
                    'lead_id' => $id,
                    'inv_id' => $id,
                    'out_sum' => $result->Info->IncSum,
                    'status' => (int)$result->State->Code,
                    'info' => json_encode($result),
                    'result' => '[]',
                    'created_at' => Carbon::parse($result->State->StateDate)->format('Y-m-d H:i:s')
                ]);
                $rb->save();
            }
        }

        $pays = RoboPayment::where('lead_id', $id)->orderBy('created_at')->get();
        if ($pays->count()) {
            foreach ($pays as $item) {
                $signature = vsprintf('%s:%u:%s', [$login, $item->inv_id, $pass]);
                $url = $url_start . 'MerchantLogin=' . $login . '&InvoiceID=' . $item->inv_id . '&Signature=' . md5($signature);
                $result = simplexml_load_string(file_get_contents($url, false, $options));
                if ($result->Result->Code != 0) continue;
                $item->info = json_encode($result);
                $item->status = (int)$result->State->Code;
                $item->save();
            }
            return view('admin::leads.robo_status', compact('pays'));
        }

        return '';
    }

    public function getHeaderHtml($key)
    {
        $date = now();
        $plan = CatalogClass::Isrd()->whereMonth('date',$date->format("m"))->whereYear('date',$date->format("Y"))->get()->pluck('value', 'key')->toArray();
        $plan_sales = CatalogSales::Isrd()->whereMonth('date',$date->format("m"))->whereYear('date',$date->format("Y"))->get()->pluck('value', 'key')->toArray();
        $plan2 = LeadClass::Isrd()->whereMonth('date',$date->format("m"))->whereYear('date',$date->format("Y"))->get()->pluck('value', 'key')->toArray();
        $plan_cats = LeadCatalog::Isrd()->whereMonth('date',$date->format("m"))->whereYear('date',$date->format("Y"))->get()->pluck('value', 'key')->toArray();
        $plan3 = ConversionClass::Isrd()->whereMonth('date',$date->format("m"))->whereYear('date',$date->format("Y"))->get()->pluck('value', 'key')->toArray();
        $plan_conv = ConversionCatalog::Isrd()->whereMonth('date',$date->format("m"))->whereYear('date',$date->format("Y"))->get()->pluck('value', 'key')->toArray();
        $plan4 = AverageClass::Isrd()->whereMonth('date',$date->format("m"))->whereYear('date',$date->format("Y"))->get()->pluck('value', 'key')->toArray();
        $plan5 = CatalogScore::Isrd()->whereMonth('date',$date->format("m"))->whereYear('date',$date->format("Y"))->get()->pluck('value', 'key')->toArray();
        $plan_average = AverageCatalog::Isrd()->whereMonth('date',$date->format("m"))->whereYear('date',$date->format("Y"))->get()->pluck('value', 'key')->toArray();
        $cats = \Cache::remember('rd' . ".leads.info.catalogs", now()->addHour(), function () use ($date) {
            $leads = OrderCall::with('cart.catalog.parent','order')->whereNull('parent_id')->whereMonth('created_at', $date->month)->whereYear('created_at', $date->year)->whereNull('black_list')->whereNull('deleted_at')->get();
            $orders = OrderNew::with('lead.cart.catalog.parent','suppliers.carts','lead.cart.user.roles','moneys')->whereMonth('created_at', $date->month)->whereYear('created_at', $date->year)->whereNotIn('status', ['RETURN','NEEDRETURN','PRETENSE','INPROCESSCREDIT'])->get();
            return $this->getConversionCatalogClass($leads, $orders);
        });
        $catalogs = \Cache::remember("rd.reports.catalogs2",now()->addDay(), fn() => \App\Models\Catalog::withDepth()->with('parent','descendants')->whereNotIn('id',[1,2,6,7])->whereNotNull('class')->orderBy('_lft')->get());
        $catalogs->load('settings');
        $orders2 = \Cache::remember('rd' . ".leads.info.catalog_class", now()->addHour(), function () {
            return $this->getCatalogClass();
        });
        $orders2['D'] = $orders2->only(['D', 'D1', 'D2', 'D3', 'D4'])->sum();

        $leads_plan_category = \Cache::remember('rd' . ".leads.info.lead_class", now()->addHour(), function () use ($date) {
            $leads = OrderCall::with('cart.catalog.parent')->whereNull('parent_id')->whereMonth('created_at', $date->month)->whereYear('created_at', $date->year)->whereNull('black_list')->whereNull('deleted_at')->get();
            return $this->getLeadsCatalogClass($leads);
        });
        $orders_count_category = \Cache::remember('rd' . ".leads.info.orders_count_category", now()->addHour(), function () use ($date){
            $orders = OrderNew::with('lead.cart.catalog.parent','suppliers.carts','lead.cart.user.roles','moneys')->whereMonth('created_at', $date->month)->whereYear('created_at', $date->year)->whereNotIn('status', ['RETURN','NEEDRETURN','PRETENSE','INPROCESSCREDIT'])->get();
            return $this->getOrdersCountClass($orders);
        });
        $orders_count_category['D'] = collect($orders_count_category)->only(['D', 'D1', 'D2', 'D3', 'D4'])->sum();
        $conversion_class = \Cache::remember('rd' . ".leads.info.conversion_class", now()->addHour(), function () use ($leads_plan_category, $orders_count_category){
            return [
                'D'=> optional($leads_plan_category)['D'] > 0 ? 100 * optional($orders_count_category)['D'] / optional($leads_plan_category)['D'] : 0,
                'D1'=> optional($leads_plan_category)['D1'] > 0 ? 100 * optional($orders_count_category)['D1'] / optional($leads_plan_category)['D1'] : 0,
                'D2'=> optional($leads_plan_category)['D2'] > 0 ? 100 * optional($orders_count_category)['D2'] / optional($leads_plan_category)['D2'] : 0,
                'D3'=> optional($leads_plan_category)['D3'] > 0 ? 100 * optional($orders_count_category)['D3'] / optional($leads_plan_category)['D3'] : 0,
                'D4'=> optional($leads_plan_category)['D4'] > 0 ? 100 * optional($orders_count_category)['D4'] / optional($leads_plan_category)['D4'] : 0,
            ];
        });

        $average_class = \Cache::remember('rd' . ".leads.info.average_class", now()->addHour(), function () use ($orders2, $orders_count_category){
            return [
                'D'=> optional($orders_count_category)['D'] > 0 ? optional($orders2)['D'] / optional($orders_count_category)['D'] : 0,
                'D1'=> optional($orders_count_category)['D1'] > 0 ? optional($orders2)['D1'] / optional($orders_count_category)['D1'] : 0,
                'D2'=> optional($orders_count_category)['D2'] > 0 ? optional($orders2)['D2'] / optional($orders_count_category)['D2'] : 0,
                'D3'=> optional($orders_count_category)['D3'] > 0 ? optional($orders2)['D3'] / optional($orders_count_category)['D3'] : 0,
                'D4'=> optional($orders_count_category)['D4'] > 0 ? optional($orders2)['D4'] / optional($orders_count_category)['D4'] : 0,
            ];
        });

        $scores = [
            'D'=> 0,
        ];

        /*$cert = \Cache::remember(config('franchise.key') . ".leads.info.certification", now()->addDay(), function () {
            return Certification::with(['user' => function ($q) {
                $q->where('status', '<>', 'fired')->with('roles')->excludeOtherFranchises();
            }])
                ->where('checked', 1)->get()->filter(function ($v) {
                    return !empty($v->user) && $v->user->hasRole('certification');
                })
                ->groupBy('user.fi')->sortByDesc(function ($v) {
                    return count($v);
                })->take(5)
                ->map(function ($v, $k) {
                    return '<div class="list-group-item list-group-item-action">' . $k . ' <b class="float-right">' . count($v) . '</b></div>';
                })
                ->implode('');
        });*/

        return view('admin::rd.info.leads_info', compact('catalogs','cats','plan_cats','plan_sales','plan_conv','plan_average','plan','plan2','plan3','plan4','plan5', 'orders2', 'leads_plan_category', 'orders_count_category', 'conversion_class', 'average_class','scores', 'key'));
    }

    public function getPanelButtons($user_options)
    {
        $html = (auth()->user()->isAdmin() || auth()->user()->hasRole(['topsales','seniorsales','subdostavka'])) ? $this->getTopSalesCounts($user_options) . ' ' . $this->getSalesCounts() : $this->getSalesCounts();
        $my_leads = request()->has('myleads') ?
            http_build_query(\Arr::except(request()->query(), 'myleads')) :
            http_build_query(\Arr::add(request()->query(), 'myleads', 1));
        $tasks = request()->has('tasks') ?
            http_build_query(\Arr::except(request()->query(), 'tasks')) :
            http_build_query(\Arr::add(request()->query(), 'tasks', 1));
        $is_ur = request()->has('isur') ?
            http_build_query(\Arr::except(request()->query(), 'isur')) :
            http_build_query(\Arr::add(request()->query(), 'isur', 1));
        $is_dg = request()->has('isdg') ?
            http_build_query(\Arr::except(request()->query(), 'isdg')) :
            http_build_query(\Arr::add(request()->query(), 'isdg', 1));
        $is_st = request()->has('isst') ?
            http_build_query(\Arr::except(request()->query(), 'isst')) :
            http_build_query(\Arr::add(request()->query(), 'isst', 1));
        $is_am = request()->has('isam') ?
            http_build_query(\Arr::except(request()->query(), ['isam','isrd'])) :
            http_build_query(\Arr::add(\Arr::except(request()->query(), ['isrd','isam']), 'isam', 1));
        $is_rd = request()->has('isrd') ?
            http_build_query(\Arr::except(request()->query(), ['isrd','isam'])) :
            http_build_query(\Arr::add(\Arr::except(request()->query(), ['isrd','isam']), 'isrd', 1));
        $is_am_label = request()->has('isam') ? 'success' : 'default';
        $is_rd_label = request()->has('isrd') ? 'success' : 'default';
        $my_leads_label = request()->has('myleads') ? 'success' : 'default';
        $tasks_label = request()->has('tasks') ? 'success' : 'default';
        $is_ur_label = request()->has('isur') ? 'success' : 'default';
        $is_dg_label = request()->has('isdg') ? 'success' : 'default';
        $is_st_label = request()->has('isst') ? 'success' : 'default';
        $html = '<a href="/order_callrds?' . $my_leads . '" class="btn btn-' . $my_leads_label . ' btn-block mb-2">МОИ ЛИДЫ</a> ' . $html;
        $html .= ' <a href="/order_callrds?' . $tasks . '" class="btn btn-' . $tasks_label . ' btn-block mb-2">ПРОСРОЧЕННЫЕ ДЕЛА</a>';
        $html .= ' <a href="/order_callrds?' . $is_dg . '" class="btn btn-' . $is_dg_label . ' btn-block mb-2">ДИЗАЙНЕРЫ</a>';
        $html .= ' <a href="/order_callrds?' . $is_st . '" class="btn btn-' . $is_st_label . ' btn-block mb-2">СТРОИТЕЛИ</a>';
        $html .= ' <a href="/order_callrds?' . $is_ur . '" class="btn btn-' . $is_ur_label . ' btn-block mb-2">ЮР. ЛИЦА</a>';
//            $html .= ' <span class="badge bg-danger float-right">баллов: <b>'.$this->curScores().'</b></span>';
        $html .= ' <a href="/order_callrds?' . $is_am . '" class="btn btn-' . $is_am_label . ' btn-block mb-2">ЛИДЫ AM24</a>';
        $html .= ' <a href="/order_callrds?' . $is_rd . '" class="btn btn-' . $is_rd_label . ' btn-block mb-2">ЛИДЫ RD</a>';
        $html .= ' <a href="/api/leads/stats/calc_sum" class="btn btn-' . (session()->has('leads_stats_calc_sum') ? 'success' : 'danger') . '"><i class="fa fa-chart-bar"></i> Статистика</a>';
        return $html;
    }

    function customRules(\SleepingOwl\Admin\Form\Element\NamedFormElement $element, $model = null) {
        $status = $model->status ?? 'NEW';
        $order_status = $model->order_status ?? \App\Models\OrderStatus::where('slug','NEW')->first();
        $order_type = $model->call_type ?? \App\Models\OrderType::where('alias','call')->first();
        $hides = $order_status->hides ?? [];
        $reqs = $order_status->reqs ?? [];
        $checks = $order_status->checks ?? [];
        $type_checks = $order_type->checks ?? [];
        $checks = array_values(array_merge($checks, $type_checks));

        $classesString = 'custom_rule custom_'.$element->getName();

        if ($element->getName() == 'reject_reason' || $element->getName() == 'terms') {
            $classesString = 'custom2_'.$element->getName();
            if ($status != 'REJECT') {
                $classesString .= ' hidden';
            } else {
//                $element->required();
            }
        } else {
            $element->setValidationRules([]);
            if (in_array($element->getName(), $hides)) {
                $classesString .= ' hidden';
            }
            if (in_array($element->getName(), $reqs)) {
//                $element->required();
            }

            if (strpos($element->getName(),'checks->')!==false) {
                $id = str_replace('checks->','', $element->getName());
                $classesString = 'custom_checks custom_checks_'.$id;
                if (!in_array($id, $checks)) $classesString .= ' hidden';

                if (strpos($element->getName(),'checks->class_reason')!==false) {
                    $element->addValidationRule('required_if:class,0', 'Поле Причина оценки обязательно для заполнения при текущей оценке');
                }
            }
//            if (strpos($element->getName(),'checks->')!==false) {
//                $id = str_replace('checks->','', $element->getName());
//                $classesString = 'custom_checks custom_checks_'.$id;
//                if (!empty($model->checks[$id])) {
//                    $element->required();
//                    $classesString = '';
//                }
//                elseif (!in_array($id, $checks)) $classesString .= ' hidden';
//            }
        }
        $element->setHtmlAttribute('show', $classesString);
        return $element;
    }

}
