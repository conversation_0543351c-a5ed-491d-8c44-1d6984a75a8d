<?php

namespace Admin\Http\Sections\ADMIN;


use App\Models\AdminComment;
use App\Models\CatalogAdmin;
use App\Models\CatalogAdmin as Catalog;
use App\Models\Characteristic;
use App\Models\CharacteristicTypeList;
use App\Models\GoodsGroup;
use App\Models\Modset;
use App\Models\Note;
use App\Models\TypeButton;
use App\Models\Variant;
use App\View\Components\Helper;
use SleepingOwl\Admin\Form\Buttons\Save;
use SleepingOwl\Admin\Section;
use AdminDisplay;
use AdminForm;
use AdminFormElement;
use AdminSection;
use App\Models\CatalogRelatedLink;
use AdminColumn;

class Catalogs extends Section
{
    /**
     * @var bool
     */
    protected $checkAccess = false;

    /**
     * @var string
     */
    protected $title = 'Каталог';

    /**
     * @var string
     */
    protected $alias = 'catalog_admin';

    public function onDisplay()
    {
        ini_set('max_execution_time', 90000);
        ini_set('memory_limit', '1024M');
        if (request()->has('qwe')) {
            $display = AdminDisplay::tree();
            if(config('franchise.key') == 'ekb') {
               $display->setApply(function ($q){
                   $q->whereNotIn('id', getRDcatalog());
               });
            }
            $display->with('characteristics');
            $display->setValue(function ($model) {
                $title = "<a class='mr-2' style='color:black; font-weight: bold;' href='/catalog_admin/{$model->id}/edit'>{$model->title}</a>";
                $get_parent_char = !$model->children->count() && $model->parent != NULL ?
                    "<a class='mr-2' href='/get_parent_chars/{$model->id}' target='_blank'>Получить характеристики родителя</a>" : '';
                $is_all = $model->show_all ? '-all' : '';
                $url = "<a style='color:#f60; font-weight: bold;' class='mr-auto' target='_blank' href='https://alfamart24.ru/catalog/{$model->url}{$is_all}'>[ссылка]</a>";
                $master = $clear = '';
                if ($model->parent != NULL) {
                    $chars = 0;
                    if ($c = $model->characteristics->count()) $chars += $c;
                    $master = $model->children->count() ? '<a class="mr-2" href="/get_master_chars/' . $model->id . '" target="_blank"><b>[' . $chars . ']</b></a>' : '<span class="mr-2">[' . $chars . ']</span>';
                    $clear = $model->children->count() ? '<a class="mr-2 text-danger" href="/clear_goods_chars/' . $model->id . '" target="_blank"><b>[сбросить хар-ки]</b></a>' : '';
                }
                return "{$master} {$clear} {$title} {$get_parent_char} {$url}";
            })->addScript('catalog', asset('js/catalog.js'), ['admin-default']);
            return $display;
        }
        $display = AdminDisplay::tree();
        if(config('franchise.key') == 'ekb') {
            $display->setApply(function ($q){
                $q->whereNotIn('id', getRDcatalog());
            });
        }
        $display->with('notes','discounts','characteristics','buttons.call_type',/*'instruction',*/ 'relatedGroupsGoods','relatedGoods','goods_first','set','goods.set','settings','suppliers');

        $display->setValue(function ($model) {
//            $count = $instanse->total($instanse->id);
//            $count = ($count) ? "<span class='badge'>{$count}</span>" : '';
            $title = "<a class='mx-3' href='/catalog_admin/{$model->id}/edit'>{$model->title}</a>";
//            $seo = ($model->seo->first()) ? "<span class='badge bg-blue'>SEO</span>" : '';
            $buttons = '';
            if ($model->buttons->count()) {
                $buttons .= '<table class="table table-condensed">';
                foreach ($model->buttons->sortBy('order')->all() as $item) {
                    $pulse = $item->pulse?'-webkit-animation: pulsate 1.2s linear infinite;animation: pulsate 1.2s linear infinite;':'';
                    $button = '<a href="/type_buttons/'.$item->id.'/edit" target="_blank" class="btn btn-block" style="'.$pulse.'color:white;background-color:#2391E6;'.$item->style.'">'. $item->name . '</a>';
                    $type = "<i class=\"{$item->call_type->icon}\" aria-hidden=\"true\" title='{$item->call_type->name}'></i>";
                    $buttons .= '<tr><td>'.$button.'</td><td>'.$type.'</td></tr>';
                }
                $buttons .= '</table>';
                $buttons = '<a class="mr-3" tabindex="0" type="button" data-toggle="popover" data-trigger="hover focus"
                    data-placement="top" data-html="true" data-content="'.htmlspecialchars($buttons).'"><i class="fa fa-bars fa-lg text-red"></i></a>';
            }
            $note = ($model->notes->first()) ? "<span class='badge bg-green mr-3'>Примечание</span>" : '';

            $seo_title = '';
            if($model->seo_title) {
                $seo_title = "<span class='badge bg-blue'>S</span>";
            }
//            $discount = $model->discounts;
//            if ($discount) {
//                $discount = ($discount->active) ? "<span class='badge bg-green-active'>Скидка 10%</span>" : "";
//            }

//            $quiz = $model->quiz ? '<i class="fa fa-question-circle-o"></i>' : '';

//            if(count($instanse->children)) {
//                $volume = '<span class="badge '. $volume_color[$instanse->volume_info].'" data-toggle="tooltip" data-original-title="' . $volume_type[$instanse->volume_info] . '">' . mb_substr($volume_type[$instanse->volume_info],
//                        0, 1, 'UTF-8') . '</span>';
//            }
//            $instructions = '';
//            if ($model->instruction) {
//                $inst_label = $model->instruction->show ? 'bg-green' : 'bg-red';
//                $instructions = '<span class="badge '.$inst_label.' mr-3">Памятка</span>';
//            }

            $groups = '';
            if ($model->relatedGroupsGoods->count()) {
                $groups = '<span class="mr-3">🔗</span>';
            }

            if ($model->relatedGoods->count()) {
                $groups = '<span class="mr-3">🧲</span>';
            }
            $hideSearch = '';
            if($model->search_exclude) {
                $hideSearch = '<span class="mx-2" title="Скрыт в поиске">👻</span>';
            }
            $searchTitle = '';
            if($model->search_title) {
                $searchTitle = '<span class="mx-2" title="Название для поиска установлено">🔎</span>';
            }

            $related = $model->related_links()->count() ? '<span class="mr-1 ml-1" title="Теги в карточке поставлены"><i class="fa fa-cube"></i></span>' : '';

            $similar = $model->similar_characteristics ? '<span class="mr-1 ml-1" title="Похожие товары поставлены"><i class="fa fa-link"></i></span>' : '';

            $sets = '';
            if ($model->set) {
                $label = $model->set->show ? ($model->set->hide_complect?'text-warning':'text-success') : 'text-danger';
                $sets = '<span class="'.$label.' mr-3"><i class="fa fa-th-large"></i></span>';
            } elseif (!$model->children->count() && $model->goods->count()) {
                $s_goods = $model->goods->where('show', true);
                $c_sets = $s_goods->filter(fn($v) => optional($v->set)->show)->count();
                if ($c_sets > 0 && $c_sets < $s_goods->count()) $sets = '<code>'.$c_sets . '/' . $s_goods->count().'</code>&nbsp;';
                if ($c_sets) $sets .= '<span class="text-primary mr-3"><i class="fa fa-th-large"></i></span>';
            }


            $url = '<code class="ml-auto mr-5">'.$model->url.'</code>';

            //$wordstat = $model->show ? '<a href="https://direct.yandex.ru/registered/main.pl?checkboxes=1&cmd=wordstat&from_forecast=1&tm=&geo=0&&text='.$title.'" class="label label-warning" target="_blank">Wordstat</a>' : '';

            $is_checked = ''; $is_checked_info = ''; // htmlentities($model->is_checked_info);
//            $style = 'style="font-weight: bold; position: absolute; right: 70px; top: 10px;" data-html="true" tabindex="0" role="button" data-toggle="popover" data-trigger="focus" data-placement="left"';
//            switch ($model->is_checked) {
//                case '1': $is_checked = '<a '.$style.' data-content="' . $is_checked_info . '"><i class="fa fa-wrench text-danger"></i></a>'; break;
//                case '2': $is_checked = '<a '.$style.' data-content="' . $is_checked_info . '"><i class="fa dollar-sign text-success"></i></a>'; break;
//                case '3': $is_checked = '<a '.$style.' data-content="' . $is_checked_info . '"><i class="fa fa-gavel text-danger"></i></a>'; break;
//                case '4': $is_checked = '<a '.$style.' data-content="' . $is_checked_info . '"><i class="fa fa-thumbs-up text-success"></i></a>'; break;
//            }
            $is_checked = CatalogAdmin::CHECKS[$model->is_checked] ?? '';
            if ($is_checked) $is_checked = '<span class="ml-3 badge bg-primary">'.$is_checked.'</span>';
            $is_checked_date = $model->is_checked_date ? '<code class="ml-5">'.$model->is_checked_date->format('d.m.Y').'</code>' : '';

            $hide_in_menu = '<span class="text-'.($model->hide_in_menu?'danger':'success').'"><i class="fa fa-check"></i></span>';

            $empty = ($model->parent_id != null && !$model->children->count() && !$model->goods_first) ? '<span class="fa fa-exclamation-triangle text-danger mr-3"></span>' : '';

            $goods_shown = request()->has('count') ? $this->model->goods_info($model->id) : '';
            $d = '<div class="mr-auto"></div>';
            $show_vars = $model->show_var ? '<i class="fa fa-calculator mr-3"></i>' : '';
            $eshelon_sort = $model->eshelon_sort ? '<i class="fa fa-circle-nodes mr-3" title="Группировать по регионам"></i>' : '';
            $show_in_catalog = $model->show_in_catalog ? '<i class="fa fa-dungeon mr-3" title="Вывести под блок"></i>' : '';

            $sort = $model->default_sort;
            if ($sort) $sort = '<span class="mr-3 badge bg-'.\App\Models\CatalogAdmin::SORT_TYPE_COLOR[$sort].'"><i class="fa fa-sort-amount-down"></i> '.\App\Models\CatalogAdmin::SORT_TYPES[$sort].'</span>';
            $circle = null;
            if($model->groups->count()) {
                $circle = '<span>●</span>';
            }
            $slider = '';
            if($model->slider) {
                $slider = "<span class='mx-1' title='Слайдер включен'>🖼️</span>";
            }

            $suppliers = $model->suppliers;
            $suppliers = $suppliers ? '<div style="max-width:550px">'.$suppliers->map(fn($v) => '<a href="/suppliers/'.$v->id.'/edit" target="_blank" class="badge bg-secondary mr-2">'.$v->title.'</a>')->implode('').'</div>'  : '';

            return "{$hide_in_menu} {$title} $suppliers {$buttons} {$sets} {$note} {$goods_shown} {$eshelon_sort} {$show_in_catalog} {$show_vars} {$empty} {$groups} {$circle} {$sort} {$seo_title} {$is_checked_date} {$is_checked}  {$slider} {$related} {$similar} {$searchTitle} {$hideSearch} $d $url";
        });//->addScript('catalog', asset('js/catalog.js'), ['admin-default']);
        view()->composer(\AdminTemplate::getViewPath('_layout.inner'), function ($view) {
            $html = '<div class="form-group"><a href="/catalog_admin'.(request()->has('count') ? '':'?count=1').'" class="btn btn-'.(request()->has('count') ? 'success':'primary').'">Количество товаров</a></div>';
            $view->getFactory()->startPush('content.header', $html);
        });

        return $display;
    }


    /**
     * @param int $id
     *
     * @return FormInterface
     */
    public function onEdit($id)
    {
        $instance = Catalog::find($id);
        $form = AdminForm::form();

        $class = 'Catalog';//(new \ReflectionClass($this->getModel()))->getShortName();

        $chars_options = $id ? $instance->characteristics()->pluck('name', 'characteristic_id')->all() : [];

        $form->setElements([
            AdminFormElement::image('cover', 'Обложка')
                ->setSaveCallback(function ($file, $path, $filename, $settings) use ($instance) {
                    if (\Storage::disk('media')->exists('Catalog/cover/' . $instance->id . '/' . $instance->url . '.jpg')) {
                        \Storage::disk('media')->delete('Catalog/cover/'.$instance->id.'/' . $instance->url . '.jpg');
                    }
                    $file->storeAs('Catalog/cover/'.$instance->id.'/', $instance->url . '.jpg', 'media');
                    $path = env('CDN_URL') .'/Catalog/cover/'.$instance->id.'/'.$instance->url.'.jpg';
                    return ['path' => $path,'value' => $path];
                })->setValueSkipped(true),
            AdminFormElement::select('parent_id', 'Уровень '.renderComponent(new Helper('catalog_admin_parent_id')))->setModelForOptions(new Catalog())->setLoadOptionsQueryPreparer(fn($e,$q) => $q->with('parent'))->setDisplay('full_title')->nullable(),
//            AdminFormElement::text('old_id', 'Старый ID'),
            AdminFormElement::text('title', 'Название '.renderComponent(new Helper('catalog_admin_title')))->required(),
            AdminFormElement::text('seo_title', 'Seo название '.renderComponent(new Helper('catalog_admin_seo_title'))),
            AdminFormElement::text('search_title', 'Название для поиска '.renderComponent(new Helper('catalog_admin_search_title'))),
            AdminFormElement::text('url', 'URL')->setReadonly(true),

//            (!$instance->parent_id) ? AdminFormElement::checkbox('showcase', 'На витрину') : '',
            AdminFormElement::columns()
                ->addColumn([
                    AdminFormElement::checkbox('showcase', 'На витрину'),
                    AdminFormElement::checkbox('hide_in_menu', 'Скрыть в меню'),
                    AdminFormElement::checkbox('show', 'Показывать на сайте'),
                    AdminFormElement::checkbox('search_exclude', 'Скрыть в поиске'),
                    AdminFormElement::checkbox('show_all', 'Кнопка "Показать все товары"'),
                    AdminFormElement::checkbox('show_var', 'Показать плашки'),
                    AdminFormElement::checkbox('eshelon_sort', 'Группировать по регионам'),
                    AdminFormElement::checkbox('slider', 'Включить слайдер'),
                    AdminFormElement::checkbox('show_in_catalog', 'Вывести под блок'),
                ], 3)
                ->addColumn([
//                    AdminFormElement::text('price', 'Текст снизу в категориях'),
                    AdminFormElement::columns()
                        ->addColumn([AdminFormElement::select('var_w','Ширина', $chars_options)])
                        ->addColumn([AdminFormElement::select('var_h','Высота', $chars_options)])
                        ->addColumn([AdminFormElement::select('var_s','Открывание', $chars_options)])
                ]),
//            AdminFormElement::checkbox('relevant_price_date', 'Актуальная цена'),
            AdminFormElement::multiselect('buttons', 'Кнопки', TypeButton::class)->setDisplay('name')
                ->setSyncCallback(function ($value, $model) use ($id) {
                    $buttons = TypeButton::all()->pluck('id')->all();
                    $ids = \DB::table('catalog_type_button')
                        ->where('catalog_id', $id)
                        ->whereNotIn('type_button_id', $buttons)
                        ->pluck('type_button_id')->all();

                    $model->buttons()->sync(array_merge($ids,$value));
                }),
            AdminFormElement::hidden('is_topsales')->setDefaultValue(true)->setValueSkipped(true),
            AdminFormElement::multiselect('topsales','Товары',\App\Models\Goods::class)
                ->setDisplay(fn ($v) => "[{$v->id}] " . $v->name)
                ->setLoadOptionsQueryPreparer(function ($element, $query) use ($instance) {
                    return $query->whereIn('catalog_id', $instance->descendants->pluck('id')->all());
                }),
            AdminFormElement::columns()
                ->addColumn([
                    AdminFormElement::select('is_checked','Дата проверки', CatalogAdmin::CHECKS)
//                    AdminFormElement::radio('is_checked', 'Статус проверки', [
//                        null => 'Не указано',
//                        1 => '<i class="fa fa-wrench text-danger" style="font-weight: bold;"></i> Необходимо проработать объем',
//                        2 => '<i class="fa dollar-sign text-success" style="font-weight: bold;"></i> Объемы проработаны',
//                        3 => '<i class="fa fa-gavel text-danger" style="font-weight: bold;"></i> Необходимо проработать характеристики',
//                        4 => '<i class="fa fa-thumbs-up text-success" style="font-weight: bold;"></i> Характеристики проработаны ',
//                    ])->setSortable(false),
                ],4)
                ->addColumn([
                    AdminFormElement::date('is_checked_date','Проверено')
                ],4),
            AdminFormElement::columns()
                ->addColumn([
                    AdminFormElement::select('default_sort','Сортировка товаров по-умолчанию', \App\Models\CatalogAdmin::SORT_TYPES)
                ],4)
//                ->addColumn([AdminFormElement::textarea('is_checked_info', 'Комментарий')->setRows(4)])
        ]);

//        if (request()->has('gen')) {

//        Товары
        $goods = AdminSection::getModel(\App\Models\Goods::class)
            ->fireDisplay(['scopes' => ['withCatalog', $id]]);
        $goods->setParameter('catalog_id', $id);

//        Характеристики
        $characteristics1 = AdminSection::getModel(Characteristic::class)->fireDisplay(['scopes'=>['withCatalog', $id]]);
//        $characteristics1->getScopes()->push(['withCatalog', $id]);
        $characteristics1->setParameter('catalog_id', $id);

        $move_chars = [];
        $show_char_move = auth()->user()->hasRole('content') || auth()->user()->isAdmin();//in_array(auth()->id(),[59,10]);
        if ($show_char_move) {
            $move_chars[] = AdminFormElement::html('<div class="collapse" id="c_move">');
            foreach (CatalogAdmin::MOVE_CHARS as $char_key => $char_name) {
                $move_chars[] = AdminFormElement::multiselect('characteristics_move'.$char_key, $char_name)
                    ->setModelForOptions(new CharacteristicTypeList)
                    ->setLoadOptionsQueryPreparer(fn($e, $q) => $q->where('characteristic_id', $char_key))
                    ->setDisplay('name')->setValueSkipped(true);
            }
            $move_chars[] = AdminFormElement::html('</div>');
        }
        $characteristics = AdminForm::card();
        $characteristics->addBody([
            AdminFormElement::multiselect('characteristics', 'Характеристики')
                ->setModelForOptions(new Characteristic)
                ->setDisplay('name'),//->setView(view('multiselectnew')),
            AdminFormElement::html('<div class="d-flex"><a href="/get_parent_chars/'.$id.'">Получить характеристики родительской категории</a>'.
                '<a class="text-secondary ml-auto" role="button" data-toggle="collapse" data-target="#c_move">Служебное</a></div><hr>'),
            AdminFormElement::columns()->addColumn($move_chars),
            $characteristics1
        ]);

        $groups = AdminSection::getModel(GoodsGroup::class)->fireDisplay(['scopes'=>['withCatalog', $id]]);
        $groups->setParameter('catalog_id', $id);
        $groups->getScopes()->push(['withCatalog', $id]);

//        Варианты
        $variants = AdminSection::getModel(Variant::class)->fireDisplay();
        $variants->setParameter('catalog_id', $id);
        $variants->getScopes()->push(['withCatalog', $id]);

//        Маржа
//        $margin = AdminSection::getModel(Margin::class)->fireDisplay();
//        $margin->setParameter('catalog_id', $id);
//        $margin->getScopes()->push(['withCatalog', $id]);

//        Доставка
//        $delivery = AdminSection::getModel(Delivery::class)->fireDisplay();
//        $delivery->setParameter('catalog_id', $id);
//        $delivery->getScopes()->push(['withCatalog', $id]);

//        SEO
//        $seo = AdminSection::getModel(Seo::class)->fireDisplay();
//        $seo->setParameter('seosable_id', $id);
//        $seo->setParameter('seosable', $class);
//        $seo->getScopes()->push(['withCatalog', $id]);

//        Описание
//        $note = AdminSection::getModel(Note::class)->fireDisplay();
//        $note->setParameter('catalog_id', $id);
//        $note->getScopes()->push(['withCatalog', $id]);
        if ($instance->notes->first()) {
            $note = AdminSection::getModel(Note::class)->fireEdit($instance->notes->first()->id);
        } else {
            $note = AdminSection::getModel(Note::class)->fireCreate(['catalog_id' => $id]);
        }

//        Акции
//        $stock = AdminSection::getModel(Stock::class)->fireDisplay();
//        $stock->setParameter('stockable_id', $id);
//        $stock->getScopes()->push(['withCatalog', $id]);

//      Памятка менеджера
        /*if ($instruction = $instance->instruction) {
            $instructions = AdminSection::getModel(Instruction::class)->fireEdit($instruction->id);
        } else {
            $instructions = AdminSection::getModel(Instruction::class)->fireCreate(['catalog_id' => $id]);
//            $instructions = AdminSection::getModel(Instruction::class)->fireDisplay([$id]);                //$comments->getScopes()->push(['withCatalog', $id]);
//            $instructions->setParameter('catalog_id', $id);
        }*/

//        Комментарии
        if (!is_null($sid = $instance->comments()->first())) {
            $comments = AdminSection::getModel(AdminComment::class)->fireEdit($sid->id);

        } else {
//            $comments = AdminSection::getModel(AdminComment::class)->fireDisplay([$id]);
            $comments = AdminSection::getModel(AdminComment::class)->fireCreate(['commentable_id' => $id,'type' => $class]);
//            $comments->setParameter('commentable_id', $id);
//            $comments->setParameter('type', $class);
        }

       /* $mods = AdminSection::getModel(Module::class)
            ->fireDisplay(['scopes' => ['withCatalog', $id]]);
        $mods->getScopes()->push(['withCatalog', $id]);
        $mods->setParameter('type', $class);*/

        $upload = AdminForm::card();
//        $upload->setHtmlAttribute('enctype', 'multipart/form-data');
        $upload->addBody([
            AdminFormElement::image('baner', '')
                ->setSaveCallback(function ($file, $path, $filename, $settings) use ($instance) {
                    $file->storeAs('Catalog/menu/', $instance->url . '.jpg', 'media');
                    $path = env('CDN_URL') .'/Catalog/menu/'.$instance->url.'.jpg';
                return ['path' => $path,'value' => $path];
             }),
        ]);

        $relatedBody = AdminSection::getModel(CatalogRelatedLink::class)
            ->fireDisplay(['scopes' => ['withCatalog', $id]]);
        $relatedBody->setParameter('catalog_id', $id);

        $related = AdminForm::card();
        $related->addBody([
            $relatedBody
        ]);

        $related->getButtons()->setButtons([
            'save' => new Save(),
        ]);


//        $relatedGroupsGoods = AdminForm::card();
//        $relatedGroupsGoods->addBody([
//            AdminFormElement::hasMany('relatedGroupsGoods', [
//                AdminFormElement::text('name', 'Название группы'),
//                AdminFormElement::text('good_ids', 'Id товаров'),
//                AdminFormElement::checkbox('active', 'Активна')->setDefaultValue(true)
//            ])
//        ]);
//        $relatedGroupsGoods->getButtons()->setButtons([
//            'save' => new Save(),
//        ]);

//        $relatedGoods = AdminForm::card();
//        $relatedGoods->addBody([
//            AdminFormElement::hasMany('relatedGoods', [
//                AdminFormElement::text('name', 'Название группы'),
//                AdminFormElement::text('good_ids', 'Id товаров'),
//                AdminFormElement::checkbox('active', 'Активна')->setDefaultValue(true)
//            ])
//        ]);
//        $relatedGoods->getButtons()->setButtons([
//            'save' => new Save(),
//        ]);

        $uniChars = AdminForm::card();
        $char = Catalog::find($id)->characteristics;
        $char = $char->pluck('name','id')->toArray();
        $uniChars->addBody([
        AdminFormElement::hasManyLocal('similar_characteristics', [
            AdminFormElement::columns()
                ->addColumn([
                    AdminFormElement::select('id', 'Характеристики')
                        ->setOptions($char)
                ])
        ])->saveAsArray(),
        ]);

//        $quiz = AdminForm::panel();
//        $quiz->addBody([
//            AdminFormElement::hidden('quiz.catalog_id')->setDefaultValue($id),
//            AdminFormElement::text('quiz.quiz_id', 'ID'),
//            AdminFormElement::text('quiz.quiz_popup_id', 'ID всплывающего'),
//            AdminFormElement::text('quiz.button_text', 'Текст на кнопке')->setDefaultValue('ПОЛУЧИТЬ СКИДКУ 10%'),
//            AdminFormElement::checkbox('quiz.active', 'Активно')->setDefaultValue(true),
//        ]);

        $set = $instance->set;
        if (!$set) $current_set = AdminSection::getModel(Modset::class)->fireCreate(['catalog_id' => $id]);
        else $current_set = AdminSection::getModel(Modset::class)->fireEdit($set->id);

        $tabs = AdminDisplay::tabbed();
        $tabs->appendTab($form, 'Основные');
        $tabs->appendTab($note, 'Описание');

        $tabs->appendTab($goods, 'Товары');
        $tabs->appendTab($current_set, 'Комплект')->setHtmlAttribute('style','background-color: #4099FC; color: white');
        $tabs->appendTab($characteristics, 'Характеристики');
        $tabs->appendTab($groups, 'ЧПУ характеристики (Группы)');
//        $tabs->appendTab($related, 'Похожие/Сопутствующие');
        $tabs->appendTab($related, 'Ссылки в карточке');
//        $tabs->appendTab($relatedGroupsGoods, 'Сопутствующие группы товаров');
        $tabs->appendTab($uniChars, 'Объединяющие характеристики');
//        $tabs->appendTab($relatedGoods, 'С этим товаром покупают');
//        $tabs->appendTab($quiz, 'Квиз');
//        $tabs->appendTab($variants, 'Варианты');
//        $tabs->appendTab($margin, 'Маржа');
//        $tabs->appendTab($delivery, 'Доставка');
//        $tabs->appendTab($seo, 'Seo');
//        $tabs->appendTab($stock, 'Акции');
//        $tabs->appendTab($instructions, 'Памятка менеджера');
        $tabs->appendTab($comments, 'Комментарии');
        if ($id && is_null($instance->parent_id)) $tabs->appendTab($upload, 'Банер для меню');
        /*$tabs->appendTab($mods, 'Комплектующие')->setBadge(function() use ($id) {
            return Module::
                join('catalog_module', 'catalog_module.module_id', '=', 'modules.id')
            ->where('catalog_module.catalog_id', $id)->count();
        });*/

        view()->composer(\AdminTemplate::getViewPath('_partials.header'), function ($view) use ($id) {
            $html = '<a href="/pricing/catalog/'.$id.'" class="btn btn-success ml-2"><i class="fa fa-check"></i> Цены проверены</a>';
            $view->getFactory()->startPush('navbar.buttons.after', $html);
        });

        return $tabs;
    }

    public function onCreate()
    {
        return AdminForm::form()->setElements([
            AdminFormElement::select('parent_id', 'Уровень')->setModelForOptions(new Catalog())->setDisplay('full_title'),
            AdminFormElement::text('title', 'Название')->required(),
            AdminFormElement::checkbox('show', 'Показывать на сайте')->setDefaultValue(true),
        ]);
    }

    public function isDeletable(\Illuminate\Database\Eloquent\Model $model)
    {
        return auth()->id() == 2 || auth()->user()->isSuperAdmin();
    }

    /**
     */
    public function onDelete($id)
    {
        // todo: remove if unused
    }

    /**
     */
    public function onRestore($id)
    {
        // todo: remove if unused
    }
}
