<?php

use App\Models\Catalog;
use Carbon\Carbon;

Route::resource('comments', \Admin\Http\Controllers\CommentController::class);

Route::get('send_invoice/{id}', 'Admin\Http\Controllers\DocController@sendInvoice');
Route::get('send_supplier/{id}', 'Admin\Http\Controllers\DocController@sendSupplier');

Route::post('cart/items/delete.json', ['as' => 'admin.cart.delete', 'uses' => 'Admin\Http\Controllers\DocController@cartDeleteItem']);

//Route::get('/school/{any?}', 'App\Http\Controllers\SpaController@index')->where('any', '.*');
// Школа

Route::prefix('school')->middleware(['auth'])->namespace('Admin\Http\Controllers')->group(function () {
    Route::get('', 'PageController@index')->name('school');
    Route::get('{rubric_id}', 'PageController@rubric')->name('school.rubric');
    Route::get('{rubric_id}/{page_id}', 'PageController@page')->name('school.page');
    Route::get('{rubric_id}/{page_id}/pdf', 'PageController@pagePDF')->name('school.pdf');

    Route::post('upload', function (){
        $fileName = Storage::disk('School')->putFile('/',request()->file('upload'));

        $result['url'] = asset(
            config('alfamart.CDN_url').'/School/'.$fileName
        );
        $result['uploaded'] = 1;
        $result['fileName'] = $fileName;

        return $result;
    });
});



Route::prefix('schoolrd')->middleware(['auth'])->namespace('Admin\Http\Controllers')->group(function () {
    Route::get('', 'PageControllerrd@index')->name('rd.school');
    Route::get('{rubric_id}', 'PageControllerrd@rubric')->name('rd.school.rubric');
    Route::get('{rubric_id}/{page_id}', 'PageControllerrd@page')->name('rd.school.page');
    Route::get('{rubric_id}/{page_id}/pdf', 'PageControllerrd@pagePDF')->name('rd.school.pdf');

    Route::post('upload', function (){
        $fileName = Storage::disk('SchoolRD')->putFile('/',request()->file('upload'));

        $result['url'] = asset(
            config('alfamart.CDN_url').'/SchoolRD/'.$fileName
        );
        $result['uploaded'] = 1;
        $result['fileName'] = $fileName;

        return $result;
    });
});

Route::post('upload/{place}', function ($place){
    switch ($place) {
        case 'goods':
            $fileName = Storage::disk('media')->putFile('/uploads/products/desc',request()->file('upload'));
            $result['url'] = asset(config('alfamart.CDN_url').'/'.$fileName);
            break;
        case 'seos':
            $fileName = Storage::disk('media')->putFile('/uploads/seos',request()->file('upload'));
            $result['url'] = asset(config('alfamart.CDN_url').'/'.$fileName);
            break;
        default:
            return false;
    }

    $result['uploaded'] = 1;
    $result['fileName'] = $fileName;

    return $result;
});

// Календарь
Route::get('/calendar/', ['as' => 'calendar', 'uses' => 'Admin\Http\Controllers\CalendarController@index']);

// График работ
Route::get('/work_days/index', ['as' => 'admin.work_days_index', 'uses' => 'Admin\Http\Controllers\WorkDaysCalendarController@index']);
Route::get('/work_days/calendar', ['as' => 'admin.work_days_calendar', 'uses' => 'Admin\Http\Controllers\WorkDaysCalendarController@calendar']);
Route::post('/work_days/calendar/data', ['as' => 'admin.work_days_calendar_data', 'uses' => 'Admin\Http\Controllers\WorkDaysCalendarController@calendar_data']);
Route::post('/work_days/calendar/departments', ['as' => 'admin.work_days_calendar_departments', 'uses' => 'Admin\Http\Controllers\WorkDaysCalendarController@departments']);
Route::post('/work_days/calendar/users', ['as' => 'admin.work_days_calendar_users', 'uses' => 'Admin\Http\Controllers\WorkDaysCalendarController@users']);
Route::post('/work_days/calendar/events', ['as' => 'admin.work_days_calendar_events', 'uses' => 'Admin\Http\Controllers\WorkDaysCalendarController@events']);
Route::post('/work_days/calendar/update', ['as' => 'admin.work_days_calendar_update', 'uses' => 'Admin\Http\Controllers\WorkDaysCalendarController@update']);
Route::get('/work_days/work', ['as' => 'admin.work_days_work', 'uses' => 'Admin\Http\Controllers\WorkDaysCalendarController@work']);

// Календарь нагрузки логистов
Route::get('/workload', ['as' => 'admin.workload', 'uses' => 'Admin\Http\Controllers\WorkloadCalendarController@index']);
Route::get('/workload/calendar', ['as' => 'admin.workload_calendar', 'uses' => 'Admin\Http\Controllers\WorkloadCalendarController@calendar']);
Route::post('/workload/calendar/data', ['as' => 'admin.workload_calendar_data', 'uses' => 'Admin\Http\Controllers\WorkloadCalendarController@calendar_data']);

// Календарь нагрузки продажников
Route::get('/workload_leads', ['as' => 'admin.workload', 'uses' => 'Admin\Http\Controllers\WorkloadCalendarController@index_leads']);
Route::get('/workload_leads/calendar', ['as' => 'admin.workload_calendar', 'uses' => 'Admin\Http\Controllers\WorkloadCalendarController@calendar_leads']);
Route::post('/workload_leads/calendar/data', ['as' => 'admin.workload_calendar_data', 'uses' => 'Admin\Http\Controllers\WorkloadCalendarController@calendar_data_leads']);

// Календарь нагрузки контента
Route::get('/workload_content', ['as' => 'admin.workload', 'uses' => 'Admin\Http\Controllers\WorkloadCalendarController@index_content']);
Route::get('/workload_content/calendar', ['as' => 'admin.workload_calendar', 'uses' => 'Admin\Http\Controllers\WorkloadCalendarController@calendar_content']);
Route::post('/workload_content/calendar/data', ['as' => 'admin.workload_calendar_data', 'uses' => 'Admin\Http\Controllers\WorkloadCalendarController@calendar_data_content']);


//order_schedule
Route::get('/order_schedule', ['as' => 'admin.order_schedule_index', 'uses' => 'Admin\Http\Controllers\Order_ScheduleController@index']);
Route::get('/order_schedule/calendar', ['as' => 'admin.order_schedule_calendar', 'uses' => 'Admin\Http\Controllers\Order_ScheduleController@calendar']);
Route::post('/order_schedule/calendar/data', ['as' => 'admin.order_schedule_data', 'uses' => 'Admin\Http\Controllers\Order_ScheduleController@calendar_data']);

// Календарь трат
Route::get('/expenses/index', ['as' => 'admin.expenses_index', 'uses' => 'Admin\Http\Controllers\ExpenseCalendarController@index']);
Route::get('/expenses/calendar', ['as' => 'expenses_calendar', 'uses' => 'Admin\Http\Controllers\ExpenseCalendarController@calendar']);
Route::post('/expenses/calendar/data', ['as' => 'admin.calendar_data', 'uses' => 'Admin\Http\Controllers\ExpenseCalendarController@calendar_data']);
Route::get('/expenses/suggestion', ['as' => 'admin.calendar_suggestion', 'uses' => 'Admin\Http\Controllers\ExpenseCalendarController@suggestions']);

// Календарь работ
Route::get('/workcalendar/index', ['as' => 'admin.workcalendar.index', 'uses' => 'Admin\Http\Controllers\WorkCalendarController@index']);
Route::get('/workcalendar/calendar', ['as' => 'work.workcalendar.calendar', 'uses' => 'Admin\Http\Controllers\WorkCalendarController@calendar']);
Route::post('/workcalendar/calendar/data', ['as' => 'admin.workcalendar.data', 'uses' => 'Admin\Http\Controllers\WorkCalendarController@calendar_data']);


Route::get('/clients/suggestion', ['as' => 'admin.client_suggestion', 'uses' => 'Admin\Http\Controllers\ClientController@suggestions']);
Route::get('/clients/info', ['as' => 'admin.client_get', 'uses' => 'Admin\Http\Controllers\ClientController@info']);
Route::get('/action/suggestion', ['as' => 'admin.action_suggestion', 'uses' => 'Admin\Http\Controllers\ClientController@actionsuggestions']);

Route::get('/candidates/suggestion', ['as' => 'admin.candidates_suggestion', 'uses' => 'Admin\Http\Controllers\ClientController@candidate_suggestions']);

Route::get('/arbitr', function () {
    return Redirect::away("https://kad.arbitr.ru/");
});

Route::get('/fssprus', function () {
    return Redirect::away("http://fssprus.ru/iss/ip");
});

if (env('RESCUE_URL', '')) {
    Route::get('/' . env('RESCUE_URL', ''), function () {
        return redirect('/');
    });
}

Route::get('send_report/logist', 'Admin\Http\Controllers\DocController@sendReportLogist');
Route::get('send_report/sales', 'Admin\Http\Controllers\DocController@sendReportSales');

Route::get('/calcs/bank', 'Admin\Http\Controllers\CalcController@index')->name('admin.calcs.bank');
Route::post('/calcs/send', 'Admin\Http\Controllers\CalcController@send')->name('admin.calcs.send');

Route::get('/calc/custom_rules/{status}/{type?}', 'Admin\Http\Controllers\CalcController@customRules');
Route::get('/calc/custom_rules_reason/{reason}', 'Admin\Http\Controllers\CalcController@customRulesReason');

Route::get('/carts/get_cart_item', 'Admin\Http\Controllers\DocController@cartGetItem');

Route::get('/calc/refresh_brands', 'Admin\Http\Controllers\DocController@refreshBrands');

Route::post('char/{id}', 'Admin\Http\Controllers\DocController@getChar')->name('char');

Route::post('/transfer', 'App\Http\Controllers\TransferController@run')->middleware(['cors']);
Route::get('/transfer_chars', 'App\Http\Controllers\TransferController@chars');

Route::post('/dublicatemargins/{id}', 'App\Http\Controllers\DuplicateMargins@index');
Route::post('/dublicategoods/{id}', 'App\Http\Controllers\DuplicateGoods@index');
Route::post('/dublicatestocks/{id}', 'App\Http\Controllers\DuplicateStocks@index');
Route::post('/dublicateseos/{id}', 'App\Http\Controllers\DuplicateSeos@index');
Route::post('/dublicatemod/{id}', 'App\Http\Controllers\DuplicateGoods@module');
Route::post('/dublicatesef/{id}', 'App\Http\Controllers\DuplicateFriendlyUrl@index');
Route::post('/dublicateseflinks/{id}', 'App\Http\Controllers\DuplicateFriendlyUrlLinks@index');
Route::post('/dublicatecataloglinks/{id}', 'App\Http\Controllers\DuplicateCatalogLinks@index');
Route::get('/get_parent_chars/{id}', 'App\Http\Controllers\DuplicateGoods@getParentChars');
Route::get('/get_master_chars/{id}', 'App\Http\Controllers\DuplicateGoods@getMasterChars');
Route::get('/clear_goods_chars/{id}', 'App\Http\Controllers\DuplicateGoods@clearGoodsChars');

// RD
Route::post('rd/dublicatesef/{id}', 'App\Http\Controllers\RD\DuplicateFriendlyUrl@index');
Route::post('rd/dublicateseflinks/{id}', 'App\Http\Controllers\RD\DuplicateFriendlyUrlLinks@index');
Route::post('rd/dublicatecataloglinks/{id}', 'App\Http\Controllers\RD\DuplicateCatalogLinks@index');

Route::get('get_counts', 'Admin\Http\Controllers\CountsController@getCounts');

Route::get('im', 'Admin\Http\Controllers\ChatsController@index');
Route::get('im/users', 'Admin\Http\Controllers\ChatsController@fetchUsers');
Route::get('im/rooms', 'Admin\Http\Controllers\ChatsController@fetchRooms');
Route::get('im/messages', 'Admin\Http\Controllers\ChatsController@fetchMessages');
Route::get('im/messages/unread', 'Admin\Http\Controllers\ChatsController@fetchUnreadMessages');
Route::post('im/messages/read', 'Admin\Http\Controllers\ChatsController@readMessage');
Route::post('im/messages', 'Admin\Http\Controllers\ChatsController@sendMessage');

Route::get('chat', 'Admin\Http\Controllers\ChatsController@indexChat');
Route::get('chat/messages', 'Admin\Http\Controllers\ChatsController@fetchChatMessages');
//Route::get('chat/download', 'Admin\Http\Controllers\ChatsController@dowloadChatFile');
Route::get('chat/messages/unread', 'Admin\Http\Controllers\ChatsController@fetchChatUnreadMessages');
Route::post('chat/send', 'Admin\Http\Controllers\ChatsController@sendChatMessage');
Route::post('chat/read', 'Admin\Http\Controllers\ChatsController@readChatMessage');
Route::post('chat/delete', 'Admin\Http\Controllers\ChatsController@deleteChatMessage');

Route::get('sup_reports', function () {
    return \AdminSection::view('<h2>Раздел находится в разработке...</h2>', null);
});
Route::get('catalog_region_reports', function () {
    $catalog_id = request()->get('catalog_id') ?? null;
    $date = request()->get('date') ?? null;
    $datetime = AdminColumnFilter::daterangex()->setPlaceholder('Дата')->setHtmlAttributes(['name' => 'date', 'value' => $date]);
    $catalogs = AdminFormElement::select('catalog_id')
        ->setModelForOptions(\App\Models\Catalog::class, 'full_title')
        ->setLoadOptionsQueryPreparer(fn($e, $q) => $q->orderBy('_lft'))
        ->setSortable(false)
        ->setHtmlAttribute('placeholder', 'Выберите раздел');
    $regions = \App\Models\Geo\Region::query()->orderBy('name')
        ->when(config('franchise.key') != 'crm', fn($q) => $q->whereIn('id', config('franchise.geo_regions')))
        ->get();

    if ($cats = App\Models\Catalog::query()->descendantsAndSelf($catalog_id)) {
        $cats_ids = $cats->pluck('id')->all();
        $catalog = $cats->find($catalog_id);
        $goods_id = \App\Models\Goods::query()->whereIn('catalog_id', $cats_ids)->pluck('id')->all();
        $ids = \App\Models\Cart::query()->whereIn('catalog_id', $cats_ids)->pluck('order_call_id')->all();
        $ids_goods = \App\Models\Cart::query()->where('item', \App\Models\Goods::class)->whereIn('item_id', $goods_id)->pluck('order_call_id')->all();
        $ids = array_unique(array_merge($ids, $ids_goods));
        $dates = ($date) ? explode(' - ', $date) : [];
        $leads = \App\Models\OrderCall::withDrafts()->with('geo_cities')->whereIn('id', $ids)
            ->when($date, fn($q) => $q->whereDate('created_at', '>=', Carbon\Carbon::parse($dates[0])->toDateString())
                ->whereDate('created_at', '<=', Carbon\Carbon::parse($dates[1])->toDateString()))
            ->whereNull('parent_id')->whereNull('black_list')->whereNull('deleted_at')->get(['id', 'city', 'created_at']);
        $orders = \DB::table('order_carts')->whereIn('lead_id', $leads->pluck('id')->all())->whereNull('deleted_at')->get(['id', 'lead_id']);
    }

    return \AdminSection::view(view('admin::reports.catalog_region',
        compact('datetime', 'catalogs', 'regions', 'catalog', 'date', 'leads', 'orders')), null);
});
Route::get('region_catalog_reports', function () {
    $region_id = request()->get('region_id') ?? null;
    $date = request()->get('date') ?? null;
    $is_children = request()->get('children') ?? null;
    $datetime = AdminColumnFilter::daterangex()->setPlaceholder('Дата')->setHtmlAttributes(['name' => 'date', 'value' => $date]);
    $regions = AdminFormElement::select('region_id')
        ->setModelForOptions(\App\Models\Geo\Region::class, 'name')
        ->setLoadOptionsQueryPreparer(fn($e, $q) => $q->when(config('franchise.key') != 'crm', fn($q) => $q->whereIn('id', config('franchise.geo_regions')))->orderBy('name'))
        ->setSortable(false)
        ->setHtmlAttribute('placeholder', 'Выберите регион');
    $children = AdminFormElement::checkbox('children', 'Показывать детей');
    $catalogs = $leads = $orders = null;
    if ($region = \App\Models\Geo\Region::query()->find($region_id)) {
        $catalogs = !\Cache::has("reports.catalogs")
            ? \Cache::remember("reports.catalogs", now()->addDay(), fn() => \App\Models\Catalog::withDepth()->with('descendants')->get()->toTree())
            : \Cache::get("reports.catalogs");
        $dates = ($date) ? explode(' - ', $date) : [];
        $leads = \Cache::remember('region_catalog_reports.leads:' . $region_id, now()->addDay(), function () use ($region) {
            return \App\Models\OrderCall::withDrafts()->whereIn('city', $region->geo_cities->pluck('name')->all())
                ->whereNull('parent_id')->whereNull('black_list')->whereNull('deleted_at')->get(['id', 'city', 'created_at']);
        });

        $leads = $leads->when($date,
            fn($q) => $q->where('created_at', '>=', Carbon\Carbon::parse($dates[0])->startOfDay()->toDateTimeString())
                ->where('created_at', '<=', Carbon\Carbon::parse($dates[1])->endOfDay()->toDateTimeString()));
        $leads->load(['cart' => fn($q) => $q->with('goods')->where('item', \App\Models\Goods::class)]);
        $leads = $leads->map(function ($lead) {
            $item = $lead->cart->sortBy('is_module')->first();
            if (!$item) return false;
            $catalog_id = $item->catalog_id;
            if (!$catalog_id && $item->item_id) $catalog_id = optional($item->goods)->catalog_id;
            if (!$catalog_id) return false;
            $lead->catalog_id = $catalog_id;
            return $lead;
        })->filter();
        $orders = \DB::table('order_carts')->whereIn('lead_id', $leads->pluck('id')->all())->whereNull('deleted_at')->get(['id', 'lead_id']);
    }

    return \AdminSection::view(view('admin::reports.region_catalog',
        compact('datetime', 'catalogs', 'regions', 'children', 'region', 'date', 'is_children', 'leads', 'orders')), null);
});

Route::get('sales_report', \App\Http\Controllers\SalesReportController::class);

Route::get('supplier_region_reports', function () {
    $regions = \App\Models\Geo\Region::query()->orderBy('name')
//        ->when(config('franchise.key') != 'crm', fn($q)=>$q->whereIn('id', config('franchise.geo_regions')))
        ->get();

    $catalogs = !\Cache::has("reports.catalogs")
        ? \Cache::remember("reports.catalogs", now()->addDay(), fn() => \App\Models\Catalog::withDepth()->with('descendants')->get()->toTree())
        : \Cache::get("reports.catalogs");

    $suppliers = \App\Models\Supplier::with('catalogs', 'supplier_city')
        ->where('status', '<>', 'NOTWORK')->get()
        ->groupBy('supplier_city.region_id')
        ->map(function ($suppliers) {
            $cats = [];
            foreach ($suppliers as $supplier) {
                foreach ($supplier->catalogs as $catalog) {
                    if (!isset($cats[$catalog->id])) $cats[$catalog->id] = 0;
                    $cats[$catalog->id]++;
                }
            }
            return $cats;
        })->all();

    return \AdminSection::view(view('admin::reports.supplier_region', compact('regions', 'catalogs', 'suppliers')));
});
Route::get('supplier_region_reportsrd', function () {
    $regions = \App\Models\Geo\Region::query()
        ->orderBy('name')
        ->get();
    \Cache::forget('reports.catalogsrd');
    $catalogs = !\Cache::has("reports.catalogsrd")
        ? \Cache::remember("reports.catalogsrd", now()->addDay(), function () {
            $cat = collect([1, 2])->map(function ($v) {
                return Catalog::descendantsAndSelf($v)->pluck('id');
            })->flatten();
            return \App\Models\Catalog::query()
                ->whereNotIn('id', $cat)
                ->withDepth()
                ->with('descendants')
                ->get()
                ->toTree();
        })
        : \Cache::get("reports.catalogsrd");

    $suppliers = \App\Models\Supplier::with('catalogs', 'supplier_city')
        ->where('status', '<>', 'NOTWORK')->get()
        ->groupBy('supplier_city.region_id')
        ->map(function ($suppliers) {
            $cats = [];
            foreach ($suppliers as $supplier) {
                foreach ($supplier->catalogs as $catalog) {
                    if (!isset($cats[$catalog->id])) $cats[$catalog->id] = 0;
                    $cats[$catalog->id]++;
                }
            }
            return $cats;
        })->all();

    return \AdminSection::view(view('admin::reports.supplier_region', compact('regions', 'catalogs', 'suppliers')));
});

Route::get('general_region_reports', function () {
    $leads_all = \Cache::remember('testweb.leads', now()->addDay(), function () {
        return \App\Models\OrderCall::withDrafts()
            ->select(['id', 'city', 'status'])
            ->whereNull('parent_id')
            ->whereNull('black_list')
            ->whereNull('deleted_at')
            ->orderBy('city')
            ->get()->toArray();
    });

    $regions = \App\Models\Geo\Region::with('geo_cities')->orderBy('name')->get();
    $html = '<table cellspacing="2" border="1" cellpadding="5" collapsed style="margin-bottom:5em;">';
    $html .= '<tr>';
    $html .= '<td>Название</td>';
    $html .= '<td>Лиды</td>';
    $html .= '<td>Заказы</td>';
    $html .= '<td>Оборот</td>';
    $html .= '<td>Конверсия</td>';
    $html .= '<td>Средний чек</td>';
    $html .= '<td>Сумма обращений</td>';
    $html .= '</tr>';
    foreach ($regions as $region) {
        $general = $region->geo_cities->where('is_capital', 1)->first();
        $cities = $region->geo_cities->where('is_capital', 0)->pluck('name')->all();
        if ($general) {
            $leads = \Cache::remember('stats.' . $region->id . '.general.leads', now()->addHour(), fn() => \Arr::where($leads_all, fn($item) => $item['city'] == $general->name));
            $orders = \Cache::remember('stats.' . $region->id . '.general.orders', now()->addHour(), fn() => \Arr::where($leads, fn($item) => $item['status'] == 'TRANSFER'));
            $total = \Cache::remember('stats.' . $region->id . '.general.total', now()->addHour(), function () use ($leads) {
                return \App\Models\Cart::whereIn('order_call_id', \Arr::pluck($leads, 'id'))->get()->map(fn($v) => $v->price * $v->quantity)->sum();
            });
            $total_orders = \Cache::remember('stats.' . $region->id . '.general.total_orders', now()->addHour(), function () use ($orders) {
                return \App\Models\Cart::whereIn('order_call_id', \Arr::pluck($orders, 'id'))->get()->map(fn($v) => $v->price * $v->quantity)->sum();
            });
            $conversion = count($leads) ? number_format(100 * (count($orders) / count($leads)), 2, ',', '') . '%' : '0%';
            $average = count($orders) ? $total_orders / count($orders) : 0;

            $html .= '<tr>';
            $html .= '<th align="left"><b>' . $general->name . '<b></td>';
            $html .= '<td align="right">' . count($leads) . '</td>';
            $html .= '<td align="right">' . count($orders) . '</td>';
            $html .= '<td align="right">' . number_format($total_orders, 0, ',', '') . '</td>';
            $html .= '<td align="right">' . $conversion . '</td>';
            $html .= '<td align="right">' . number_format($average, 0, ',', '') . '</td>';
            $html .= '<td align="right">' . number_format($total, 0, ',', '') . '</td>';
            $html .= '</tr>';
        }

        $leads = \Cache::remember('stats.' . $region->id . '.leads', now()->addHour(), fn() => \Arr::where($leads_all, fn($item) => in_array($item['city'], $cities)));
        $orders = \Cache::remember('stats.' . $region->id . '.orders', now()->addHour(), fn() => \Arr::where($leads, fn($item) => $item['status'] == 'TRANSFER'));
        $total = \Cache::remember('stats.' . $region->id . '.total', now()->addHour(), function () use ($leads) {
            return \App\Models\Cart::whereIn('order_call_id', \Arr::pluck($leads, 'id'))->get()->map(fn($v) => $v->price * $v->quantity)->sum();
        });
        $total_orders = \Cache::remember('stats.' . $region->id . '.total_orders', now()->addHour(), function () use ($orders) {
            return \App\Models\Cart::whereIn('order_call_id', \Arr::pluck($orders, 'id'))->get()->map(fn($v) => $v->price * $v->quantity)->sum();
        });
        $conversion = count($leads) ? number_format(100 * (count($orders) / count($leads)), 2, ',', '') . '%' : '0%';
        $average = count($orders) ? $total_orders / count($orders) : 0;

        $html .= '<tr>';
        $html .= '<td>' . $region->name . '</td>';
        $html .= '<td align="right">' . count($leads) . '</td>';
        $html .= '<td align="right">' . count($orders) . '</td>';
        $html .= '<td align="right">' . number_format($total_orders, 0, ',', '') . '</td>';
        $html .= '<td align="right">' . $conversion . '</td>';
        $html .= '<td align="right">' . number_format($average, 0, ',', '') . '</td>';
        $html .= '<td align="right">' . number_format($total, 0, ',', '') . '</td>';
        $html .= '</tr>';

        $html .= '<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td><td></td></tr>';
    }
    $html .= '</table><style>
table, th, td {
  border: 1px solid black;
  border-collapse: collapse;
}
</style>';

    return \AdminSection::view(view('admin::reports.general_region', compact('html')));
});

Route::get('leads_region_reports/{class?}', function ($class = null) {
    if ($class && !in_array($class, ['A', 'B', 'C', 'D'])) exit;

    $key = $class ?: 'AB';
    if (!\Cache::has("report.catalog_by_class.{$key}")) {
        $cats = \Cache::remember("report.catalog_by_class.{$key}", now()->addDay(), function () use ($key) {
            $c = $key == 'AB' ? ['A', 'B'] : [$key];
            return Catalog::select('id', 'class')->with('children:id,parent_id')->whereIn('class', $c)->get()
                ->map(function ($v) {
                    return array_merge([$v->id], $v->children->pluck('id')->toArray());
                })->flatten()->unique()->toArray();
        });
    } else {
        $cats = \Cache::get("report.catalog_by_class.{$key}");
    }

    $leads_all = \Cache::remember("testweb.leads_year.{$key}", now()->addDay(),
        fn() => \App\Models\OrderCall::withDrafts()
            ->select(['id', 'city', 'status', 'created_at'])
            ->whereIn('catalog_id', $cats)
            ->whereNull('parent_id')
            ->whereNull('black_list')
            ->whereNull('deleted_at')
            ->orderBy('city')
            ->get()->toArray());
    $years = collect([2025, 2024, 2023, 2022, 2021, 2020, 2019, 2018, 2017]);
    $regions = \App\Models\Geo\Region::with('geo_cities')->orderBy('name')->get();
    $html = '<table cellspacing="2" border="1" cellpadding="5" collapsed class="print" style="margin-bottom:5em;">';
    $html .= '<tr>';
    $html .= '<td>Регион</td>';
    $html .= '<td>Лиды</td>';
    $html .= '<td>Заказы</td>';
    $html .= '<td>Оборот</td>';
    $html .= '<td>Конверсия</td>';
    $html .= '<td>Средний чек</td>';
    $html .= '<td>Сумма обращений</td>';
    if ($key == 'AB') {
        $html .= '<td></td>';
    }
    $html .= '</tr>';
    foreach ($regions as $region) {
        $cities = $region->geo_cities->pluck('name')->all();

        $leads = \Arr::where($leads_all, fn($item) => in_array($item['city'], $cities));
        $orders = \Arr::where($leads, fn($item) => $item['status'] == 'TRANSFER');
        $carts = \Cache::remember("stats.{$region->id}.carts.{$key}", now()->addHour(), fn() => \App\Models\Cart::select(['id', 'order_call_id', 'price', 'quantity'])->whereIn('order_call_id', \Arr::pluck($leads, 'id'))->get()->toArray());
        $carts_orders = \Cache::remember("stats.{$region->id}.carts_orders.{$key}", now()->addHour(), fn() => \App\Models\Cart::select(['id', 'order_call_id', 'price', 'quantity'])->whereIn('order_call_id', \Arr::pluck($orders, 'id'))->get()->toArray());
        $total = \Cache::remember("stats.{$region->id}.total.{$key}", now()->addHour(), fn() => collect($carts)->map(fn($v) => $v['price'] * $v['quantity'])->sum());
        $total_orders = \Cache::remember("stats.{$region->id}.total_orders.{$key}", now()->addHour(), fn() => collect($carts_orders)->map(fn($v) => $v['price'] * $v['quantity'])->sum());
        $conversion = count($leads) ? 100 * (count($orders) / count($leads)) : 0;
        $average = count($orders) ? $total_orders / count($orders) : 0;
        $conversion_label = '';
        if ($key == 'AB' && $conversion > 0) {
            if ($conversion < 6) $conversion_label = 0;
            elseif ($conversion < 8) $conversion_label = 1;
            else $conversion_label = 2;
            \Cache::remember("stats.{$region->id}.store_conversiob.{$key}", now()->addDay(),
                fn() => \DB::table('geo_regions')->where('id', $region->id)->update(['conversion' => $conversion_label])
            );
            $conversion_label = '<span class="badge bg-' . \App\Models\Geo\Region::LABELS[$conversion_label] . '">' . \App\Models\Geo\Region::CONVERSIONS[$conversion_label] . '</span>';
        }

        $html .= '<tr>';
        $html .= '<td><b>' . $region->name . '</b></td>';
        $html .= '<td align="right">' . count($leads) . '</td>';
        $html .= '<td align="right">' . count($orders) . '</td>';
        $html .= '<td align="right">' . number_format($total_orders, 0, ',', ' ') . '</td>';
        $html .= '<td align="right">' . number_format($conversion, 2, ',', ' ') . '%</td>';
        $html .= '<td align="right">' . number_format($average, 0, ',', ' ') . '</td>';
        $html .= '<td align="right">' . number_format($total, 0, ',', ' ') . '</td>';
        if ($key == 'AB') {
            $html .= '<td class="text-nowrap">' . $conversion_label . '</td>';
        }
        $html .= '</tr>';

        foreach ($years as $year) {
            $leads_year = \Arr::where($leads, fn($item) => date('Y', strtotime($item['created_at'])) == $year);
            $orders_year = \Arr::where($leads_year, fn($item) => $item['status'] == 'TRANSFER');
            $carts_year = \Arr::where($carts, fn($item) => in_array($item['order_call_id'], \Arr::pluck($leads_year, 'id')));
            $carts_orders_year = \Arr::where($carts_orders, fn($item) => in_array($item['order_call_id'], \Arr::pluck($orders_year, 'id')));
            $total_year = collect($carts_year)->map(fn($v) => $v['price'] * $v['quantity'])->sum();;
            $total_orders_year = collect($carts_orders_year)->map(fn($v) => $v['price'] * $v['quantity'])->sum();
            $conversion_year = count($leads_year) ? 100 * (count($orders_year) / count($leads_year)) : 0;
            $average_year = count($orders_year) ? $total_orders / count($orders_year) : 0;
            $conversion_label = '';
            if ($key == 'AB' && $conversion_year > 0) {
                if ($conversion < 6) $conversion_label = 0;
                elseif ($conversion < 8) $conversion_label = 1;
                else $conversion_label = 2;
                $conversion_label = '<span class="badge bg-' . \App\Models\Geo\Region::LABELS[$conversion_label] . '">' . \App\Models\Geo\Region::CONVERSIONS[$conversion_label] . '</span>';
            }

            $html .= '<tr>';
            $html .= '<td>' . $year . '</td>';
            $html .= '<td align="right">' . count($leads_year) . '</td>';
            $html .= '<td align="right">' . count($orders_year) . '</td>';
            $html .= '<td align="right">' . number_format($total_orders_year, 0, ',', ' ') . '</td>';
            $html .= '<td align="right">' . number_format($conversion_year, 2, ',', ' ') . '%</td>';
            $html .= '<td align="right">' . number_format($average_year, 0, ',', ' ') . '</td>';
            $html .= '<td align="right">' . number_format($total_year, 0, ',', ' ') . '</td>';
            if ($key == 'AB') {
                $html .= '<td class="text-nowrap">' . $conversion_label . '</td>';
            }
            $html .= '</tr>';
        }

        $html .= '<tr><td colspan="' . ($key == 'AB' ? 8 : 7) . '">&nbsp;</td></tr>';
    }
    $html .= '</table><style>

table, th, td {
  border: 1px solid black;
  border-collapse: collapse;
}
</style>';
    view()->composer(\AdminTemplate::getViewPath('_partials.header'), function ($view) {
        $html = '<button class="btn btn-secondary" onclick="javascript:window.print()"><i class="fas fa-print"></i> Печать</button>';
        $view->getFactory()->startPush('navbar.buttons.after', $html);
    });
//    return view('admin::reports.general_region', compact('html'));
    config(['sleeping_owl.breadcrumbs' => false]);
    return \AdminSection::view(view('admin::reports.general_region', compact('html')));
});

Route::get('active_domains', function () {
    $cities = \App\Models\Geo\City::orderBy('name')->get();
    $html = '<table cellspacing="2" border="1" cellpadding="5" collapsed style="margin-bottom:5em;">';
    $html .= '<tr>';
    $html .= '<td>Город</td>';
    $html .= '<td>Домен</td>';
    $html .= '</tr>';
    foreach ($cities as $city) {
        $html .= '<tr>';
        $html .= '<td>' . $city->name . '</td>';
        $html .= '<td align="right">' . $city->subdomain . '</td>';
        $html .= '</tr>';
    }
    $html .= '</table><style>
        table, th, td {
          border: 1px solid black;
          border-collapse: collapse;
        }
    </style>';

    return view('admin::reports.general_region', compact('html'));
});

Route::get('hr_statistics_day', function () {
    $data = [];
    $responded = \App\Models\Candidate::with(['audits' => fn($q) => $q->whereJsonLength('new_values->status', '>', 0)])->whereIn('type', [0, 1, 3])->whereBetween('created_at', [
        Carbon::today()->startOfDay()->toDateTimeString(),
        Carbon::today()->endOfDay()->toDateTimeString()
    ])->get();
    $added = \App\Models\Candidate::with(['audits' => fn($q) => $q->whereJsonLength('new_values->status', '>', 0)])->whereIn('type', [2])->whereBetween('created_at', [
        Carbon::today()->startOfDay()->toDateTimeString(),
        Carbon::today()->endOfDay()->toDateTimeString()
    ])->get();
    $rejected = \App\Models\Candidate::with('candidate_reason')->where('status', 'reject')->whereNotNull('type')->whereBetween('created_at', [
        Carbon::today()->startOfDay()->toDateTimeString(),
        Carbon::today()->endOfDay()->toDateTimeString()
    ])->get();
    $vacancies = \App\Models\Vacancy::query()->where('status', 'open')->get();
//    return $this->audits()->whereJsonContains('new_values',['status'=>'REJECT'])->oldest();

    $data['responded_by_status'] = $responded->countBy('status')->all();
    $data['responded_by_source'] = $responded->countBy('source')->all();
    $data['added_by_status'] = $added->countBy('status')->all();
    $data['added_by_source'] = $added->countBy('source')->all();
    $data['statuses'] = \App\Models\CandidateStatus::query()->orderBy('order')->pluck('name', 'slug')->all();

    $data['responded_funnel'] = [
        'Новый лид (Отклики+звонки)' => $responded->count(),
        'Собеседование назначено' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview')->count())->count(),
        'Собеседование проведено' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview2')->count())->count(),
        'Оффер (стаж.назначена)' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship2')->count())->count(),
        'Стажировка проходит' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship')->count())->count(),
        'Вакансия успешно закрыта (30 раб. дней отработки)' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'work')->count())->count(),
        'Испытательный срок пройден (прошло 3 мес)' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'work2')->count())->count(),
    ];

    $data['added_funnel'] = [
        'Новый лид' => $added->count(),
        'Отправлено предложение' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'offersend')->count())->count(),
        'Собеседование назначено' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview')->count())->count(),
        'Собеседование проведено' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview2')->count())->count(),
        'Оффер (стаж.назначена)' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship2')->count())->count(),
        'Стажировка проходит' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship')->count())->count(),
        'Вакансия успешно закрыта (10 раб. дней отработки)' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'work')->count())->count(),
        'Испытательный срок пройден (прошло 3 мес)' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'work2')->count())->count(),
    ];

    return AdminSection::view(view('admin::statistics.hr-day', $data));
});

Route::get('hr_statistics/{year?}/{current?}', function ($current_year = null, $current = null) {
    if (is_null($current) && is_null($current_year)) $current = now()->month;
    if (is_null($current_year)) $current_year = now()->year;
    config(['sleeping_owl.breadcrumbs' => false]);

    $data = [];

    $data['months'] = ['Январь', 'Февраль', 'Март', 'Апрель', 'Май', 'Июнь', 'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь'];
    $data['years'] = [2024, 2025];
    $data['current'] = $current;
    $data['current_year'] = $current_year;

    $responded = \App\Models\Candidate::with(['audits' => fn($q) => $q->whereJsonLength('new_values->status', '>', 0)])->whereIn('type', [0, 1, 3])->when($current, fn($q) => $q->whereMonth('created_at', $current))->whereYear('created_at', $current_year)->get();
    $added = \App\Models\Candidate::with(['audits' => fn($q) => $q->whereJsonLength('new_values->status', '>', 0)])->whereIn('type', [2])->when($current, fn($q) => $q->whereMonth('created_at', $current))->whereYear('created_at', $current_year)->get();
    $rejected = \App\Models\Candidate::with('candidate_reason')->where('status', 'reject')->whereNotNull('type')->when($current, fn($q) => $q->whereMonth('created_at', $current))->whereYear('created_at', $current_year)->get();
    $vacancies = \App\Models\Vacancy::query()->where('status', 'open')->get();
//    return $this->audits()->whereJsonContains('new_values',['status'=>'REJECT'])->oldest();

    $data['responded_by_status'] = $responded->countBy('status')->all();
    $data['responded_by_source'] = $responded->countBy('source')->all();
    $data['added_by_status'] = $added->countBy('status')->all();
    $data['added_by_source'] = $added->countBy('source')->all();
    $data['statuses'] = \App\Models\CandidateStatus::query()->orderBy('order')->pluck('name', 'slug')->all();

    $data['responded_funnel'] = [
        'Новый лид' => $responded->count(),
        'Собеседование назначено' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview')->count())->count(),
        'Собеседование проведено' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview2')->count())->count(),
        'Оффер (стаж.назначена)' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship2')->count())->count(),
        'Стажировка проходит' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship')->count())->count(),
        'Вакансия успешно закрыта (30 раб. дней отработки)' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'work')->count())->count(),
        'Испытательный срок пройден (прошло 3 мес)' => $responded->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'work2')->count())->count(),
    ];

    $data['added_funnel'] = [
        'Новый лид' => $added->count(),
        'Отправлено предложение' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'offersend')->count())->count(),
        'Собеседование назначено' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview')->count())->count(),
        'Собеседование проведено' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview2')->count())->count(),
        'Оффер (стаж.назначена)' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship2')->count())->count(),
        'Стажировка проходит' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship')->count())->count(),
        'Вакансия успешно закрыта (10 раб. дней отработки)' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'work')->count())->count(),
        'Испытательный срок пройден (прошло 3 мес)' => $added->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'work2')->count())->count(),
    ];

    $data['responded_sources'] = $responded->groupBy('source')->map(fn($v) => [
        $v->count(), //Лиды
        $v->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview')->count())->count(), //Собеседование назначено
        $v->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview2')->count())->count(), //Собеседование проведено
        $v->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship2')->count())->count(), //Оффер (стажировка назначена)
        $v->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship')->count())->count(), //Стажировка проходит
    ]);

    $data['added_sources'] = $added->groupBy('source')->map(fn($v) => [
        $v->count(), //Лиды
        $v->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview')->count())->count(), //Собеседование назначено
        $v->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'interview2')->count())->count(), //Собеседование проведено
        $v->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship2')->count())->count(), //Оффер (стажировка назначена)
        $v->filter(fn($q) => $q->audits->filter(fn($v) => $v->new_values['status'] == 'internship')->count())->count(), //Стажировка проходит
    ]);

    $data['reasons'] = $rejected->each(fn($v) => $v->reject_reason = $v->candidate_reason->title ?? '[Неизвестная причина]')->countBy('reject_reason')->sortByDesc(fn($v) => $v);

    return AdminSection::view(view('admin::statistics.hr', $data));
})->name('admin.hr_statistics');

Route::get('cart_reports', function () {
    $cats = \App\Models\Catalog::whereIn('parent_id', [89, 1571])->pluck('id')->all();
    $cats = array_merge($cats, [89, 1571]);
    $leads = \App\Models\OrderCall::with(['cart', 'catalog'])->whereIn('catalog_id', $cats)->where('franchisee', 'ekb')
        ->where('deleted_at', null)->whereNull('black_list')->whereNull('parent_id')->whereBetween('created_at', ['2022-01-01 00:00:00', '2023-12-31 23:59:59'])->get();

    $orders = $leads->where('status', 'TRANSFER')->map(function ($lead) {
        $carts = $lead->cart->sum(fn($v) => $v->quantity * $v->price);
        return [
            $lead->created_at->format('d.m.Y'),
            $lead->catalog->full_title2,
            $carts
        ];
    });
    $html = '<table cellspacing="2" border="1" cellpadding="5" collapsed style="margin-bottom:5em;">';
    $html .= '<tr><th colspan="3" style="text-align:center">Оплаченные по ШК</th></tr>';
    $html .= '<tr>';
    $html .= '<th>Дата заказа</th>';
    $html .= '<th>Тип товара</th>';
    $html .= '<th>Сумма заказа</th>';
    $html .= '</tr>';
    foreach ($orders as $lead) {
        $html .= '<tr>';
        $html .= '<td>' . $lead[0] . '</td>';
        $html .= '<td>' . $lead[1] . '</td>';
        $html .= '<td>' . $lead[2] . '</td>';
        $html .= '</tr>';
    }
    $html .= '</table>';

    $cats2 = \App\Models\Catalog::whereIn('parent_id', [21, 916, 1044, 1045, 1046, 1047, 1049, 1454, 1631, 1768, 1796, 1900, 1959, 2186])->pluck('id')->all();
    $cats2 = array_merge($cats2, [2139], [21, 916, 1044, 1045, 1046, 1047, 1049, 1454, 1631, 1768, 1796, 1900, 1959, 2186]);
    $leads2 = \App\Models\OrderCall::with(['cart', 'catalog'])->whereIn('catalog_id', $cats2)->where('franchisee', 'ekb')
        ->where('deleted_at', null)->whereNull('black_list')->whereNull('parent_id')->whereBetween('created_at', ['2022-01-01 00:00:00', '2023-12-31 23:59:59'])->get();
    $orders2 = $leads2->where('status', 'TRANSFER')->map(function ($lead) {
        $carts = $lead->cart->sum(fn($v) => $v->quantity * $v->price);
        return [
            $lead->created_at->format('d.m.Y'),
            $lead->catalog->full_title2,
            $carts
        ];
    });
    $html .= '<table cellspacing="2" border="1" cellpadding="5" collapsed style="margin-bottom:5em;">';
    $html .= '<tr><th colspan="3" style="text-align:center">Оплаченные по межкомнатным</th></tr>';
    $html .= '<tr>';
    $html .= '<th>Дата заказа</th>';
    $html .= '<th>Тип товара</th>';
    $html .= '<th>Сумма заказа</th>';
    $html .= '</tr>';
    foreach ($orders2 as $lead) {
        $html .= '<tr>';
        $html .= '<td>' . $lead[0] . '</td>';
        $html .= '<td>' . $lead[1] . '</td>';
        $html .= '<td>' . $lead[2] . '</td>';
        $html .= '</tr>';
    }
    $html .= '</table>';

    $html .= '<table cellspacing="2" border="1" cellpadding="5" collapsed style="margin-bottom:5em;">';
    $html .= '<tr><th colspan="3" style="text-align:center">Конверсия по ШК</th></tr>';
    $html .= '<tr>';
    $html .= '<th>Месяц и год</th>';
    $html .= '<th>Конверсия</th>';
    $html .= '</tr>';
    $month_list = array_map(fn($month) => \Carbon\Carbon::create(null, $month)->translatedFormat('F'), range(1, 12));
    foreach ([2022, 2023] as $year) {
        foreach ($month_list as $k => $month) {
            $m = $k + 1;
            $yLeads = $leads->filter(fn($v) => $v->created_at->month == $m && $v->created_at->year == $year);
            $yOrders = $yLeads->where('status', 'TRANSFER');
            $conversion = count($yLeads) ? number_format(100 * (count($yOrders) / count($yLeads)), 2, ',', '') . '%' : '0%';
            $html .= '<tr>';
            $html .= '<td>' . $month . ' ' . $year . '</td>';
            $html .= '<td>' . $conversion . '</td>';
            $html .= '</tr>';
        }
    }
    $html .= '</table>';

    $html .= '<table cellspacing="2" border="1" cellpadding="5" collapsed style="margin-bottom:5em;">';
    $html .= '<tr><th colspan="3" style="text-align:center">Конверсия по межкомнатным</th></tr>';
    $html .= '<tr>';
    $html .= '<th>Месяц и год</th>';
    $html .= '<th>Конверсия</th>';
    $html .= '</tr>';
    foreach ([2022, 2023] as $year) {
        foreach ($month_list as $k => $month) {
            $m = $k + 1;
            $yLeads = $leads2->filter(fn($v) => $v->created_at->month == $m && $v->created_at->year == $year);
            $yOrders = $yLeads->where('status', 'TRANSFER');
            $conversion = count($yLeads) ? number_format(100 * (count($yOrders) / count($yLeads)), 2, ',', '') . '%' : '0%';
            $html .= '<tr>';
            $html .= '<td>' . $month . ' ' . $year . '</td>';
            $html .= '<td>' . $conversion . '</td>';
            $html .= '</tr>';
        }
    }
    $html .= '</table><br>';

    $html .= '<style>
        * {font-size:.98em;}
        table {float:left; margin-left:1em;}
        table, th, td {
          border: 1px solid black;
          border-collapse: collapse;
          text-align: right;
        }
    </style>';

    return $html;
});

Route::get('auto-purchase-percent', function () {
    return AdminSection::view(view('admin::auto-purchase-percent'), 'Автоматический % на закуп');
})->name('admin.auto-purchase-percent');

Route::get('auto-percent', App\Http\Livewire\AutoPurchasePercent::class)->name('auto-percent');

Route::get('asdzxc/{year?}', function ($year = null) {
    $statuses = \App\Models\OrderStatus::pluck('name', 'slug')->all();
    $types = \App\Models\OrderType::pluck('name', 'alias')->all();

    echo '<style>
            * {font-size:.98em;}
            table {float:left; margin-left:1em;}
            table, th, td {
              border: 1px solid black;
              border-collapse: collapse;
              text-align: right;
            }
        </style><table cellspacing="2" border="1" cellpadding="5" collapsed style="margin-bottom:5em;">
        <tr>
            <th>№ лида</th>
            <th>Дата заявки</th>
            <th>Тип лида</th>
            <th>Телефон</th>
            <th>Whatsapp</th>
            <th>Телеграмм</th>
            <th>Пол</th>
            <th>Тип клиента</th>
            <th>Имя в лиде / Имя клиент</th>
            <th>Город</th>
            <th>Область</th>
            <th>Товар</th>
            <th>Категория</th>
            <th>Сумма</th>
            <th>Способ оплаты</th>
            <th>Статус</th>
            <th>Причина отказа</th>
        </tr>';

    \App\Models\RidersDrive\OrderCallrd::query()
        ->when($year, fn($q) => $q->whereYear('created_at', $year))
        ->whereNull('deleted_at')->whereNull('black_list')->whereNull('parent_id')
        ->chunk(200, function ($orders) use ($statuses, $types) {
            $orders->load('client', 'cart', 'catalog.parent', 'geo_cities.region');
            $rows = [];
            foreach ($orders as $order) {
                $name = trim($order->name);
                $client = $order->client;
                $client_name = trim($client?->fio);
                if ($order->client && !empty($client_name) && $client_name != $name) {
                    $name .= ' (' .$client_name . ')';
                }
                $cart = $order->cart;
                $paid_amount = $cart->map(function ($value) {
                    return $value->quantity * $value->price;
                })->sum();
                $phones = [$order->phone, $order->phone2, $order->phone3, $order->phone4];
                $phones = array_filter($phones);

                $rows[] = [
                    $order->id,
                    $order->created_at->format('d.m.Y'),
                    $types[$order->type] ?? $order->type,
                    implode(', ', $phones),
                    $order->whatsapp,
                    $order->telegram,
                    ($client->sex ?? ''),
                    ($client ? \App\Models\Client::TYPES[$client->type] : ''),
                    $name,
                    $order->city,
                    ($order->geo_cities ? $order->geo_cities->region->name : ''),
                    ($cart->first()->name ?? ''),
                    ($order->catalog?->full_title ?? ''),
                    $paid_amount,
                    \App\Models\OrderCall::PAY_TYPES[$order->pay_type] ?? '',
                    ($statuses[$order->status] ?? $order->status),
                    ($order->reason->title ?? ''),
                ];
            }
            foreach ($rows as $row) {
                echo '<tr>';
                foreach ($row as $k => $v) {
                    echo '<td>' . $v . '</td>';
                }
                echo '</tr>';
            }
        });

    echo '</table><br>';
    return '';
});

Route::get('dfsfasd/{year?}', function ($year = null) {
    $statuses = \App\Models\OrderStatus::pluck('name', 'slug')->all();
    $types = \App\Models\OrderType::pluck('name', 'alias')->all();

    echo '<style>
            * {font-size:.98em;}
            table {float:left; margin-left:1em;}
            table, th, td {
              border: 1px solid black;
              border-collapse: collapse;
              text-align: right;
            }
        </style><table cellspacing="2" border="1" cellpadding="5" collapsed style="margin-bottom:5em;">
        <tr>
            <th>№ лида</th>
            <th>Дата заявки</th>
            <th>Тип лида</th>
            <th>Телефон</th>
            <th>Whatsapp</th>
            <th>Телеграмм</th>
            <th>Пол</th>
            <th>Тип клиента</th>
            <th>Квалификация</th>
            <th>Имя в лиде / Имя клиент</th>
            <th>Город</th>
            <th>Область</th>
            <th>Товар</th>
            <th>Категория</th>
            <th>Сумма</th>
            <th>Способ оплаты</th>
            <th>Статус</th>
            <th>Причина отказа</th>
            <th>Ремонт</th>
            <th>Был на встрече</th>
            <th>Замеры проведены</th>
            <th>Повторное обращение</th>
        </tr>';

    $rd = \Cache::remember('catalog_by_class.D', now()->addDay(), function () {
        $rd = \App\Models\Catalog::descendantsAndSelf(1229)->pluck('id')->toArray();
        return array_merge(\App\Models\Catalog::descendantsAndSelf(1567)->pluck('id')->toArray(), $rd);
    });

    \App\Models\OrderCall::query()
        ->when($year, fn($q) => $q->whereYear('created_at', $year))
        ->where(fn($q) => $q->whereNull('catalog_id')->orWhereNotIn('catalog_id', $rd))->where(fn ($q) => $q->whereNull('url')->orWhere('url', 'not like', '%ridersdrive%'))
        ->whereNull('deleted_at')->whereNull('black_list')->whereNull('parent_id')
        ->chunk(200, function ($orders) use ($statuses, $types, $rd) {
            $orders->load('client', 'cart', 'catalog.parent', 'geo_cities.region');
            $rows = [];
            foreach ($orders as $order) {
                $name = trim($order->name);
                $client = $order->client;
                $client_name = trim($client?->fio);
                if ($order->client && !empty($client_name) && $client_name != $name) {
                    $name .= ' (' .$client_name . ')';
                }
                $cart = $order->cart;
                $item = $cart->first();

                if ($item && in_array($item->catalog_id, $rd)) continue;

                $paid_amount = $cart->map(function ($value) {
                    return $value->quantity * $value->price;
                })->sum();
                $phones = [$order->phone, $order->phone2, $order->phone3, $order->phone4];
                $phones = array_filter($phones);

                $rows[] = [
                    $order->id,
                    $order->created_at->format('d.m.Y'),
                    $types[$order->type] ?? $order->type,
                    implode(', ', $phones),
                    $order->whatsapp,
                    $order->telegram,
                    ($client->sex ?? ''),
                    ($client ? \App\Models\Client::TYPES[$client->type] : ''),
                    (\App\Models\OrderCall::CLIENT_TYPES[$order->client_type] ?? $order->client_type),
                    $name,
                    $order->city,
                    ($order->geo_cities ? $order->geo_cities->region->name : ''),
                    ($item->name ?? ''),
                    ($order->catalog?->full_title ?? ''),
                    $paid_amount,
                    \App\Models\OrderCall::PAY_TYPES[$order->pay_type] ?? '',
                    ($statuses[$order->status] ?? $order->status),
                    ($order->reason->title ?? ''),
                    ($order->is_repair ? 'Да' : 'Нет'),
                    ($order->is_meeting ? 'Да' : 'Нет'),
                    ($order->is_measurements ? 'Да' : 'Нет'),
                    ($order->is_repeat ? 'Да' : 'Нет'),
                ];
            }
            foreach ($rows as $row) {
                echo '<tr>';
                foreach ($row as $k => $v) {
                    echo '<td>' . $v . '</td>';
                }
                echo '</tr>';
            }
        });

    echo '</table><br>';
    return '';
});
