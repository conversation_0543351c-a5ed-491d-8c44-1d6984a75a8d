const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.js('resources/js/app.js', 'public/js');
mix.js('resources/js/school.js', 'public/js');
mix.scripts(['resources/js/crm.js'], 'public/js/crm.js');
mix.js('resources/js/components.js', 'public/js').sourceMaps();
mix.js('resources/js/components_clean.js', 'public/js').sourceMaps();
mix.sass('resources/sass/app.scss', 'public/css');
mix.sass('resources/sass/crm.scss', 'public/css');


if (mix.inProduction()) {
    mix.version();
}
